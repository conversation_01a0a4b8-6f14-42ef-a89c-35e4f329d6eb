@extends('layouts.admin')

@section('title', 'Chỉnh Sửa Bài H<PERSON>c - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Chỉnh Sửa Bài H<PERSON>c</h4>
        <p class="text-muted mb-0">{{ $lesson->title }}</p>
      </div>
      <div>
        <a href="{{ route('admin.lessons.index', ['chapter_id' => $lesson->chapter_id]) }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<form method="POST" action="{{ route('admin.lessons.update', $lesson->id) }}" enctype="multipart/form-data">
  @csrf
  @method('PUT')
  <div class="row">
    <div class="col-lg-8">
      <!-- Thông Tin Bài Học -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Thông Tin Bài Học
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-12 mb-3">
              <label for="chapter_id" class="form-label">Chương <span class="text-danger">*</span></label>
              <select class="form-select @error('chapter_id') is-invalid @enderror" id="chapter_id" name="chapter_id" required>
                <option value="">Chọn Chương</option>
                @foreach($chapters as $chapterOption)
                <option value="{{ $chapterOption->id }}" {{ old('chapter_id', $lesson->chapter_id) == $chapterOption->id ? 'selected' : '' }}>
                  {{ $chapterOption->course->title }} - {{ $chapterOption->title }}
                </option>
                @endforeach
              </select>
              @error('chapter_id')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-12 mb-3">
              <label for="title" class="form-label">Tiêu Đề Bài Học <span class="text-danger">*</span></label>
              <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title', $lesson->title) }}" required>
              @error('title')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-md-6 mb-3">
              <label for="type" class="form-label">Loại Bài Học <span class="text-danger">*</span></label>
              <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required onchange="toggleContentFields()">
                <option value="">Chọn Loại</option>
                <option value="video" {{ old('type', $lesson->type) == 'video' ? 'selected' : '' }}>Video</option>
                <option value="text" {{ old('type', $lesson->type) == 'text' ? 'selected' : '' }}>Văn Bản</option>
                <option value="pdf" {{ old('type', $lesson->type) == 'pdf' ? 'selected' : '' }}>PDF</option>
                <option value="quiz" {{ old('type', $lesson->type) == 'quiz' ? 'selected' : '' }}>Quiz</option>
              </select>
              @error('type')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-md-6 mb-3">
              <label for="duration_minutes" class="form-label">Thời Lượng (Phút) <span class="text-danger">*</span></label>
              <input type="number" class="form-control @error('duration_minutes') is-invalid @enderror" id="duration_minutes" name="duration_minutes" value="{{ old('duration_minutes', $lesson->duration_minutes) }}" min="1" required>
              @error('duration_minutes')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <!-- Video URL Field -->
            <div class="col-12 mb-3" id="video_url_field" style="display: none;">
              <label for="video_url" class="form-label">URL Video</label>
              <input type="url" class="form-control @error('video_url') is-invalid @enderror" id="video_url" name="video_url" value="{{ old('video_url', $lesson->video_url) }}" placeholder="https://www.youtube.com/watch?v=...">
              @error('video_url')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
              <div class="form-text">Nhập URL YouTube hoặc video khác</div>
            </div>
            
            <!-- Content Field -->
            <div class="col-12 mb-3" id="content_field" style="display: none;">
              <label for="content" class="form-label">Nội Dung</label>
              <textarea class="form-control @error('content') is-invalid @enderror" id="content" name="content" rows="6" placeholder="Nhập Nội Dung Bài Học">{{ old('content', $lesson->content) }}</textarea>
              @error('content')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <!-- File Upload Field -->
            <div class="col-12 mb-3" id="file_field" style="display: none;">
              <label for="file_path" class="form-label">File Tài Liệu</label>
              @if($lesson->file_path)
              <div class="mb-2">
                <a href="{{ asset('storage/' . $lesson->file_path) }}" target="_blank" class="btn btn-outline-primary btn-sm">
                  <i class="fa fa-download me-1"></i>Tải File Hiện Tại
                </a>
                <small class="text-muted d-block">{{ basename($lesson->file_path) }}</small>
              </div>
              @endif
              <input type="file" class="form-control @error('file_path') is-invalid @enderror" id="file_path" name="file_path" accept=".pdf,.doc,.docx">
              @error('file_path')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
              <div class="form-text">Chọn File Mới Để Thay Thế (PDF, DOC, DOCX) - Tối Đa 10MB</div>
            </div>
            
            <!-- Exercise Files Upload Field -->
            <div class="col-12 mb-3">
              <label for="exercise_files" class="form-label">File Bài Tập (Tùy Chọn)</label>

              @if($lesson->exercise_files && count($lesson->exercise_files) > 0)
              <div class="mb-3">
                <h6>File Hiện Tại:</h6>
                @foreach($lesson->exercise_files as $index => $file)
                <div class="d-flex align-items-center justify-content-between border rounded p-2 mb-2">
                  <div>
                    <a href="{{ asset('storage/' . $file['path']) }}" target="_blank" class="btn btn-outline-success btn-sm">
                      <i class="fa fa-download me-1"></i>{{ $file['name'] }}
                    </a>
                    <small class="text-muted d-block">{{ number_format($file['size'] / 1024, 1) }} KB</small>
                  </div>
                  <button type="button" class="btn btn-outline-danger btn-sm delete-existing-file" data-index="{{ $index }}">
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
                @endforeach
              </div>
              @endif

              <div id="exercise-files-container">
                <div class="exercise-file-input mb-2">
                  <div class="input-group">
                    <input type="file" class="form-control @error('exercise_files.*') is-invalid @enderror" name="exercise_files[]" accept=".pdf,.doc,.docx,.zip,.rar">
                    <button type="button" class="btn btn-outline-danger remove-file-btn" style="display: none;">
                      <i class="fa fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
              <button type="button" class="btn btn-outline-primary btn-sm" id="add-exercise-file">
                <i class="fa fa-plus me-1"></i>Thêm File Bài Tập
              </button>
              @error('exercise_files.*')
              <div class="invalid-feedback d-block">{{ $message }}</div>
              @enderror
              <div class="form-text">Thêm File Bài Tập Mới (PDF, DOC, DOCX, ZIP, RAR) - Tối Đa 20MB Mỗi File</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <!-- Cài Đặt -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Cài Đặt
          </h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label for="position" class="form-label">Vị Trí <span class="text-danger">*</span></label>
            <input type="number" class="form-control @error('position') is-invalid @enderror" id="position" name="position" value="{{ old('position', $lesson->position) }}" min="1" required>
            @error('position')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Thứ Tự Hiển Thị Của Bài Học</div>
          </div>
          
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="is_preview" name="is_preview" value="1" {{ old('is_preview', $lesson->is_preview) ? 'checked' : '' }}>
              <label class="form-check-label" for="is_preview">
                Cho Phép Preview
              </label>
            </div>
            <div class="form-text">Người dùng có thể xem miễn phí</div>
          </div>
          
          <div class="mb-3">
            <label for="is_active" class="form-label">Trạng Thái</label>
            <select class="form-select" id="is_active" name="is_active">
              <option value="1" {{ old('is_active', $lesson->is_active) == '1' ? 'selected' : '' }}>Hoạt Động</option>
              <option value="0" {{ old('is_active', $lesson->is_active) == '0' ? 'selected' : '' }}>Ẩn</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Thông Tin -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Thông Tin
          </h5>
        </div>
        <div class="card-body">
          <div class="mb-2">
            <small class="text-muted">ID:</small>
            <div class="fw-bold">{{ $lesson->id }}</div>
          </div>
          <div class="mb-2">
            <small class="text-muted">Ngày Tạo:</small>
            <div class="fw-bold">{{ $lesson->created_at->format('d/m/Y H:i') }}</div>
          </div>
          <div class="mb-2">
            <small class="text-muted">Cập Nhật Cuối:</small>
            <div class="fw-bold">{{ $lesson->updated_at->format('d/m/Y H:i') }}</div>
          </div>
        </div>
      </div>

      <!-- Hành Động -->
      <div class="card">
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary">
              <i class="fa fa-save me-2"></i>Cập Nhật Bài Học
            </button>
            <a href="{{ route('admin.lessons.show', $lesson->id) }}" class="btn btn-outline-info">
              <i class="fa fa-eye me-2"></i>Xem Chi Tiết
            </a>
            <a href="{{ route('admin.lessons.index', ['chapter_id' => $lesson->chapter_id]) }}" class="btn btn-outline-secondary">
              <i class="fa fa-times me-2"></i>Hủy
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>

<script>
function toggleContentFields() {
  const type = document.getElementById('type').value;
  const videoField = document.getElementById('video_url_field');
  const contentField = document.getElementById('content_field');
  const fileField = document.getElementById('file_field');
  
  // Ẩn tất cả
  videoField.style.display = 'none';
  contentField.style.display = 'none';
  fileField.style.display = 'none';
  
  // Hiển thị theo loại
  if (type === 'video') {
    videoField.style.display = 'block';
  } else if (type === 'text' || type === 'quiz') {
    contentField.style.display = 'block';
  } else if (type === 'pdf') {
    fileField.style.display = 'block';
  }
}

// Gọi hàm khi trang load
document.addEventListener('DOMContentLoaded', function() {
  toggleContentFields();

  // Quản lý nhiều file bài tập
  const addFileBtn = document.getElementById('add-exercise-file');
  const container = document.getElementById('exercise-files-container');

  addFileBtn.addEventListener('click', function() {
    const newFileInput = document.createElement('div');
    newFileInput.className = 'exercise-file-input mb-2';
    newFileInput.innerHTML = `
      <div class="input-group">
        <input type="file" class="form-control" name="exercise_files[]" accept=".pdf,.doc,.docx,.zip,.rar">
        <button type="button" class="btn btn-outline-danger remove-file-btn">
          <i class="fa fa-trash"></i>
        </button>
      </div>
    `;
    container.appendChild(newFileInput);
    updateRemoveButtons();
  });

  // Xử lý xóa file mới
  container.addEventListener('click', function(e) {
    if (e.target.closest('.remove-file-btn')) {
      e.target.closest('.exercise-file-input').remove();
      updateRemoveButtons();
    }
  });

  // Xử lý xóa file hiện tại
  document.querySelectorAll('.delete-existing-file').forEach(btn => {
    btn.addEventListener('click', function() {
      if (confirm('Bạn có chắc muốn xóa file này?')) {
        this.closest('.d-flex').remove();
      }
    });
  });

  function updateRemoveButtons() {
    const fileInputs = container.querySelectorAll('.exercise-file-input');
    fileInputs.forEach((input, index) => {
      const removeBtn = input.querySelector('.remove-file-btn');
      if (fileInputs.length > 1) {
        removeBtn.style.display = 'block';
      } else {
        removeBtn.style.display = 'none';
      }
    });
  }
});
</script>
@endsection
