/* Custom CSS cho PanDa Learning Platform */

/* Override màu sắc chính */
:root {
  --bs-primary: #6c757d;
  --bs-primary-rgb: 108, 117, 125;
  --bs-secondary: #6c757d;
  --bs-success: #28a745;
  --bs-info: #17a2b8;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #495057;
}

/* Sidebar customization */
.left-sidebar {
  background: #ffffff;
  border-right: 1px solid #dee2e6;
}

.sidebar-nav .sidebar-item .sidebar-link {
  color: #495057;
  border-radius: 8px;
  margin: 2px 8px;
}

.sidebar-nav .sidebar-item .sidebar-link:hover,
.sidebar-nav .sidebar-item .sidebar-link.active {
  background-color: #f8f9fa;
  color: #495057;
}

/* Card styling */
.card {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

/* Button styling */
.btn-primary {
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-primary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-outline-primary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-primary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

/* Badge styling */
.badge.bg-primary {
  background-color: #6c757d !important;
}

/* Progress bar */
.progress-bar {
  background-color: #6c757d;
}

/* Table styling */
.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

/* Form styling */
.form-control:focus {
  border-color: #6c757d;
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}

/* Alert styling */
.alert-primary {
  color: #495057;
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

/* Navigation styling */
.navbar-nav .nav-link {
  color: #495057;
}

.navbar-nav .nav-link:hover {
  color: #6c757d;
}

/* Dropdown styling */
.dropdown-menu {
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Custom utilities */
.text-primary {
  color: #6c757d !important;
}

.bg-primary {
  background-color: #6c757d !important;
}

.border-primary {
  border-color: #6c757d !important;
}

/* Loading spinner */
.preloader {
  background: #ffffff;
}

/* Logo styling */
.brand-logo img {
  max-height: 40px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }
  
  .btn {
    font-size: 0.875rem;
  }
}

/* Print styles */
@media print {
  .sidebar,
  .topbar,
  .btn,
  .dropdown {
    display: none !important;
  }
  
  .page-wrapper {
    margin-left: 0 !important;
  }
}
