@extends('layouts.admin')

@section('title', '<PERSON> Ti<PERSON> Kh<PERSON> H<PERSON> - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Chi Tiết Khóa Học</h4>
        <p class="text-muted mb-0">{{ $course->title }}</p>
      </div>
      <div>
        <a href="{{ route('admin.courses.edit', $course->id) }}" class="btn btn-warning me-2">
          <i class="fa fa-edit me-2"></i>Chỉnh Sửa
        </a>
        <a href="{{ route('admin.courses.index') }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-8">
    <!-- Thông Tin Khóa Học -->
    <div class="card mb-4">
      @if($course->image_url)
      <img src="{{ $course->image_url }}" class="card-img-top" alt="{{ $course->title }}" style="height: 300px; object-fit: cover;">
      @else
      <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
        <i class="fa fa-book fa-4x text-muted"></i>
      </div>
      @endif
      
      <div class="card-body">
        <div class="mb-3">
          <span class="badge bg-primary">{{ $course->category->name }}</span>
          <span class="badge bg-secondary ms-1">
            @if($course->level == 'beginner') Người Mới Bắt Đầu
            @elseif($course->level == 'intermediate') Trung Cấp
            @else Nâng Cao
            @endif
          </span>
          @if($course->status == 'published')
          <span class="badge bg-success ms-1">Đã Xuất Bản</span>
          @elseif($course->status == 'draft')
          <span class="badge bg-warning ms-1">Nháp</span>
          @else
          <span class="badge bg-secondary ms-1">Lưu Trữ</span>
          @endif
        </div>
        
        <h1 class="card-title h3">{{ $course->title }}</h1>
        <p class="card-text">{{ $course->description }}</p>
        
        <div class="row text-center mb-4">
          <div class="col-3">
            <div class="border-end">
              <h5 class="text-primary mb-0">{{ $course->total_lessons }}</h5>
              <small class="text-muted">Bài Học</small>
            </div>
          </div>
          <div class="col-3">
            <div class="border-end">
              <h5 class="text-success mb-0">{{ $course->duration_hours }}h</h5>
              <small class="text-muted">Thời Lượng</small>
            </div>
          </div>
          <div class="col-3">
            <div class="border-end">
              <h5 class="text-warning mb-0">{{ $course->chapters->count() }}</h5>
              <small class="text-muted">Chương</small>
            </div>
          </div>
          <div class="col-3">
            <h5 class="text-info mb-0">0</h5>
            <small class="text-muted">Học Viên</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Nội Dung Khóa Học -->
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Nội Dung Khóa Học
        </h5>
        <a href="{{ route('admin.chapters.create', ['course_id' => $course->id]) }}" class="btn btn-primary btn-sm">
          <i class="fa fa-plus me-2"></i>Thêm Chương
        </a>
      </div>
      <div class="card-body">
        @if($course->chapters->count() > 0)
        <div class="accordion" id="courseContent">
          @foreach($course->chapters as $index => $chapter)
          <div class="accordion-item">
            <h2 class="accordion-header" id="heading{{ $index }}">
              <button class="accordion-button {{ $index == 0 ? '' : 'collapsed' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $index }}" aria-expanded="{{ $index == 0 ? 'true' : 'false' }}">
                <div class="d-flex justify-content-between w-100 me-3">
                  <span>{{ $chapter->title }}</span>
                  <small class="text-muted">{{ $chapter->lessons->count() }} Bài - {{ $chapter->total_duration }} Phút</small>
                </div>
              </button>
            </h2>
            <div id="collapse{{ $index }}" class="accordion-collapse collapse {{ $index == 0 ? 'show' : '' }}" data-bs-parent="#courseContent">
              <div class="accordion-body">
                @if($chapter->description)
                <p class="text-muted mb-3">{{ $chapter->description }}</p>
                @endif
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                  <h6>Danh Sách Bài Học</h6>
                  <a href="{{ route('admin.lessons.create', ['chapter_id' => $chapter->id]) }}" class="btn btn-outline-primary btn-sm">
                    <i class="fa fa-plus me-1"></i>Thêm Bài Học
                  </a>
                </div>
                
                @if($chapter->lessons->count() > 0)
                <div class="list-group list-group-flush">
                  @foreach($chapter->lessons as $lesson)
                  <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                      @if($lesson->type == 'video')
                      <i class="fa fa-play-circle text-primary me-2"></i>
                      @elseif($lesson->type == 'text')
                      <i class="fa fa-file-text text-success me-2"></i>
                      @elseif($lesson->type == 'pdf')
                      <i class="fa fa-file-pdf text-danger me-2"></i>
                      @else
                      <i class="fa fa-question-circle text-warning me-2"></i>
                      @endif
                      
                      <div>
                        <span>{{ $lesson->title }}</span>
                        @if($lesson->is_preview)
                        <span class="badge bg-success ms-2">Preview</span>
                        @endif
                        @if(!$lesson->is_active)
                        <span class="badge bg-secondary ms-2">Ẩn</span>
                        @endif
                      </div>
                    </div>
                    
                    <div class="d-flex align-items-center">
                      <small class="text-muted me-3">{{ $lesson->duration_minutes }} Phút</small>
                      <div class="btn-group btn-group-sm">
                        <a href="{{ route('admin.lessons.edit', $lesson->id) }}" class="btn btn-outline-warning" title="Chỉnh Sửa">
                          <i class="fa fa-edit"></i>
                        </a>
                        <form method="POST" action="{{ route('admin.lessons.destroy', $lesson->id) }}" class="d-inline">
                          @csrf
                          @method('DELETE')
                          <button type="submit" class="btn btn-outline-danger" title="Xóa" onclick="return confirm('Bạn Có Chắc Muốn Xóa?')">
                            <i class="fa fa-trash"></i>
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                  @endforeach
                </div>
                @else
                <div class="text-center py-3 text-muted">
                  <i class="fa fa-inbox fa-2x mb-2"></i>
                  <p class="mb-0">Chưa Có Bài Học Nào</p>
                </div>
                @endif
              </div>
            </div>
          </div>
          @endforeach
        </div>
        @else
        <div class="text-center py-5">
          <i class="fa fa-book fa-4x text-muted mb-3"></i>
          <h5 class="text-muted mb-3">Chưa Có Chương Nào</h5>
          <p class="text-muted mb-4">Bắt Đầu Tạo Chương Đầu Tiên Cho Khóa Học</p>
          <a href="{{ route('admin.chapters.create', ['course_id' => $course->id]) }}" class="btn btn-primary">
            <i class="fa fa-plus me-2"></i>Tạo Chương Đầu Tiên
          </a>
        </div>
        @endif
      </div>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="col-lg-4">
    <!-- Thông Tin Giá -->
    <div class="card mb-4">
      <div class="card-body text-center">
        <div class="mb-3">
          @if($course->price > 0)
          <h3 class="text-primary mb-0">{{ number_format($course->price, 0, ',', '.') }}đ</h3>
          @else
          <h3 class="text-success mb-0">Miễn Phí</h3>
          @endif
        </div>
        
        <div class="d-grid gap-2">
          <a href="{{ route('courses.show', $course->slug) }}" class="btn btn-outline-primary" target="_blank">
            <i class="fa fa-eye me-2"></i>Xem Trang Công Khai
          </a>
        </div>
      </div>
    </div>

    <!-- Yêu Cầu -->
    @if($course->requirements)
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Yêu Cầu
        </h6>
      </div>
      <div class="card-body">
        <div class="text-muted">{{ $course->requirements }}</div>
      </div>
    </div>
    @endif

    <!-- Bạn Sẽ Học Được -->
    @if($course->what_you_learn)
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Bạn Sẽ Học Được
        </h6>
      </div>
      <div class="card-body">
        <div class="text-muted">{{ $course->what_you_learn }}</div>
      </div>
    </div>
    @endif

    <!-- Thông Tin Kỹ Thuật -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Thông Tin Kỹ Thuật
        </h6>
      </div>
      <div class="card-body">
        <div class="mb-2">
          <small class="text-muted">ID:</small>
          <span class="fw-bold">{{ $course->id }}</span>
        </div>
        <div class="mb-2">
          <small class="text-muted">Slug:</small>
          <span class="fw-bold">{{ $course->slug }}</span>
        </div>
        <div class="mb-2">
          <small class="text-muted">Ngày Tạo:</small>
          <span class="fw-bold">{{ $course->created_at->format('d/m/Y H:i') }}</span>
        </div>
        <div class="mb-2">
          <small class="text-muted">Cập Nhật Cuối:</small>
          <span class="fw-bold">{{ $course->updated_at->format('d/m/Y H:i') }}</span>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
