<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Notification;
use App\Models\UserCourse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OrderController extends Controller
{
    /**
     * Hiển <PERSON>hị <PERSON>h Sách Đơn Hàng
     */
    public function index(Request $request)
    {
        $query = Order::with(['user:id,name,email', 'items.course:id,title,category_id']);

        // Lọc theo trạng thái
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Tìm kiếm
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $orders = $query->latest()->paginate(15);

        return view('admin.orders.index', compact('orders'));
    }

    /**
     * Hiển Thị Chi Tiết Đơn Hàng
     */
    public function show($id)
    {
        $order = Order::with(['user', 'items.course', 'approvedBy'])->findOrFail($id);
        return view('admin.orders.show', compact('order'));
    }

    /**
     * Duyệt Đơn Hàng
     */
    public function approve(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'admin_note' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $order = Order::findOrFail($id);

        if ($order->status !== 'pending') {
            return back()->with('error', 'Chỉ Có Thể Duyệt Đơn Hàng Đang Chờ!');
        }

        $order->update([
            'status' => 'approved',
            'admin_note' => $request->admin_note,
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);

        // Tạo UserCourse cho từng khóa học trong đơn hàng
        foreach ($order->items as $item) {
            UserCourse::firstOrCreate([
                'user_id' => $order->user_id,
                'course_id' => $item->course_id,
            ], [
                'order_id' => $order->id,
                'enrolled_at' => now(),
            ]);
        }

        // Gửi thông báo cho user
        Notification::createForUser(
            $order->user_id,
            'Đơn Hàng Được Duyệt',
            "Đơn Hàng {$order->order_number} Đã Được Duyệt Thành Công. Bạn Có Thể Bắt Đầu Học Các Khóa Học Đã Mua.",
            'success',
            ['order_id' => $order->id]
        );

        return back()->with('success', 'Duyệt Đơn Hàng Thành Công!');
    }

    /**
     * Từ Chối Đơn Hàng
     */
    public function reject(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'admin_note' => 'required|string|max:1000',
        ], [
            'admin_note.required' => 'Vui Lòng Nhập Lý Do Từ Chối',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $order = Order::findOrFail($id);

        if ($order->status !== 'pending') {
            return back()->with('error', 'Chỉ Có Thể Từ Chối Đơn Hàng Đang Chờ!');
        }

        $order->update([
            'status' => 'rejected',
            'admin_note' => $request->admin_note,
            'approved_by' => auth()->id(),
        ]);

        // Gửi thông báo cho user
        Notification::createForUser(
            $order->user_id,
            'Đơn Hàng Bị Từ Chối',
            "Đơn hàng {$order->order_number} đã bị từ chối. Lý do: {$request->admin_note}",
            'error',
            ['order_id' => $order->id]
        );

        return back()->with('success', 'Từ Chối Đơn Hàng Thành Công!');
    }

    /**
     * Hoàn Thành Đơn Hàng
     */
    public function complete($id)
    {
        $order = Order::findOrFail($id);

        if ($order->status !== 'approved') {
            return back()->with('error', 'Chỉ Có Thể Hoàn Thành Đơn Hàng Đã Duyệt!');
        }

        $order->update([
            'status' => 'completed',
        ]);

        // Gửi thông báo cho user
        Notification::createForUser(
            $order->user_id,
            'Đơn Hàng Hoàn Thành',
            "Đơn Hàng {$order->order_number} Đã Được Hoàn Thành . Cảm Ơn Các Bạn Đã Sử Dụng Dịch Vụ Của Chúng Tôi!",
            'success',
            ['order_id' => $order->id]
        );

        return back()->with('success', 'Hoàn Thành Đơn Hàng Thành Công!');
    }

    /**
     * Xóa Đơn Hàng
     */
    public function destroy($id)
    {
        $order = Order::findOrFail($id);

        if (in_array($order->status, ['approved', 'completed'])) {
            return back()->with('error', 'Không Thể Xóa Đơn Hàng Đã Duyệt Hoặc Hoàn Thành!');
        }

        $order->delete();

        return redirect()->route('admin.orders.index')
            ->with('success', 'Xóa Đơn Hàng Thành Công!');
    }
}
