<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class BackupDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:database {--keep=7 : <PERSON>ố ngày giữ lại backup}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backup Database Và Dọn Dẹp Backup Cũ';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Bắt Đầu Backup Database...');

        try {
            // Tạo tên file backup
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $filename = "backup_database_{$timestamp}.sql";
            $backupPath = storage_path("app/backups/{$filename}");

            // Tạo thư mục backup nếu chưa có
            if (!file_exists(storage_path('app/backups'))) {
                mkdir(storage_path('app/backups'), 0755, true);
            }

            // Lấy thông tin database
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port');
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');

            // Tạo command mysqldump
            $command = sprintf(
                'mysqldump --host=%s --port=%s --user=%s --password=%s %s > %s',
                escapeshellarg($host),
                escapeshellarg($port),
                escapeshellarg($username),
                escapeshellarg($password),
                escapeshellarg($database),
                escapeshellarg($backupPath)
            );

            // Thực hiện backup
            $output = null;
            $returnCode = null;
            exec($command, $output, $returnCode);

            if ($returnCode === 0 && file_exists($backupPath)) {
                $fileSize = $this->formatBytes(filesize($backupPath));
                $this->info("✅ Backup Thành Công: {$filename} ({$fileSize})");

                // Dọn dẹp backup cũ
                $this->cleanOldBackups($this->option('keep'));

                return Command::SUCCESS;
            } else {
                $this->error('❌ Backup Thất Bại!');
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error("❌ Lỗi Backup: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }

    /**
     * Dọn Dẹp Backup Cũ
     */
    private function cleanOldBackups($keepDays)
    {
        $this->info("Dọn Dẹp Backup Cũ Hơn {$keepDays} Ngày...");

        $backupDir = storage_path('app/backups');
        $cutoffDate = Carbon::now()->subDays($keepDays);
        $deletedCount = 0;

        if (is_dir($backupDir)) {
            $files = glob($backupDir . '/backup_database_*.sql');

            foreach ($files as $file) {
                $fileTime = Carbon::createFromTimestamp(filemtime($file));

                if ($fileTime->lt($cutoffDate)) {
                    unlink($file);
                    $deletedCount++;
                    $this->line("🗑️  Đã Xóa: " . basename($file));
                }
            }
        }

        if ($deletedCount > 0) {
            $this->info("✅ Đã Xóa {$deletedCount} Backup Cũ");
        } else {
            $this->info("ℹ️  Không Có Backup Cũ Cần Xóa");
        }
    }

    /**
     * Format Bytes
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
