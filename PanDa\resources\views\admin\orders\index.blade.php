@extends('layouts.admin')

@section('title', 'Quản Lý Đơn Hàng')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Quản Lý Đơn Hàng</h4>
        <p class="text-muted mb-0">Duyệt Và Quản Lý Các Đơn Hàng Từ Người Dùng</p>
      </div>
    </div>
  </div>
</div>

<!-- Bộ Lọc -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" action="{{ route('admin.orders.index') }}">
          <div class="row">
            <div class="col-md-4 mb-3">
              <label class="form-label">T<PERSON><PERSON></label>
              <input type="text" class="form-control" name="search" value="{{ request('search') }}" placeholder="M<PERSON> đơn, tên khách hàng...">
            </div>
            
            <div class="col-md-4 mb-3">
              <label class="form-label">Trạng Thái</label>
              <select class="form-select" name="status">
                <option value="">Tất Cả Trạng Thái</option>
                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Chờ Duyệt</option>
                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Đã Duyệt</option>
                <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Từ Chối</option>
                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Hoàn Thành</option>
              </select>
            </div>
            
            <div class="col-md-4 mb-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-search me-2"></i>Lọc
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Danh Sách Đơn Hàng -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Danh Sách Đơn Hàng ({{ $orders->total() }})
        </h5>
      </div>
      <div class="card-body p-0">
        @if($orders->count() > 0)
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Mã Đơn</th>
                <th>Khách Hàng</th>
                <th>Số Khóa Học</th>
                <th>Tổng Tiền</th>
                <th>Thanh Toán</th>
                <th>Trạng Thái</th>
                <th>Ngày Tạo</th>
                <th>Thao Tác</th>
              </tr>
            </thead>
            <tbody>
              @foreach($orders as $order)
              <tr>
                <td>
                  <div>
                    <span class="fw-bold">{{ $order->order_number }}</span>
                    @if($order->note)
                    <br><small class="text-muted">Có ghi chú</small>
                    @endif
                  </div>
                </td>
                
                <td>
                  <div>
                    <span class="fw-bold">{{ $order->user->name }}</span>
                    <br><small class="text-muted">{{ $order->user->email }}</small>
                  </div>
                </td>
                
                <td>
                  <span class="badge bg-primary">{{ $order->items->count() }} Khóa</span>
                </td>
                
                <td>
                  <span class="fw-bold text-success">{{ number_format($order->total_amount, 0, ',', '.') }}đ</span>
                </td>
                
                <td>
                  <span class="badge bg-info">{{ $order->payment_method_text }}</span>
                </td>
                
                <td>
                  @if($order->status == 'pending')
                  <span class="badge bg-warning">{{ $order->status_text }}</span>
                  @elseif($order->status == 'approved')
                  <span class="badge bg-success">{{ $order->status_text }}</span>
                  @elseif($order->status == 'rejected')
                  <span class="badge bg-danger">{{ $order->status_text }}</span>
                  @else
                  <span class="badge bg-info">{{ $order->status_text }}</span>
                  @endif
                </td>
                
                <td>
                  <small class="text-muted">{{ $order->created_at->format('d/m/Y H:i') }}</small>
                </td>
                
                <td>
                  <div class="btn-group btn-group-sm">
                    <a href="{{ route('admin.orders.show', $order->id) }}" class="btn btn-outline-info" title="Xem Chi Tiết">
                      <i class="fa fa-eye"></i>
                    </a>
                    
                    @if($order->status == 'pending')
                    <button class="btn btn-outline-success" title="Duyệt" onclick="approveOrder({{ $order->id }})">
                      <i class="fa fa-check"></i>
                    </button>
                    <button class="btn btn-outline-danger" title="Từ Chối" onclick="rejectOrder({{ $order->id }})">
                      <i class="fa fa-times"></i>
                    </button>
                    @elseif($order->status == 'approved')
                    <button class="btn btn-outline-primary" title="Hoàn Thành" onclick="completeOrder({{ $order->id }})">
                      <i class="fa fa-check-double"></i>
                    </button>
                    @endif
                    
                    @if(in_array($order->status, ['pending', 'rejected']))
                    <form method="POST" action="{{ route('admin.orders.destroy', $order->id) }}" class="d-inline">
                      @csrf
                      @method('DELETE')
                      <button type="submit" class="btn btn-outline-danger" title="Xóa" onclick="return confirm('Bạn Có Chắc Muốn Xóa?')">
                        <i class="fa fa-trash"></i>
                      </button>
                    </form>
                    @endif
                  </div>
                </td>
              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
        @else
        <div class="text-center py-5">
          <i class="fa fa-shopping-bag fa-4x text-muted mb-3"></i>
          <h5 class="text-muted mb-3">Chưa Có Đơn Hàng Nào</h5>
          <p class="text-muted mb-0">Các đơn hàng từ người dùng sẽ hiển thị tại đây</p>
        </div>
        @endif
      </div>
    </div>
  </div>
</div>

<!-- Phân Trang -->
@if($orders->hasPages())
<div class="row mt-4">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      {{ $orders->appends(request()->query())->links() }}
    </div>
  </div>
</div>
@endif

<!-- Thống Kê -->
<div class="row mt-4">
  <div class="col-md-3">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $orders->total() }}</h4>
        <small>Tổng Đơn Hàng</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-warning text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $orders->where('status', 'pending')->count() }}</h4>
        <small>Chờ Duyệt</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $orders->where('status', 'approved')->count() }}</h4>
        <small>Đã Duyệt</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ number_format($orders->sum('total_amount'), 0, ',', '.') }}đ</h4>
        <small>Tổng Doanh Thu</small>
      </div>
    </div>
  </div>
</div>

<!-- Modal Duyệt -->
<div class="modal fade" id="approveModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Duyệt Đơn Hàng</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <form id="approveForm" method="POST">
        @csrf
        <div class="modal-body">
          <div class="mb-3">
            <label for="admin_note_approve" class="form-label">Ghi Chú Admin (Tùy Chọn)</label>
            <textarea class="form-control" id="admin_note_approve" name="admin_note" rows="3" placeholder="Nhập ghi chú..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-success">Duyệt Đơn Hàng</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal Từ Chối -->
<div class="modal fade" id="rejectModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Từ Chối Đơn Hàng</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <form id="rejectForm" method="POST">
        @csrf
        <div class="modal-body">
          <div class="mb-3">
            <label for="admin_note_reject" class="form-label">Lý Do Từ Chối <span class="text-danger">*</span></label>
            <textarea class="form-control" id="admin_note_reject" name="admin_note" rows="3" placeholder="Nhập lý do từ chối..." required></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-danger">Từ Chối Đơn Hàng</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
function approveOrder(orderId) {
  document.getElementById('approveForm').action = `/admin/orders/${orderId}/approve`;
  new bootstrap.Modal(document.getElementById('approveModal')).show();
}

function rejectOrder(orderId) {
  document.getElementById('rejectForm').action = `/admin/orders/${orderId}/reject`;
  new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

function completeOrder(orderId) {
  if (confirm('Bạn có chắc muốn hoàn thành đơn hàng này?')) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/orders/${orderId}/complete`;
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    form.appendChild(csrfToken);
    document.body.appendChild(form);
    form.submit();
  }
}
</script>
@endsection
