@extends('layouts.admin')

@section('title', 'Quản <PERSON> - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Q<PERSON>ản L<PERSON> K<PERSON> H<PERSON></h4>
        <p class="text-muted mb-0"><PERSON><PERSON>ả Khó<PERSON> H<PERSON> Trong Hệ Thống</p>
      </div>
      <div>
        <a href="{{ route('admin.courses.create') }}" class="btn btn-primary">
          <i class="fa fa-plus me-2"></i>Thêm Khó<PERSON> H<PERSON>
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Bộ Lọc -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" action="{{ route('admin.courses.index') }}">
          <div class="row">
            <div class="col-md-4 mb-3">
              <label class="form-label">Tìm Kiếm</label>
              <input type="text" class="form-control" name="search" value="{{ request('search') }}" placeholder="Nhập Từ Khóa...">
            </div>
            
            <div class="col-md-3 mb-3">
              <label class="form-label">Danh Mục</label>
              <select class="form-select" name="category">
                <option value="">Tất Cả Danh Mục</option>
                @foreach($categories as $category)
                <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                  {{ $category->name }}
                </option>
                @endforeach
              </select>
            </div>
            
            <div class="col-md-3 mb-3">
              <label class="form-label">Trạng Thái</label>
              <select class="form-select" name="status">
                <option value="">Tất Cả Trạng Thái</option>
                <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Nháp</option>
                <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Đã Xuất Bản</option>
                <option value="archived" {{ request('status') == 'archived' ? 'selected' : '' }}>Lưu Trữ</option>
              </select>
            </div>
            
            <div class="col-md-2 mb-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-search me-2"></i>Lọc
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Danh Sách Khóa Học -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Danh Sách Khóa Học ({{ $courses->total() }})
        </h5>
      </div>
      <div class="card-body p-0">
        @if($courses->count() > 0)
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Hình Ảnh</th>
                <th>Thông Tin Khóa Học</th>
                <th>Danh Mục</th>
                <th>Giá</th>
                <th>Trạng Thái</th>
                <th>Ngày Tạo</th>
                <th>Thao Tác</th>
              </tr>
            </thead>
            <tbody>
              @foreach($courses as $course)
              <tr>
                <td>
                  @if($course->image_url)
                  <img src="{{ $course->image_url }}"
                       alt="{{ $course->title }}"
                       class="rounded"
                       width="60"
                       height="40"
                       style="object-fit: cover;"
                       onerror="this.src='{{ asset('assets/images/default-course.svg') }}'; this.onerror=null;">
                  @else
                  <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 60px; height: 40px;">
                    <i class="fa fa-book text-muted"></i>
                  </div>
                  @endif
                </td>
                
                <td>
                  <div>
                    <h6 class="mb-1">
                      <a href="{{ route('admin.courses.show', $course->id) }}" class="text-decoration-none">
                        {{ $course->title }}
                      </a>
                    </h6>
                    <small class="text-muted">{{ Str::limit($course->description, 50) }}</small>
                    <div class="mt-1">
                      <span class="badge bg-secondary">
                        @if($course->level == 'beginner') Người Mới
                        @elseif($course->level == 'intermediate') Trung Cấp
                        @else Nâng Cao
                        @endif
                      </span>
                      <small class="text-muted ms-2">{{ $course->duration_hours }}h</small>
                    </div>
                  </div>
                </td>
                
                <td>
                  <span class="badge bg-primary">{{ $course->category->name }}</span>
                </td>
                
                <td>
                  @if($course->price > 0)
                  <span class="fw-bold text-success">{{ number_format($course->price, 0, ',', '.') }}đ</span>
                  @else
                  <span class="fw-bold text-info">Miễn Phí</span>
                  @endif
                </td>
                
                <td>
                  @if($course->status == 'published')
                  <span class="badge bg-success">Đã Xuất Bản</span>
                  @elseif($course->status == 'draft')
                  <span class="badge bg-warning">Nháp</span>
                  @else
                  <span class="badge bg-secondary">Lưu Trữ</span>
                  @endif
                </td>
                
                <td>
                  <small class="text-muted">{{ $course->created_at->format('d/m/Y') }}</small>
                </td>
                
                <td>
                  @include('admin.partials.action-buttons', [
                    'showRoute' => route('admin.courses.show', $course->id),
                    'editRoute' => route('admin.courses.edit', $course->id),
                    'deleteRoute' => route('admin.courses.destroy', $course->id),
                    'extraButtons' => [
                      [
                        'route' => route('admin.chapters.index', ['course_id' => $course->id]),
                        'icon' => 'list',
                        'color' => 'primary',
                        'title' => 'Quản Lý Chương',
                        'position' => 'before-edit'
                      ]
                    ]
                  ])
                </td>
              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
        @else
        <div class="text-center py-5">
          <i class="fa fa-book fa-4x text-muted mb-3"></i>
          <h5 class="text-muted mb-3">Chưa Có Khóa Học Nào</h5>
          <p class="text-muted mb-4">Bắt Đầu Tạo Khóa Học Đầu Tiên</p>
          <a href="{{ route('admin.courses.create') }}" class="btn btn-primary">
            <i class="fa fa-plus me-2"></i>Tạo Khóa Học
          </a>
        </div>
        @endif
      </div>
    </div>
  </div>
</div>

<!-- Phân Trang -->
@if($courses->hasPages())
<div class="row mt-4">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      {{ $courses->appends(request()->query())->links() }}
    </div>
  </div>
</div>
@endif

<!-- Thống Kê -->
<div class="row mt-4">
  <div class="col-md-3">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $courses->total() }}</h4>
        <small>Tổng Khóa Học</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $courses->where('status', 'published')->count() }}</h4>
        <small>Đã Xuất Bản</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-warning text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $courses->where('status', 'draft')->count() }}</h4>
        <small>Nháp</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $categories->count() }}</h4>
        <small>Danh Mục</small>
      </div>
    </div>
  </div>
</div>
@endsection
