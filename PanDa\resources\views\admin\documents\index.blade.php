@extends('layouts.admin')

@section('title', 'Quản Lý Tài Liệu')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Quản Lý Tài Liệu</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active">Tài <PERSON></li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="total-docs">0</h4>
              <p class="mb-0">Tổng Tài Liệu</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-file fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-danger text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="pdf-docs">0</h4>
              <p class="mb-0">File PDF</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-file-pdf fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="exercise-docs">0</h4>
              <p class="mb-0">Bài Tập</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-tasks fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-warning text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="total-size">0 MB</h4>
              <p class="mb-0">Tổng Dung Lượng</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-hdd fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="GET" class="row g-3">
            <div class="col-md-4">
              <label class="form-label">Tìm Kiếm</label>
              <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                     placeholder="Tên bài học, khóa học...">
            </div>
            <div class="col-md-3">
              <label class="form-label">Loại Tài Liệu</label>
              <select class="form-select" name="type">
                <option value="">Tất Cả</option>
                <option value="pdf" {{ request('type') == 'pdf' ? 'selected' : '' }}>File PDF</option>
                <option value="exercise" {{ request('type') == 'exercise' ? 'selected' : '' }}>Bài Tập</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Khóa Học</label>
              <select class="form-select" name="course_id">
                <option value="">Tất Cả Khóa Học</option>
                @foreach($courses as $course)
                <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                  {{ $course->title }}
                </option>
                @endforeach
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-search me-1"></i>Tìm
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Documents List -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-file-alt me-2"></i>Danh Sách Tài Liệu ({{ $documents->total() }})
          </h5>
        </div>
        <div class="card-body p-0">
          @if($documents->count() > 0)
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Bài Học</th>
                  <th>Khóa Học</th>
                  <th>Loại Tài Liệu</th>
                  <th>Số File</th>
                  <th>Dung Lượng</th>
                  <th>Ngày Tạo</th>
                  <th>Thao Tác</th>
                </tr>
              </thead>
              <tbody>
                @foreach($documents as $lesson)
                <tr>
                  <td>
                    <div>
                      <h6 class="mb-1">
                        <a href="{{ route('admin.documents.show', $lesson->id) }}" class="text-decoration-none">
                          {{ $lesson->title }}
                        </a>
                      </h6>
                      <small class="text-muted">{{ $lesson->chapter->title }}</small>
                    </div>
                  </td>
                  
                  <td>
                    <span class="badge bg-primary">{{ $lesson->chapter->course->title }}</span>
                  </td>
                  
                  <td>
                    @if($lesson->type == 'pdf' && $lesson->file_path)
                    <span class="badge bg-danger">
                      <i class="fa fa-file-pdf me-1"></i>PDF
                    </span>
                    @endif
                    
                    @if($lesson->exercise_files && count($lesson->exercise_files) > 0)
                    <span class="badge bg-success">
                      <i class="fa fa-tasks me-1"></i>Bài Tập
                    </span>
                    @endif
                  </td>
                  
                  <td>
                    @php
                      $fileCount = 0;
                      if($lesson->type == 'pdf' && $lesson->file_path) $fileCount++;
                      if($lesson->exercise_files) $fileCount += count($lesson->exercise_files);
                    @endphp
                    <span class="badge bg-info">{{ $fileCount }} File</span>
                  </td>
                  
                  <td>
                    @php
                      $totalSize = 0;
                      if($lesson->type == 'pdf' && $lesson->file_path) {
                        $pdfPath = storage_path('app/public/' . $lesson->file_path);
                        if(file_exists($pdfPath)) $totalSize += filesize($pdfPath);
                      }
                      if($lesson->exercise_files) {
                        foreach($lesson->exercise_files as $file) {
                          $filePath = storage_path('app/public/' . $file['path']);
                          if(file_exists($filePath)) $totalSize += filesize($filePath);
                        }
                      }
                      $sizeFormatted = $totalSize > 0 ? number_format($totalSize / 1024 / 1024, 2) . ' MB' : '0 MB';
                    @endphp
                    {{ $sizeFormatted }}
                  </td>
                  
                  <td>{{ $lesson->created_at->format('d/m/Y') }}</td>
                  
                  <td>
                    <div class="btn-group btn-group-sm">
                      <a href="{{ route('admin.documents.show', $lesson->id) }}" class="btn btn-outline-primary" title="Xem">
                        <i class="fa fa-eye"></i>
                      </a>
                      <a href="{{ route('admin.lessons.edit', $lesson->id) }}" class="btn btn-outline-warning" title="Sửa">
                        <i class="fa fa-edit"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          <div class="card-footer">
            {{ $documents->links() }}
          </div>
          @else
          <div class="text-center py-5">
            <i class="fa fa-file-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Không Tìm Thấy Tài Liệu</h5>
            <p class="text-muted">Thử thay đổi bộ lọc để tìm kiếm</p>
          </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Load stats
fetch('{{ route("admin.documents.stats") }}')
  .then(response => response.json())
  .then(data => {
    document.getElementById('total-docs').textContent = data.total_documents;
    document.getElementById('pdf-docs').textContent = data.pdf_documents;
    document.getElementById('exercise-docs').textContent = data.exercise_files;
    document.getElementById('total-size').textContent = data.total_size;
  })
  .catch(error => console.error('Error loading stats:', error));
</script>
@endsection
