@extends('layouts.admin')

@section('title', 'Bảng Điều Khiển Admin')

@section('content')
<div class="container-fluid">
  <!-- Welcome Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="welcome-header text-white rounded-4 p-4">
        <div class="row align-items-center">
          <div class="col-md-8">
            <h2 class="mb-2">Chào Mừng Trở Lại, {{ auth()->user()->name }}! 👋</h2>
            <p class="mb-0 opacity-75">Hôm nay là {{ now()->format('d/m/Y') }} - H<PERSON>y cùng quản lý hệ thống học tập hiệu quả</p>
          </div>
          <div class="col-md-4 text-end">
            <div class="welcome-stats">
              <div class="stat-item">
                <h4 class="mb-0">{{ \App\Models\User::whereDate('created_at', today())->count() }}</h4>
                <small>Người Dùng Mới Hôm Nay</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Stats -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
      <div class="stats-card bg-primary">
        <div class="stats-icon">
          <i class="fa fa-users"></i>
        </div>
        <div class="stats-content">
          <h3>{{ number_format(\App\Models\User::count()) }}</h3>
          <p>Tổng Người Dùng</p>
          <div class="stats-trend">
            <i class="fa fa-arrow-up"></i>
            <span>+{{ \App\Models\User::whereDate('created_at', '>=', now()->subDays(7))->count() }} tuần này</span>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
      <div class="stats-card bg-success">
        <div class="stats-icon">
          <i class="fa fa-graduation-cap"></i>
        </div>
        <div class="stats-content">
          <h3>{{ number_format(\App\Models\Course::count()) }}</h3>
          <p>Tổng Khóa Học</p>
          <div class="stats-trend">
            <i class="fa fa-arrow-up"></i>
            <span>{{ \App\Models\Course::where('status', 'published')->count() }} đã xuất bản</span>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
      <div class="stats-card bg-warning">
        <div class="stats-icon">
          <i class="fa fa-book"></i>
        </div>
        <div class="stats-content">
          <h3>{{ number_format(\App\Models\Lesson::count()) }}</h3>
          <p>Tổng Bài Học</p>
          <div class="stats-trend">
            <i class="fa fa-clock"></i>
            <span>{{ \App\Models\Chapter::count() }} chương</span>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
      <div class="stats-card bg-info">
        <div class="stats-icon">
          <i class="fa fa-user-graduate"></i>
        </div>
        <div class="stats-content">
          <h3>{{ number_format(\App\Models\UserCourse::count()) }}</h3>
          <p>Lượt Đăng Ký</p>
          <div class="stats-trend">
            <i class="fa fa-chart-line"></i>
            <span>{{ \App\Models\UserCourse::whereDate('created_at', today())->count() }} Hôm Nay</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts & Analytics -->
  <div class="row mb-4">
    <div class="col-xl-8 mb-4">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-header bg-transparent border-0 pb-0">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="fa fa-chart-line text-primary me-2"></i>Thống Kê Đăng Ký Khóa Học
            </h5>
            <div class="dropdown">
              <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                7 ngày qua
              </button>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#">7 Ngày Qua</a></li>
                <li><a class="dropdown-item" href="#">30 Ngày Qua</a></li>
                <li><a class="dropdown-item" href="#">3 Tháng Qua</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card-body">
          <canvas id="enrollmentChart" height="300"></canvas>
        </div>
      </div>
    </div>

    <div class="col-xl-4 mb-4">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-header bg-transparent border-0 pb-0">
          <h5 class="card-title mb-0">
            <i class="fa fa-fire text-danger me-2"></i>Khóa Học Hot
          </h5>
        </div>
        <div class="card-body">
          @foreach(\App\Models\Course::withCount('userCourses')->orderBy('user_courses_count', 'desc')->take(5)->get() as $index => $course)
          <div class="popular-course-item d-flex align-items-center mb-3">
            <div class="rank-badge">{{ $index + 1 }}</div>
            <div class="course-info flex-grow-1 ms-3">
              <h6 class="mb-1">{{ Str::limit($course->title, 30) }}</h6>
              <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2">{{ $course->user_courses_count }} Học Viên</span>
                <small class="text-muted">{{ $course->category->name }}</small>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>

  <!-- Management Tools & Quick Actions -->
  <div class="row">
    <div class="col-xl-6 mb-4">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent border-0 pb-0">
          <h5 class="card-title mb-0">
            <i class="fa fa-tools text-primary me-2"></i>Công Cụ Quản Lý
          </h5>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-6">
              <a href="{{ route('admin.courses.create') }}" class="management-tool-card">
                <div class="tool-icon bg-primary">
                  <i class="fa fa-plus"></i>
                </div>
                <div class="tool-content">
                  <h6>Tạo Khóa Học</h6>
                  <small>Thêm khóa học mới</small>
                </div>
              </a>
            </div>
            <div class="col-md-6">
              <a href="{{ route('admin.users.index') }}" class="management-tool-card">
                <div class="tool-icon bg-success">
                  <i class="fa fa-users"></i>
                </div>
                <div class="tool-content">
                  <h6>Quản Lý User</h6>
                  <small>Danh sách người dùng</small>
                </div>
              </a>
            </div>
            <div class="col-md-6">
              <a href="{{ route('admin.categories.index') }}" class="management-tool-card">
                <div class="tool-icon bg-warning">
                  <i class="fa fa-list"></i>
                </div>
                <div class="tool-content">
                  <h6>Danh Mục</h6>
                  <small>Quản lý danh mục</small>
                </div>
              </a>
            </div>
            <div class="col-md-6">
              <a href="{{ route('admin.orders.index') }}" class="management-tool-card">
                <div class="tool-icon bg-info">
                  <i class="fa fa-shopping-bag"></i>
                </div>
                <div class="tool-content">
                  <h6>Đơn Hàng</h6>
                  <small>Quản lý đơn hàng</small>
                </div>
              </a>
            </div>
            <div class="col-md-6">
              <a href="{{ route('admin.documents.index') }}" class="management-tool-card">
                <div class="tool-icon bg-secondary">
                  <i class="fa fa-file-pdf"></i>
                </div>
                <div class="tool-content">
                  <h6>Tài Liệu</h6>
                  <small>Quản lý tài liệu</small>
                </div>
              </a>
            </div>
            <div class="col-md-6">
              <a href="{{ route('admin.settings.index') }}" class="management-tool-card">
                <div class="tool-icon bg-dark">
                  <i class="fa fa-cogs"></i>
                </div>
                <div class="tool-content">
                  <h6>Cài Đặt</h6>
                  <small>Cài đặt hệ thống</small>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-6 mb-4">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent border-0 pb-0">
          <h5 class="card-title mb-0">
            <i class="fa fa-clock text-info me-2"></i>Hoạt Động Gần Đây
          </h5>
        </div>
        <div class="card-body">
          <div class="activity-timeline">
            @foreach(\App\Models\User::latest()->take(6)->get() as $user)
            <div class="activity-item">
              <div class="activity-avatar">
                @if($user->avatar)
                <img src="{{ asset('storage/' . $user->avatar) }}" alt="{{ $user->name }}">
                @else
                <div class="avatar-placeholder">{{ strtoupper(substr($user->name, 0, 1)) }}</div>
                @endif
              </div>
              <div class="activity-content">
                <p class="mb-1"><strong>{{ $user->name }}</strong> Đã Đăng Ký Tài Khoản</p>
                <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
              </div>
            </div>
            @endforeach
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Management Sections -->
  <div class="row">
    <div class="col-xl-4 mb-4">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent border-0 pb-0">
          <h5 class="card-title mb-0">
            <i class="fa fa-chart-bar text-success me-2"></i>Báo Cáo Nhanh
          </h5>
        </div>
        <div class="card-body">
          <div class="quick-report-item">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <span>Doanh Thu Tháng Này</span>
              <strong class="text-success">{{ number_format(\App\Models\Order::whereMonth('created_at', now()->month)->sum('total_amount')) }} VNĐ</strong>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-3">
              <span>Khóa Học Mới</span>
              <strong class="text-primary">{{ \App\Models\Course::whereMonth('created_at', now()->month)->count() }}</strong>
            </div>
            <div class="d-flex justify-content-between align-items-center">
              <span>Đăng Ký Mới</span>
              <strong class="text-info">{{ \App\Models\UserCourse::whereMonth('created_at', now()->month)->count() }}</strong>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-4 mb-4">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent border-0 pb-0">
          <h5 class="card-title mb-0">
            <i class="fa fa-exclamation-triangle text-warning me-2"></i>Cần Xử Lý
          </h5>
        </div>
        <div class="card-body">
          <div class="pending-items">
            <div class="pending-item">
              <i class="fa fa-credit-card text-warning me-2"></i>
              <span>{{ \App\Models\Order::where('status', 'pending')->count() }} Đơn Hàng Chờ Duyệt</span>
            </div>
            <div class="pending-item">
              <i class="fa fa-comments text-info me-2"></i>
              <span>{{ \App\Models\Comment::where('status', 'pending')->count() }} Bình Luận Chờ Duyệt</span>
            </div>
            <div class="pending-item">
              <i class="fa fa-star text-warning me-2"></i>
              <span>{{ \App\Models\Review::where('status', 'pending')->count() }} Đánh Giá Chờ Duyệt</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-4 mb-4">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent border-0 pb-0">
          <h5 class="card-title mb-0">
            <i class="fa fa-rocket text-danger me-2"></i>Thao Tác Nhanh
          </h5>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('admin.courses.create') }}" class="btn btn-primary btn-sm">
              <i class="fa fa-plus me-2"></i>Tạo Khóa Học Mới
            </a>
            <a href="{{ route('admin.chapters.create') }}" class="btn btn-success btn-sm">
              <i class="fa fa-folder-plus me-2"></i>Thêm Chương Mới
            </a>
            <a href="{{ route('admin.lessons.create') }}" class="btn btn-warning btn-sm">
              <i class="fa fa-play-circle me-2"></i>Tạo Bài Học
            </a>
            <a href="{{ route('admin.categories.create') }}" class="btn btn-info btn-sm">
              <i class="fa fa-tags me-2"></i>Thêm Danh Mục
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.welcome-header {
  background: linear-gradient(135deg, #5e72e4 0%, #825ee4 100%);
  border-radius: 20px !important;
}

.stats-card {
  background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark));
  border-radius: 20px;
  padding: 30px;
  color: white;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
}

.stats-card.bg-success {
  background: linear-gradient(135deg, #2dce89, #2dcecc);
}

.stats-card.bg-warning {
  background: linear-gradient(135deg, #fb6340, #fbb140);
}

.stats-card.bg-info {
  background: linear-gradient(135deg, #11cdef, #1171ef);
}

.management-tool-card {
  display: block;
  text-decoration: none;
  color: inherit;
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.management-tool-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
  text-decoration: none;
  color: inherit;
}

.tool-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.tool-content h6 {
  font-weight: 600;
  margin-bottom: 5px;
  color: #2c3e50;
}

.tool-content small {
  color: #6c757d;
  font-size: 0.85rem;
}

.stats-icon {
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 3rem;
  opacity: 0.3;
}

.stats-content h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stats-content p {
  margin-bottom: 10px;
  opacity: 0.9;
}

.stats-trend {
  font-size: 0.85rem;
  opacity: 0.8;
}

.rank-badge {
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, #5e72e4, #825ee4);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.quick-report-item {
  padding: 10px 0;
}

.pending-items {
  padding: 10px 0;
}

.pending-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 0.9rem;
}

.pending-item:last-child {
  border-bottom: none;
}

.activity-timeline {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
}

.activity-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #5e72e4, #825ee4);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.card {
  border-radius: 15px;
  border: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.btn {
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.card-header {
  background: transparent !important;
  border-bottom: 1px solid #f0f0f0;
}

.card-title {
  font-weight: 600;
  color: #2c3e50;
}
</style>
@endsection
