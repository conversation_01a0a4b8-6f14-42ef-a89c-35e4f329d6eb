<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Vui L<PERSON>ng <PERSON>');
        }

        if (auth()->user()->role !== 'admin') {
            return redirect()->route('user.dashboard')->with('error', 'Bạn <PERSON>ng <PERSON>ề<PERSON>');
        }

        return $next($request);
    }
}
