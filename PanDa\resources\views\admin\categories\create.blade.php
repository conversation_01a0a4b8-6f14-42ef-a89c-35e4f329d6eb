@extends('layouts.admin')

@section('title', 'Thêm <PERSON> - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Thêm <PERSON></h4>
        <p class="text-muted mb-0">Tạo <PERSON> H<PERSON></p>
      </div>
      <div>
        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<form method="POST" action="{{ route('admin.categories.store') }}">
  @csrf
  <div class="row">
    <div class="col-lg-8">
      <!-- Thông <PERSON> -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Thông Tin Danh Mục
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-12 mb-3">
              <label for="name" class="form-label">Tên Danh Mục <span class="text-danger">*</span></label>
              <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" placeholder="Nhập Tên Danh Mục" required>
              @error('name')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-12 mb-3">
              <label for="description" class="form-label">Mô Tả</label>
              <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="4" placeholder="Nhập Mô Tả Danh Mục">{{ old('description') }}</textarea>
              @error('description')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-12 mb-3">
              <label for="image_url" class="form-label">URL Hình Ảnh Danh Mục</label>
              <input type="url" class="form-control @error('image_url') is-invalid @enderror"
                     id="image_url" name="image_url" value="{{ old('image_url') }}"
                     placeholder="https://example.com/category-image.jpg">
              @error('image_url')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
              <div class="form-text">Nhập URL hình ảnh từ internet (tiết kiệm dung lượng server)</div>

              <!-- Preview -->
              <div id="image-preview-container" class="mt-2" style="display: none;">
                <img id="image-preview" class="img-fluid rounded" style="max-height: 200px;" alt="Preview">
              </div>
            </div>

            <!-- Màu Sắc -->
            <div class="col-md-6 mb-3">
              <label for="color" class="form-label">Màu Sắc</label>
              <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror"
                     id="color" name="color" value="{{ old('color', '#007bff') }}">
              @error('color')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <!-- Thứ Tự -->
            <div class="col-md-6 mb-3">
              <label for="sort_order" class="form-label">Thứ Tự Hiển Thị</label>
              <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                     id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
              @error('sort_order')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <!-- Cài Đặt -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Cài Đặt
          </h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label for="is_active" class="form-label">Trạng Thái</label>
            <select class="form-select" id="is_active" name="is_active">
              <option value="1" {{ old('is_active', '1') == '1' ? 'selected' : '' }}>Hoạt Động</option>
              <option value="0" {{ old('is_active') == '0' ? 'selected' : '' }}>Ẩn</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Hành Động -->
      <div class="card">
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary">
              <i class="fa fa-save me-2"></i>Lưu Danh Mục
            </button>
            <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
              <i class="fa fa-times me-2"></i>Hủy
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>

<!-- Preview URL Ảnh -->
<script>
// Preview image URL
document.getElementById('image_url').addEventListener('input', function(e) {
  const url = e.target.value;
  const preview = document.getElementById('image-preview');
  const container = document.getElementById('image-preview-container');

  if (url && isValidImageUrl(url)) {
    preview.src = url;
    container.style.display = 'block';

    // Kiểm tra nếu ảnh load thành công
    preview.onload = function() {
      container.style.display = 'block';
    };

    preview.onerror = function() {
      container.style.display = 'none';
    };
  } else {
    container.style.display = 'none';
  }
});

// Bỏ validation strict, chấp nhận mọi URL
function isValidImageUrl(url) {
  return url && url.trim() !== '';
}
</script>
@endsection
