@extends('layouts.user')

@section('title', 'Chia Sẻ Chứng Chỉ')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Chia Sẻ Chứng Chỉ</h4>
        <p class="text-muted mb-0">Chia Sẻ Thành Tích Của Bạn Với Mọi <PERSON></p>
      </div>
      <div>
        <a href="{{ route('user.certificates.index') }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Certificate Preview -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-certificate text-warning me-2"></i>{{ $certificate->certificate_data['course_title'] }}
        </h5>
      </div>
      <div class="card-body text-center">
        <div class="certificate-preview p-4 border rounded bg-light">
          <h2 class="text-primary mb-3">CHỨNG CHỈ HOÀN THÀNH</h2>
          <p class="lead">Chứng nhận rằng</p>
          <h3 class="text-success fw-bold mb-3">{{ $certificate->certificate_data['user_name'] }}</h3>
          <p class="lead">đã hoàn thành xuất sắc khóa học</p>
          <h4 class="text-primary fw-bold mb-3">{{ $certificate->certificate_data['course_title'] }}</h4>
          <div class="row mt-4">
            <div class="col-md-6">
              <small class="text-muted">Ngày cấp: {{ $certificate->formatted_issue_date }}</small>
            </div>
            <div class="col-md-6">
              <small class="text-muted">Mã chứng chỉ: {{ $certificate->certificate_code }}</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Share Options -->
<div class="row">
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-link me-2"></i>Liên Kết Chia Sẻ
        </h6>
      </div>
      <div class="card-body">
        <div class="input-group mb-3">
          <input type="text" class="form-control" id="shareUrl" value="{{ $certificate->share_url }}" readonly>
          <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('shareUrl')">
            <i class="fa fa-copy"></i>
          </button>
        </div>
        <small class="text-muted">Sao chép liên kết này để chia sẻ chứng chỉ của bạn</small>
      </div>
    </div>
  </div>

  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-qrcode me-2"></i>Mã QR
        </h6>
      </div>
      <div class="card-body text-center">
        <div id="qrcode" class="mb-3"></div>
        <button class="btn btn-outline-primary btn-sm" onclick="downloadQR()">
          <i class="fa fa-download me-1"></i>Tải QR Code
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Social Media Share -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-share-alt me-2"></i>Chia Sẻ Trên Mạng Xã Hội
        </h6>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-md-3 mb-3">
            <a href="{{ $facebookShareUrl }}" target="_blank" class="btn btn-primary w-100">
              <i class="fab fa-facebook-f me-2"></i>Facebook
            </a>
          </div>
          <div class="col-md-3 mb-3">
            <a href="{{ $twitterShareUrl }}" target="_blank" class="btn btn-info w-100">
              <i class="fab fa-twitter me-2"></i>Twitter
            </a>
          </div>
          <div class="col-md-3 mb-3">
            <a href="{{ $linkedinShareUrl }}" target="_blank" class="btn btn-primary w-100" style="background-color: #0077b5;">
              <i class="fab fa-linkedin-in me-2"></i>LinkedIn
            </a>
          </div>
          <div class="col-md-3 mb-3">
            <button class="btn btn-success w-100" onclick="shareViaEmail()">
              <i class="fa fa-envelope me-2"></i>Email
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Actions -->
<div class="row mt-4">
  <div class="col-12 text-center">
    <a href="{{ route('user.certificates.show', $certificate->id) }}" class="btn btn-outline-primary me-2">
      <i class="fa fa-eye me-2"></i>Xem Chi Tiết
    </a>
    <a href="{{ route('user.certificates.download', $certificate->id) }}" class="btn btn-success me-2">
      <i class="fa fa-download me-2"></i>Tải PDF
    </a>
    <a href="{{ $certificate->verify_url }}" target="_blank" class="btn btn-info">
      <i class="fa fa-check-circle me-2"></i>Xác Thực
    </a>
  </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
// Generate QR Code
document.addEventListener('DOMContentLoaded', function() {
    QRCode.toCanvas(document.getElementById('qrcode'), '{{ $certificate->verify_url }}', {
        width: 200,
        height: 200,
        colorDark: '#495057',
        colorLight: '#ffffff'
    });
});

// Copy to clipboard
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show success message
    const btn = element.nextElementSibling;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fa fa-check"></i>';
    btn.classList.remove('btn-outline-secondary');
    btn.classList.add('btn-success');
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.classList.remove('btn-success');
        btn.classList.add('btn-outline-secondary');
    }, 2000);
}

// Download QR Code
function downloadQR() {
    const canvas = document.querySelector('#qrcode canvas');
    const link = document.createElement('a');
    link.download = 'certificate-qr-{{ $certificate->certificate_code }}.png';
    link.href = canvas.toDataURL();
    link.click();
}

// Share via email
function shareViaEmail() {
    const subject = encodeURIComponent('Chứng chỉ hoàn thành khóa học: {{ $certificate->certificate_data["course_title"] }}');
    const body = encodeURIComponent(`Xin chào,

Tôi vừa hoàn thành khóa học "{{ $certificate->certificate_data["course_title"] }}" và nhận được chứng chỉ.

Bạn có thể xem chứng chỉ của tôi tại: {{ $certificate->share_url }}

Hoặc xác thực chứng chỉ tại: {{ $certificate->verify_url }}

Trân trọng,
{{ $certificate->certificate_data["user_name"] }}`);
    
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
}
</script>
@endpush

@push('styles')
<style>
.certificate-preview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #6c757d;
}

.social-share-btn {
    transition: transform 0.3s ease;
}

.social-share-btn:hover {
    transform: translateY(-2px);
}

#qrcode canvas {
    border: 1px solid #dee2e6;
    border-radius: 8px;
}
</style>
@endpush
