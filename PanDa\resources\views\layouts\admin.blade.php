<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="csrf-token" content="{{ csrf_token() }}">

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="{{ asset('assets/images/logos/favicon.png') }}" />

  <!-- Core Css -->
  <link rel="stylesheet" href="{{ asset('assets/css/styles.css') }}" />

  <!-- Custom Css -->
  <link rel="stylesheet" href="{{ asset('assets/css/custom.css') }}" />

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <title>@yield('title', 'Quản Trị Hệ Thống')</title>
</head>

<body class="link-sidebar">
  <!-- Preloader -->
  <div class="preloader">
    <img src="{{ asset('assets/images/logos/favicon.png') }}" alt="loader" class="lds-ripple img-fluid" />
  </div>
  
  <div id="main-wrapper">
    <!-- Sidebar Start -->
    <aside class="left-sidebar with-vertical">
      <div>
        <div class="brand-logo d-flex align-items-center">
          <a href="{{ route('admin.dashboard') }}" class="text-nowrap logo-img">
            <img src="{{ asset('assets/images/logos/logo.svg') }}" alt="Logo" />
          </a>
        </div>

        <!-- Sidebar Navigation -->
        <nav class="sidebar-nav scroll-sidebar" data-simplebar>
          <ul class="sidebar-menu" id="sidebarnav">
            <!-- Dashboard -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Bảng Điều Khiển</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.dashboard') }}" aria-expanded="false">
                <i class="fa fa-tachometer-alt"></i>
                <span class="hide-menu">Tổng Quan</span>
              </a>
            </li>

            <!-- Quản Lý Khóa Học -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Quản Lý Khóa Học</span>
            </li>



                <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.categories.index') }}" aria-expanded="false">
                <i class="fa fa-list"></i>
                <span class="hide-menu">Danh Mục</span>
              </a>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.courses.index') }}" aria-expanded="false">
                <i class="fa fa-book"></i>
                <span class="hide-menu">Danh Sách Khóa Học</span>
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.chapters.index') }}" aria-expanded="false">
                <i class="fa fa-folder"></i>
                <span class="hide-menu">Quản Lý Chương</span>
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.lessons.index') }}" aria-expanded="false">
                <i class="fa fa-play-circle"></i>
                <span class="hide-menu">Quản Lý Bài Học</span>
              </a>
            </li>

            <!-- Quản Lý Người Dùng -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Quản Lý Người Dùng</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.users.index') }}" aria-expanded="false">
                <i class="fa fa-users"></i>
                <span class="hide-menu">Danh Sách Người Dùng</span>
              </a>
            </li>
            <!-- Quản Lý Giao Dịch -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Quản Lý Giao Dịch</span>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.orders.index') }}" aria-expanded="false">
                <i class="fa fa-shopping-bag"></i>
                <span class="hide-menu">Đơn Hàng</span>
              </a>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="#" aria-expanded="false">
                <i class="fa fa-credit-card"></i>
                <span class="hide-menu">Duyệt Thanh Toán</span>
              </a>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="#" aria-expanded="false">
                <i class="fa fa-history"></i>
                <span class="hide-menu">Lịch Sử Giao Dịch</span>
              </a>
            </li>
                            
            <li class="sidebar-item">
              <a class="sidebar-link" href="#" aria-expanded="false">
                <i class="fa fa-money-bill"></i>
                <span class="hide-menu">Báo Cáo Doanh Thu</span>
              </a>
            </li>

            <!-- Quản Lý Nội Dung -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Quản Lý Nội Dung</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.documents.index') }}" aria-expanded="false">
                <i class="fa fa-file-pdf"></i>
                <span class="hide-menu">Tài Liệu</span>
              </a>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.comments.index') }}" aria-expanded="false">
                <i class="fa fa-comments"></i>
                <span class="hide-menu">Bình Luận</span>
              </a>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.reviews.index') }}" aria-expanded="false">
                <i class="fa fa-star"></i>
                <span class="hide-menu">Đánh Giá</span>
              </a>
            </li>



            <!-- Cài Đặt Hệ Thống -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Cài Đặt</span>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.settings.index') }}" aria-expanded="false">
                <i class="fa fa-cogs"></i>
                <span class="hide-menu">Cài Đặt Hệ Thống</span>
              </a>
            </li>

            <!-- Chuyển Đổi Giao Diện -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Chuyển Đổi</span>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('user.dashboard') }}" aria-expanded="false">
                <i class="fa fa-exchange-alt"></i>
                <span class="hide-menu">Về Người Dùng</span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </aside>
    <!-- Sidebar End -->

    <!-- Main wrapper -->
    <div class="page-wrapper">
      <!-- Header Start -->
      <header class="topbar">
        <div class="with-vertical">
          <nav class="navbar navbar-expand-lg p-0">
            <ul class="navbar-nav">
              <li class="nav-item nav-icon-hover-bg rounded-circle">
                <a class="nav-link sidebartoggler" id="headerCollapse" href="javascript:void(0)">
                  <i class="fa fa-bars"></i>
                </a>
              </li>
            </ul>

            <div class="d-block d-lg-none py-4">
              <a href="{{ route('admin.dashboard') }}" class="text-nowrap logo-img">
                <img src="{{ asset('assets/images/logos/logo.svg') }}" alt="Logo" />
              </a>
            </div>

            <a class="navbar-toggler nav-icon-hover-bg rounded-circle p-0 mx-0 border-0" href="javascript:void(0)" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
              <i class="fa fa-ellipsis-v"></i>
            </a>

            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
              <div class="d-flex align-items-center justify-content-between">
     

                <ul class="navbar-nav flex-row ms-auto align-items-center justify-content-center">
                  <!-- Notifications -->                
                  <!-- User Profile -->
                  <li class="nav-item dropdown">
                    <a class="nav-link nav-icon-hover-bg rounded-circle" href="javascript:void(0)" id="drop2" aria-expanded="false">
                      @if(auth()->user()->avatar)
                      <img src="{{ asset('storage/' . auth()->user()->avatar) }}" alt="Avatar" width="35" height="35" class="rounded-circle">
                      @else
                      <i class="fa fa-user-shield"></i>
                      @endif
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-animate-up" aria-labelledby="drop2">
                      <div class="message-body">
                        <div class="mx-3 mt-2 mb-3 d-flex align-items-center">
                          @if(auth()->user()->avatar)
                          <img src="{{ asset('storage/' . auth()->user()->avatar) }}" alt="Avatar" width="40" height="40" class="rounded-circle me-3">
                          @else
                          <div class="rounded-circle bg-warning d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            <i class="fa fa-user-shield text-white"></i>
                          </div>
                          @endif
                          <div>
                            <h6 class="mb-0">{{ auth()->user()->name }}</h6>
                            <span class="fs-12 text-warning fw-bold">Quản Trị Viên</span>
                          </div>
                        </div>
                        
                        <a href="{{ route('profile.index') }}" class="d-flex align-items-center gap-2 dropdown-item">
                          <i class="fa fa-user fs-4"></i>
                          <p class="mb-0 fs-3">Hồ Sơ Cá Nhân</p>
                        </a>
                        
                        <a href="#" class="d-flex align-items-center gap-2 dropdown-item">
                          <i class="fa fa-cogs fs-4"></i>
                          <p class="mb-0 fs-3">Cài Đặt Hệ Thống</p>
                        </a>
                        
                        <form method="POST" action="{{ route('logout') }}">
                          @csrf
                          <button type="submit" class="btn btn-outline-danger mx-3 mt-2 d-block w-100">
                            <i class="fa fa-sign-out-alt me-2"></i>Đăng Xuất
                          </button>
                        </form>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </header>
      <!-- Header End -->

      <!-- Main Content -->
      <div class="body-wrapper">
        <div class="container-fluid">
          @if (session('success'))
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fa fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
          @endif

          @if (session('error'))
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fa fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
          @endif

          @yield('content')
        </div>
      </div>
    </div>
  </div>

  <!-- Import Js Files -->
  <script src="{{ asset('assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>
  <script src="{{ asset('assets/libs/simplebar/dist/simplebar.min.js') }}"></script>
  <script src="{{ asset('assets/js/theme/app.init.js') }}"></script>
  <script src="{{ asset('assets/js/theme/theme.js') }}"></script>
  <script src="{{ asset('assets/js/theme/app.min.js') }}"></script>

  <!-- Image Fallback Handler -->
  <script src="{{ asset('js/image-fallback.js') }}"></script>

  <!-- Solar Icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>

  @stack('scripts')
</body>

</html>
