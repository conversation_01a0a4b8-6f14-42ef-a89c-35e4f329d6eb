<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // <PERSON><PERSON><PERSON> các bản ghi trùng lặp trong user_courses (SQLite compatible)
        // Giữ lại bản ghi có ID nhỏ nhất cho mỗi user_id + course_id
        DB::statement("
            DELETE FROM user_courses
            WHERE id NOT IN (
                SELECT MIN(id)
                FROM (SELECT * FROM user_courses) AS uc
                GROUP BY user_id, course_id
            )
        ");

        // Thêm unique constraint để tránh duplicate trong tương lai
        Schema::table('user_courses', function (Blueprint $table) {
            $table->unique(['user_id', 'course_id'], 'user_course_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_courses', function (Blueprint $table) {
            $table->dropUnique('user_course_unique');
        });
    }
};
