<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lesson;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Tạo Categories
        $categories = [
            [
                'name' => 'Lập Trình Web',
                'slug' => 'lap-trinh-web',
                'description' => 'Học Lập Trình Web Từ Cơ Bản Đến Nâng Cao',
                'is_active' => true,
            ],
            [
                'name' => 'Mobile App',
                'slug' => 'mobile-app',
                'description' => 'Phát Triển Ứng Dụng Di Động',
                'is_active' => true,
            ],
            [
                'name' => 'Data Science',
                'slug' => 'data-science',
                'description' => 'Khoa Học Dữ Liệu Và Machine Learning',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }

        // Tạo Courses
        $courses = [
            [
                'title' => 'Laravel Từ Cơ Bản Đến Nâng Cao',
                'slug' => 'laravel-tu-co-ban-den-nang-cao',
                'description' => 'Học Laravel Framework Để Xây Dựng Ứng Dụng Web Hiện Đại. Khóa Học Bao Gồm Tất Cả Kiến Thức Cần Thiết Từ Cơ Bản Đến Nâng Cao.',
                'price' => 299000,
                'level' => 'intermediate',
                'category_id' => 1,
                'status' => 'published',
                'duration_hours' => 20,
                'requirements' => 'Kiến Thức Cơ Bản Về PHP, HTML, CSS',
                'what_you_learn' => 'MVC Pattern, Eloquent ORM, Blade Template, Authentication, API Development',
            ],
            [
                'title' => 'React Native - Xây Dựng App Mobile',
                'slug' => 'react-native-xay-dung-app-mobile',
                'description' => 'Tạo Ứng Dụng Mobile Đa Nền Tảng Với React Native. Từ Cơ Bản Đến Triển Khai App Store.',
                'price' => 399000,
                'level' => 'intermediate',
                'category_id' => 2,
                'status' => 'published',
                'duration_hours' => 25,
                'requirements' => 'JavaScript ES6+, React Cơ Bản',
                'what_you_learn' => 'React Native Components, Navigation, State Management, API Integration',
            ],
            [
                'title' => 'Python Cho Data Science',
                'slug' => 'python-cho-data-science',
                'description' => 'Khóa Học Python Chuyên Sâu Cho Data Science. Học Pandas, NumPy, Matplotlib Và Machine Learning.',
                'price' => 0,
                'level' => 'beginner',
                'category_id' => 3,
                'status' => 'published',
                'duration_hours' => 30,
                'requirements' => 'Không Cần Kiến Thức Trước',
                'what_you_learn' => 'Python Basics, Data Analysis, Visualization, Machine Learning Algorithms',
            ],
        ];

        foreach ($courses as $courseData) {
            $course = Course::create($courseData);

            // Tạo Chapters cho mỗi Course
            $chapters = [
                [
                    'title' => 'Giới Thiệu Và Cài Đặt',
                    'description' => 'Tìm Hiểu Về Framework Và Cách Cài Đặt Môi Trường',
                    'position' => 1,
                ],
                [
                    'title' => 'Kiến Thức Cơ Bản',
                    'description' => 'Học Các Khái Niệm Cơ Bản Và Cấu Trúc Dự Án',
                    'position' => 2,
                ],
                [
                    'title' => 'Thực Hành Dự Án',
                    'description' => 'Xây Dựng Dự Án Thực Tế Từ A Đến Z',
                    'position' => 3,
                ],
            ];

            foreach ($chapters as $chapterData) {
                $chapterData['course_id'] = $course->id;
                $chapter = Chapter::create($chapterData);

                // Tạo Lessons cho mỗi Chapter
                $lessons = [
                    [
                        'title' => 'Bài 1: Giới Thiệu Tổng Quan',
                        'type' => 'video',
                        'content' => 'Trong Bài Học Này, Chúng Ta Sẽ Tìm Hiểu Tổng Quan Về Framework Và Lợi Ích Của Việc Sử Dụng Nó.',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'duration_minutes' => 15,
                        'position' => 1,
                        'is_preview' => true,
                    ],
                    [
                        'title' => 'Bài 2: Cài Đặt Môi Trường',
                        'type' => 'video',
                        'content' => 'Hướng Dẫn Chi Tiết Cách Cài Đặt Và Cấu Hình Môi Trường Phát Triển.',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'duration_minutes' => 20,
                        'position' => 2,
                        'is_preview' => false,
                    ],
                    [
                        'title' => 'Bài 3: Tài Liệu Tham Khảo',
                        'type' => 'pdf',
                        'content' => 'Tài Liệu PDF Chứa Các Kiến Thức Quan Trọng Và Tham Khảo.',
                        'duration_minutes' => 10,
                        'position' => 3,
                        'is_preview' => true,
                    ],
                ];

                foreach ($lessons as $lessonData) {
                    $lessonData['chapter_id'] = $chapter->id;
                    Lesson::create($lessonData);
                }
            }
        }
    }
}
