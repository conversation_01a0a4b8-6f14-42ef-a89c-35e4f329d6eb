@extends('layouts.user')

@section('title', 'Tất <PERSON> - <PERSON>')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Tất <PERSON> H<PERSON></h4>
        <p class="text-muted mb-0">Khám Phá Các Khóa Học Chất Lượng Cao</p>
      </div>
      <div>
        <span class="badge bg-primary fs-3">{{ $courses->total() }} Khóa Học</span>
      </div>
    </div>
  </div>
</div>

<!-- Bộ Lọc -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" action="{{ route('courses.index') }}">
          <div class="row">
            <div class="col-md-4 mb-3">
              <label class="form-label">T<PERSON><PERSON></label>
              <input type="text" class="form-control" name="search" value="{{ request('search') }}" placeholder="Nhập Từ Khóa...">
            </div>
            
            <div class="col-md-3 mb-3">
              <label class="form-label">Danh Mục</label>
              <select class="form-select" name="category">
                <option value="">Tất Cả Danh Mục</option>
                @foreach($categories as $category)
                <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                  {{ $category->name }}
                </option>
                @endforeach
              </select>
            </div>
            
            <div class="col-md-3 mb-3">
              <label class="form-label">Cấp Độ</label>
              <select class="form-select" name="level">
                <option value="">Tất Cả Cấp Độ</option>
                <option value="beginner" {{ request('level') == 'beginner' ? 'selected' : '' }}>Người Mới Bắt Đầu</option>
                <option value="intermediate" {{ request('level') == 'intermediate' ? 'selected' : '' }}>Trung Cấp</option>
                <option value="advanced" {{ request('level') == 'advanced' ? 'selected' : '' }}>Nâng Cao</option>
              </select>
            </div>
            
            <div class="col-md-2 mb-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-search me-2"></i>Lọc
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Danh Sách Khóa Học -->
<div class="row">
  @forelse($courses as $course)
  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      @if($course->image_url)
      <img src="{{ $course->image_url }}"
           class="card-img-top"
           alt="{{ $course->title }}"
           style="height: 200px; object-fit: cover;"
           onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
      <div class="card-img-top bg-light align-items-center justify-content-center"
           style="height: 200px; display: none;">
        <div class="text-center">
          <i class="fa fa-book fa-3x text-muted mb-2"></i>
          <div class="small text-muted">Không Có Ảnh</div>
        </div>
      </div>
      @else
      <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
           style="height: 200px;">
        <div class="text-center">
          <i class="fa fa-book fa-3x text-muted mb-2"></i>
          <div class="small text-muted">Không Có Ảnh</div>
        </div>
      </div>
      @endif
      
      <div class="card-body d-flex flex-column">
        <div class="mb-2">
          <span class="badge bg-primary">{{ $course->category->name }}</span>
          <span class="badge bg-secondary ms-1">
            @if($course->level == 'beginner') Người Mới
            @elseif($course->level == 'intermediate') Trung Cấp
            @else Nâng Cao
            @endif
          </span>

          @if(isset($userCourses[$course->id]))
          @php $userCourse = $userCourses[$course->id]; @endphp
          @if($userCourse->completed_at)
          <span class="badge bg-success ms-1">
            <i class="fa fa-check-circle me-1"></i>Đã Hoàn Thành
          </span>
          @elseif($userCourse->started_at)
          <span class="badge bg-warning ms-1">
            <i class="fa fa-play me-1"></i>Đang Học ({{ $userCourse->progress_percentage }}%)
          </span>
          @else
          <span class="badge bg-info ms-1">
            <i class="fa fa-bookmark me-1"></i>Đã Đăng Ký
          </span>
          @endif
          @endif
        </div>
        
        <h5 class="card-title">{{ $course->title }}</h5>
        <p class="card-text text-muted flex-grow-1">{{ Str::limit($course->description, 100) }}</p>
        
        <div class="mt-auto">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted small">
              <i class="fa fa-clock me-1"></i>{{ $course->duration_hours }}h
              <i class="fa fa-play-circle ms-2 me-1"></i>{{ $course->total_lessons }} Bài
            </div>
            <div class="fw-bold text-primary">
              @if($course->price > 0)
              {{ number_format($course->price, 0, ',', '.') }}đ
              @else
              Miễn Phí
              @endif
            </div>
          </div>
          
          @if(isset($userCourses[$course->id]))
          @php $userCourse = $userCourses[$course->id]; @endphp
          @if($userCourse->completed_at)
          <a href="{{ route('my-courses.show', $course->id) }}" class="btn btn-success w-100">
            <i class="fa fa-certificate me-2"></i>Xem Chứng Chỉ
          </a>
          @elseif($userCourse->started_at)
          @php
            $nextLesson = $userCourse->getNextLesson() ?? $userCourse->currentLesson;
            if (!$nextLesson) {
              $firstChapter = $userCourse->course->chapters->sortBy('position')->first();
              $nextLesson = $firstChapter ? $firstChapter->lessons->sortBy('position')->first() : null;
            }
          @endphp
          @if($nextLesson)
          <a href="{{ route('user.courses.lessons.show', ['course' => $course->id, 'chapter' => $nextLesson->chapter_id, 'lesson' => $nextLesson->id]) }}" class="btn btn-warning w-100">
            <i class="fa fa-play me-2"></i>Tiếp Tục Học
          </a>
          @else
          <a href="{{ route('my-courses.show', $course->id) }}" class="btn btn-outline-primary w-100">
            <i class="fa fa-eye me-2"></i>Xem Chi Tiết
          </a>
          @endif
          @else
          <a href="{{ route('my-courses.show', $course->id) }}" class="btn btn-primary w-100">
            <i class="fa fa-play me-2"></i>Bắt Đầu Học
          </a>
          @endif
          @else
          <a href="{{ route('courses.show', $course->slug) }}" class="btn btn-primary w-100">
            <i class="fa fa-code me-2"></i>Xem Chi Tiết
          </a>
          @endif
        </div>
      </div>
    </div>
  </div>
  @empty
  <div class="col-12">
    <div class="text-center py-5">
      <i class="fa fa-search fa-4x text-muted mb-3"></i>
      <h5 class="text-muted mb-3">Không Tìm Thấy Khóa Học</h5>
      <p class="text-muted mb-4">Thử Thay Đổi Bộ Lọc Hoặc Từ Khóa Tìm Kiếm</p>
      <a href="{{ route('courses.index') }}" class="btn btn-primary">
        <i class="fa fa-code me-2"></i>Xem Tất Cả Khóa Học
      </a>
    </div>
  </div>
  @endforelse
</div>

<!-- Phân Trang -->
@if($courses->hasPages())
<div class="row mt-4">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      {{ $courses->appends(request()->query())->links() }}
    </div>
  </div>
</div>
@endif

<!-- Thống Kê -->
<div class="row mt-5">
  <div class="col-12">
    <div class="card bg-light">
      <div class="card-body text-center">
        <div class="row">
          <div class="col-md-3">
            <h4 class="text-primary mb-0">{{ $courses->total() }}</h4>
            <small class="text-muted">Tổng Khóa Học</small>
          </div>
          <div class="col-md-3">
            <h4 class="text-success mb-0">{{ $categories->count() }}</h4>
            <small class="text-muted">Danh Mục</small>
          </div>
          <div class="col-md-3">
            <h4 class="text-warning mb-0">0</h4>
            <small class="text-muted">Học Viên</small>
          </div>
          <div class="col-md-3">
            <h4 class="text-info mb-0">0</h4>
            <small class="text-muted">Giờ Học</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
