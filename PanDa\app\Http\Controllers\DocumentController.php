<?php

namespace App\Http\Controllers;

use App\Models\UserCourse;
use App\Models\Lesson;
use Illuminate\Http\Request;

class DocumentController extends Controller
{
    /**
     * Hiển Thị Trang Tài Liệu
     */
    public function index(Request $request)
    {
        $query = auth()->user()->userCourses()
            ->with(['course.category', 'course.chapters.lessons']);

        // Lọc theo khóa học
        if ($request->filled('course')) {
            $query->where('course_id', $request->course);
        }

        // Lọc theo loại tài liệu
        if ($request->filled('type')) {
            $query->whereHas('course.chapters.lessons', function ($lessonQuery) use ($request) {
                $lessonQuery->where('type', $request->type);
            });
        }

        $userCourses = $query->get();

        // Lấy tất cả tài liệu từ các khóa học đã mua
        $documents = collect();

        foreach ($userCourses as $userCourse) {
            foreach ($userCourse->course->chapters as $chapter) {
                foreach ($chapter->lessons as $lesson) {
                    // Chỉ lấy lessons có tài liệu đính kèm
                    if ($lesson->type === 'pdf' || $lesson->attachment_url) {
                        $documents->push([
                            'lesson' => $lesson,
                            'course' => $userCourse->course,
                            'chapter' => $chapter,
                            'user_course' => $userCourse,
                            'is_completed' => $userCourse->isLessonCompleted($lesson->id),
                        ]);
                    }
                }
            }
        }

        // Lọc theo loại nếu có
        if ($request->filled('type')) {
            $documents = $documents->filter(function ($doc) use ($request) {
                return $doc['lesson']->type === $request->type;
            });
        }

        // Sắp xếp
        $sortBy = $request->get('sort', 'recent');
        switch ($sortBy) {
            case 'name':
                $documents = $documents->sortBy('lesson.title');
                break;
            case 'course':
                $documents = $documents->sortBy('course.title');
                break;
            default:
                $documents = $documents->sortByDesc('lesson.created_at');
        }

        // Phân trang thủ công
        $perPage = 12;
        $currentPage = $request->get('page', 1);
        $documentsCollection = $documents->forPage($currentPage, $perPage);

        // Lấy danh sách khóa học để filter
        $courses = auth()->user()->userCourses()->with('course')->get()->pluck('course');

        return view('user.documents.index', compact('documentsCollection', 'courses', 'documents'));
    }

    /**
     * Tải Tài Liệu
     */
    public function download($lessonId)
    {
        $lesson = Lesson::findOrFail($lessonId);

        // Kiểm tra user có quyền truy cập
        $userCourse = auth()->user()->userCourses()
            ->where('course_id', $lesson->chapter->course_id)
            ->first();

        if (!$userCourse) {
            abort(403, 'Bạn Chưa Đăng Ký Khóa Học Này');
        }

        // Kiểm tra có file đính kèm
        if (!$lesson->attachment_url) {
            abort(404, 'Không Tìm Thấy Tài Liệu');
        }

        $filePath = storage_path('app/public/' . $lesson->attachment_url);

        if (!file_exists($filePath)) {
            abort(404, 'File Không Tồn Tại');
        }

        return response()->download($filePath, $lesson->title . '.pdf');
    }

    /**
     * Xem Tài Liệu Online
     */
    public function view($lessonId)
    {
        $lesson = Lesson::with(['chapter.course'])->findOrFail($lessonId);

        // Kiểm tra user có quyền truy cập
        $userCourse = auth()->user()->userCourses()
            ->where('course_id', $lesson->chapter->course_id)
            ->first();

        if (!$userCourse) {
            abort(403, 'Bạn Chưa Đăng Ký Khóa Học Này');
        }

        return view('user.documents.view', compact('lesson', 'userCourse'));
    }

    /**
     * Lấy Tài Liệu Theo Khóa Học (API)
     */
    public function getByCourse($courseId)
    {
        $userCourse = auth()->user()->userCourses()
            ->where('course_id', $courseId)
            ->first();

        if (!$userCourse) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $documents = Lesson::whereHas('chapter', function ($query) use ($courseId) {
                $query->where('course_id', $courseId);
            })
            ->where(function ($query) {
                $query->where('type', 'pdf')
                      ->orWhereNotNull('attachment_url');
            })
            ->with(['chapter'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'documents' => $documents->map(function ($lesson) use ($userCourse) {
                return [
                    'id' => $lesson->id,
                    'title' => $lesson->title,
                    'type' => $lesson->type,
                    'chapter' => $lesson->chapter->title,
                    'is_completed' => $userCourse->isLessonCompleted($lesson->id),
                    'download_url' => route('user.documents.download', $lesson->id),
                    'view_url' => route('user.documents.view', $lesson->id),
                ];
            })
        ]);
    }
}
