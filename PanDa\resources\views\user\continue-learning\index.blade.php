@extends('layouts.user')

@section('title', 'Tiế<PERSON>')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Tiếp <PERSON></h4>
        <p class="text-muted mb-0">Theo <PERSON><PERSON> Tiếp Tục H<PERSON> Trình H<PERSON> Tậ<PERSON></p>
      </div>
      <div>
        @if($inProgressCourses->count() > 0)
        @php
          $recentCourse = $inProgressCourses->first();
          $nextLesson = $recentCourse->getNextLesson() ?? $recentCourse->currentLesson;
          if (!$nextLesson) {
            $firstChapter = $recentCourse->course->chapters->sortBy('position')->first();
            $nextLesson = $firstChapter ? $firstChapter->lessons->sortBy('position')->first() : null;
          }
        @endphp
        @if($nextLesson)
        <a href="{{ route('user.courses.lessons.show', ['course' => $recentCourse->course_id, 'chapter' => $nextLesson->chapter_id, 'lesson' => $nextLesson->id]) }}" class="btn btn-primary">
          <i class="fa fa-play me-2"></i>Tiếp Tục Học
        </a>
        @endif
        @endif
      </div>
    </div>
  </div>
</div>

<!-- Thống Kê Tổng Quan -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <i class="fa fa-graduation-cap fa-2x mb-2"></i>
        <h4 class="mb-0">{{ $stats['total_courses'] }}</h4>
        <small>Tổng Khóa Học</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-warning text-white">
      <div class="card-body text-center">
        <i class="fa fa-clock fa-2x mb-2"></i>
        <h4 class="mb-0">{{ $stats['in_progress'] }}</h4>
        <small>Đang Học</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <i class="fa fa-check-circle fa-2x mb-2"></i>
        <h4 class="mb-0">{{ $stats['completed'] }}</h4>
        <small>Hoàn Thành</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <i class="fa fa-chart-line fa-2x mb-2"></i>
        <h4 class="mb-0">{{ round($stats['total_progress']) }}%</h4>
        <small>Tiến Độ TB</small>
      </div>
    </div>
  </div>
</div>

@if($inProgressCourses->count() > 0)
<!-- Khóa Học Đang Học -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-play-circle me-2"></i>Đang Học ({{ $inProgressCourses->count() }})
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          @foreach($inProgressCourses as $userCourse)
          <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                  <h6 class="card-title mb-0">{{ Str::limit($userCourse->course->title, 40) }}</h6>
                  <span class="badge bg-warning">{{ $userCourse->progress_percentage }}%</span>
                </div>
                
                <p class="card-text text-muted small mb-2">{{ $userCourse->course->category->name }}</p>
                
                <!-- Tiến Độ -->
                <div class="progress mb-2" style="height: 6px;">
                  <div class="progress-bar bg-warning" style="width: {{ $userCourse->progress_percentage }}%"></div>
                </div>
                
                <!-- Bài Học Hiện Tại -->
                @if($userCourse->currentLesson)
                <small class="text-muted d-block mb-2">
                  <i class="fa fa-bookmark me-1"></i>
                  {{ $userCourse->currentLesson->title }}
                </small>
                @endif
                
                <!-- Thời Gian -->
                <small class="text-muted d-block mb-3">
                  <i class="fa fa-clock me-1"></i>
                  {{ $userCourse->last_accessed_at->diffForHumans() }}
                </small>
                
                <div class="d-grid">
                  @php
                    $nextLesson = $userCourse->getNextLesson() ?? $userCourse->currentLesson;
                    if (!$nextLesson) {
                      $firstChapter = $userCourse->course->chapters->sortBy('position')->first();
                      $nextLesson = $firstChapter ? $firstChapter->lessons->sortBy('position')->first() : null;
                    }
                  @endphp
                  @if($nextLesson)
                  <a href="{{ route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $nextLesson->chapter_id, 'lesson' => $nextLesson->id]) }}" class="btn btn-warning btn-sm">
                    <i class="fa fa-play me-1"></i>Tiếp Tục
                  </a>
                  @else
                  <button class="btn btn-secondary btn-sm" disabled>
                    <i class="fa fa-check-circle me-1"></i>Hoàn Thành
                  </button>
                  @endif
                </div>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
</div>
@endif

@if($recentCourses->count() > 0)
<!-- Khóa Học Gần Đây -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-history me-2"></i>Học Gần Đây
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          @foreach($recentCourses->take(4) as $userCourse)
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-light">
              <div class="card-body p-3">
                <h6 class="card-title mb-1">{{ Str::limit($userCourse->course->title, 30) }}</h6>
                <small class="text-muted d-block mb-2">{{ $userCourse->course->category->name }}</small>
                
                <div class="d-flex justify-content-between align-items-center">
                  <small class="text-muted">{{ $userCourse->progress_percentage }}%</small>
                  <a href="{{ route('my-courses.show', $userCourse->course_id) }}" class="btn btn-outline-primary btn-sm">
                    Xem
                  </a>
                </div>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
</div>
@endif

@if($notStartedCourses->count() > 0)
<!-- Khóa Học Chưa Bắt Đầu -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-plus-circle me-2"></i>Chưa Bắt Đầu ({{ $notStartedCourses->count() }})
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          @foreach($notStartedCourses as $userCourse)
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border">
              <div class="card-body">
                <h6 class="card-title">{{ Str::limit($userCourse->course->title, 35) }}</h6>
                <p class="card-text text-muted small">{{ $userCourse->course->category->name }}</p>
                
                <div class="mb-2">
                  <small class="text-muted">
                    <i class="fa fa-calendar me-1"></i>
                    Đăng Ký: {{ $userCourse->enrolled_at->format('d/m/Y') }}
                  </small>
                </div>
                
                <div class="d-grid">
                  @php
                    $firstChapter = $userCourse->course->chapters->sortBy('position')->first();
                    $firstLesson = $firstChapter ? $firstChapter->lessons->sortBy('position')->first() : null;
                  @endphp
                  @if($firstLesson)
                  <a href="{{ route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $firstLesson->chapter_id, 'lesson' => $firstLesson->id]) }}" class="btn btn-success btn-sm w-100">
                    <i class="fa fa-play me-1"></i>Bắt Đầu
                  </a>
                  @else
                  <button class="btn btn-secondary btn-sm w-100" disabled>
                    <i class="fa fa-exclamation-triangle me-1"></i>Chưa Có Bài Học
                  </button>
                  @endif
                </div>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
</div>
@endif

<!-- Mục Tiêu Học Tập -->
<div class="row mb-4">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-target me-2"></i>Mục Tiêu Học Tập
        </h6>
      </div>
      <div class="card-body">
        <form id="goalForm">
          <div class="mb-3">
            <label class="form-label">Học Mỗi Ngày (phút)</label>
            <input type="number" class="form-control" name="daily_minutes" value="{{ session('learning_goal_daily_minutes', 30) }}" min="5" max="480">
          </div>
          <div class="mb-3">
            <label class="form-label">Bài Học Mỗi Tuần</label>
            <input type="number" class="form-control" name="weekly_lessons" value="{{ session('learning_goal_weekly_lessons', 5) }}" min="1" max="50">
          </div>
          <button type="submit" class="btn btn-primary btn-sm">
            <i class="fa fa-save me-1"></i>Lưu Mục Tiêu
          </button>
        </form>
      </div>
    </div>
  </div>
  
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-chart-bar me-2"></i>Thống Kê Tuần Này
        </h6>
      </div>
      <div class="card-body" id="weeklyStats">
        <div class="text-center">
          <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">Đang Tải...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

@if($stats['total_courses'] == 0)
<!-- Trống -->
<div class="row">
  <div class="col-12">
    <div class="text-center py-5">
      <i class="fa fa-graduation-cap fa-4x text-muted mb-4"></i>
      <h4 class="text-muted mb-3">Chưa Có Khóa Học Nào</h4>
      <p class="text-muted mb-4">Hãy Bắt Đầu Hành Trình Học Tập Của Bạn</p>
      <a href="{{ route('courses.index') }}" class="btn btn-primary">
        <i class="fa fa-search me-2"></i>Khám Phá Khóa Học
      </a>
    </div>
  </div>
</div>
@endif
@endsection

@push('scripts')
<script>
// Load weekly stats
document.addEventListener('DOMContentLoaded', function() {
    loadWeeklyStats();
});

// Goal form submission
document.getElementById('goalForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{{ route("continue-learning.update-goal") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Cập Nhật Mục Tiêu Thành Công!');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
});

function loadWeeklyStats() {
    fetch('{{ route("continue-learning.stats") }}')
        .then(response => response.json())
        .then(data => {
            document.getElementById('weeklyStats').innerHTML = `
                <div class="row text-center">
                    <div class="col-4">
                        <h5 class="mb-0">${data.this_week.lessons_completed}</h5>
                        <small class="text-muted">Bài Học</small>
                    </div>
                    <div class="col-4">
                        <h5 class="mb-0">${data.streak}</h5>
                        <small class="text-muted">Ngày Liên Tiếp</small>
                    </div>
                    <div class="col-4">
                        <h5 class="mb-0">${Math.round(data.total_progress)}%</h5>
                        <small class="text-muted">Tiến Độ TB</small>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            document.getElementById('weeklyStats').innerHTML = '<p class="text-muted mb-0">Không thể tải thống kê</p>';
        });
}
</script>
@endpush
