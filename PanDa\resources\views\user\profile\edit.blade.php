@extends('layouts.user')

@section('title', 'Chỉnh <PERSON><PERSON><PERSON>')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Chỉnh <PERSON><PERSON><PERSON>ơ</h4>
        <p class="text-muted mb-0">Cậ<PERSON>t Thông Tin Cá Nhân Của Bạn</p>
      </div>
      <div>
        <a href="{{ route('profile.index') }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-8">
    <!-- Form Thông Tin Cá Nhân -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-user me-2"></i>Thông Tin Cá Nhân
        </h6>
      </div>
      <div class="card-body">
        <form method="POST" action="{{ route('profile.update') }}" enctype="multipart/form-data">
          @csrf
          @method('PUT')
          
          <!-- Avatar -->
          <div class="row mb-4">
            <div class="col-md-3 text-center">
              <div class="mb-3">
                @if($user->avatar)
                <img src="{{ asset('storage/' . $user->avatar) }}" alt="Avatar" class="rounded-circle" width="120" height="120" style="object-fit: cover;" id="avatarPreview">
                @else
                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 120px; height: 120px;" id="avatarPlaceholder">
                  <i class="fa fa-user fa-3x text-white"></i>
                </div>
                <img src="" alt="Avatar" class="rounded-circle d-none" width="120" height="120" style="object-fit: cover;" id="avatarPreview">
                @endif
              </div>
              
              <div class="mb-2">
                <label for="avatar" class="btn btn-outline-primary btn-sm">
                  <i class="fa fa-camera me-1"></i>Chọn Ảnh
                </label>
                <input type="file" id="avatar" name="avatar" class="d-none" accept="image/*">
              </div>
              
              @if($user->avatar)
              <div>
                <form method="POST" action="{{ route('profile.delete-avatar') }}" class="d-inline">
                  @csrf
                  @method('DELETE')
                  <button type="submit" class="btn btn-outline-danger btn-sm" onclick="return confirm('Bạn có chắc muốn xóa avatar?')">
                    <i class="fa fa-trash me-1"></i>Xóa
                  </button>
                </form>
              </div>
              @endif
            </div>
            
            <div class="col-md-9">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">Họ Tên <span class="text-danger">*</span></label>
                  <input type="text" class="form-control @error('name') is-invalid @enderror" name="name" value="{{ old('name', $user->name) }}" required>
                  @error('name')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                  <label class="form-label">Email <span class="text-danger">*</span></label>
                  <input type="email" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email', $user->email) }}" required>
                  @error('email')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                  <label class="form-label">Số Điện Thoại</label>
                  <input type="text" class="form-control @error('phone') is-invalid @enderror" name="phone" value="{{ old('phone', $user->phone) }}">
                  @error('phone')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                </div>
                
                <div class="col-12 mb-3">
                  <label class="form-label">Giới Thiệu Bản Thân</label>
                  <textarea class="form-control @error('bio') is-invalid @enderror" name="bio" rows="3" placeholder="Viết vài dòng về bản thân...">{{ old('bio', $user->bio) }}</textarea>
                  @error('bio')
                  <div class="invalid-feedback">{{ $message }}</div>
                  @enderror
                  <small class="text-muted">Tối đa 500 ký tự</small>
                </div>
              </div>
            </div>
          </div>
          
          <div class="text-end">
            <button type="submit" class="btn btn-primary">
              <i class="fa fa-save me-2"></i>Lưu Thay Đổi
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  
  <div class="col-lg-4">
    <!-- Form Đổi Mật Khẩu -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-lock me-2"></i>Đổi Mật Khẩu
        </h6>
      </div>
      <div class="card-body">
        <form method="POST" action="{{ route('profile.change-password') }}">
          @csrf
          
          <div class="mb-3">
            <label class="form-label">Mật Khẩu Hiện Tại <span class="text-danger">*</span></label>
            <input type="password" class="form-control @error('current_password') is-invalid @enderror" name="current_password" required>
            @error('current_password')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          
          <div class="mb-3">
            <label class="form-label">Mật Khẩu Mới <span class="text-danger">*</span></label>
            <input type="password" class="form-control @error('password') is-invalid @enderror" name="password" required>
            @error('password')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <small class="text-muted">Ít nhất 8 ký tự</small>
          </div>
          
          <div class="mb-3">
            <label class="form-label">Xác Nhận Mật Khẩu Mới <span class="text-danger">*</span></label>
            <input type="password" class="form-control" name="password_confirmation" required>
          </div>
          
          <div class="d-grid">
            <button type="submit" class="btn btn-warning">
              <i class="fa fa-key me-2"></i>Đổi Mật Khẩu
            </button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- Thông Tin Tài Khoản -->
    <div class="card mt-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-info-circle me-2"></i>Thông Tin Tài Khoản
        </h6>
      </div>
      <div class="card-body">
        <div class="mb-2">
          <strong>ID Tài Khoản:</strong>
          <span class="text-muted">#{{ $user->id }}</span>
        </div>
        
        <div class="mb-2">
          <strong>Ngày Tham Gia:</strong>
          <span class="text-muted">{{ $user->created_at->format('d/m/Y H:i') }}</span>
        </div>
        
        <div class="mb-2">
          <strong>Cập Nhật Cuối:</strong>
          <span class="text-muted">{{ $user->updated_at->format('d/m/Y H:i') }}</span>
        </div>
        
        <div class="mb-2">
          <strong>Trạng Thái Email:</strong>
          @if($user->email_verified_at)
          <span class="badge bg-success">Đã Xác Thực</span>
          @else
          <span class="badge bg-warning">Chưa Xác Thực</span>
          @endif
        </div>
        
        <div class="mb-3">
          <strong>Vai Trò:</strong>
          <span class="badge bg-primary">{{ ucfirst($user->role) }}</span>
        </div>
        
        <div class="d-grid">
          <a href="{{ route('profile.export') }}" class="btn btn-outline-info btn-sm">
            <i class="fa fa-download me-1"></i>Xuất Dữ Liệu
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection

@push('scripts')
<script>
// Preview avatar
document.getElementById('avatar').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('avatarPreview');
            const placeholder = document.getElementById('avatarPlaceholder');
            
            preview.src = e.target.result;
            preview.classList.remove('d-none');
            
            if (placeholder) {
                placeholder.classList.add('d-none');
            }
        };
        reader.readAsDataURL(file);
    }
});

// Character counter for bio
const bioTextarea = document.querySelector('textarea[name="bio"]');
if (bioTextarea) {
    const maxLength = 500;
    const counter = document.createElement('small');
    counter.className = 'text-muted float-end';
    bioTextarea.parentNode.appendChild(counter);
    
    function updateCounter() {
        const remaining = maxLength - bioTextarea.value.length;
        counter.textContent = `${remaining} ký tự còn lại`;
        counter.className = remaining < 50 ? 'text-warning float-end' : 'text-muted float-end';
    }
    
    bioTextarea.addEventListener('input', updateCounter);
    updateCounter();
}
</script>
@endpush
