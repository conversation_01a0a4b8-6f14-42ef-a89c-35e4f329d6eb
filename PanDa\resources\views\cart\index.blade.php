@extends('layouts.user')

@section('title', 'Giỏ Hàng - H<PERSON> Thống H<PERSON>c Tập Online')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Giỏ Hàng Của Tôi</h4>
        <p class="text-muted mb-0">Quản Lý Các Khóa Học Bạn Muốn Mua</p>
      </div>
      <div>
        <span class="badge bg-primary fs-3">{{ $cartItems->count() }} Khó<PERSON> Họ<PERSON></span>
      </div>
    </div>
  </div>
</div>

@if($cartItems->count() > 0)
<!-- Danh Sách Khóa Học Trong Giỏ -->
<div class="row">
  <div class="col-lg-8">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i><PERSON><PERSON>ch <PERSON>
        </h5>
      </div>
      <div class="card-body p-0">
        @foreach($cartItems as $item)
        <div class="border-bottom p-4">
          <div class="row align-items-center">
            <div class="col-md-3">
              @if($item->course->image)
              <img src="{{ asset('storage/' . $item->course->image) }}" class="img-fluid rounded" alt="{{ $item->course->title }}" style="height: 100px; object-fit: cover;">
              @else
              <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 100px;">
                <i class="fa fa-book fa-2x text-muted"></i>
              </div>
              @endif
            </div>
            
            <div class="col-md-6">
              <div class="mb-2">
                <span class="badge bg-primary">{{ $item->course->category->name }}</span>
                <span class="badge bg-secondary ms-1">
                  @if($item->course->level == 'beginner') Người Mới
                  @elseif($item->course->level == 'intermediate') Trung Cấp
                  @else Nâng Cao
                  @endif
                </span>
              </div>
              
              <h6 class="mb-2">
                <a href="{{ route('courses.show', $item->course->slug) }}" class="text-decoration-none">
                  {{ $item->course->title }}
                </a>
              </h6>
              
              <p class="text-muted mb-2 small">{{ Str::limit($item->course->description, 100) }}</p>
              
              <div class="text-muted small">
                <i class="fa fa-clock me-1"></i>{{ $item->course->duration_hours }} Giờ
                <i class="fa fa-play-circle ms-2 me-1"></i>{{ $item->course->total_lessons }} Bài
              </div>
            </div>
            
            <div class="col-md-3 text-end">
              <div class="mb-3">
                <h5 class="text-primary mb-0">
                  @if($item->course->price > 0)
                  {{ number_format($item->course->price, 0, ',', '.') }}đ
                  @else
                  Miễn Phí
                  @endif
                </h5>
              </div>
              
              <div class="d-grid gap-2">
                <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#noteModal{{ $item->id }}">
                  <i class="fa fa-edit me-1"></i>Ghi Chú
                </button>
                
                <form method="POST" action="{{ route('cart.destroy', $item->id) }}" class="d-inline">
                  @csrf
                  @method('DELETE')
                  <button type="submit" class="btn btn-outline-danger btn-sm w-100" onclick="return confirm('Bạn Có Chắc Muốn Xóa?')">
                    <i class="fa fa-trash me-1"></i>Xóa
                  </button>
                </form>
              </div>
            </div>
          </div>
          
          @if($item->note)
          <div class="row mt-3">
            <div class="col-12">
              <div class="alert alert-info mb-0">
                <strong>Ghi Chú:</strong> {{ $item->note }}
              </div>
            </div>
          </div>
          @endif
        </div>

        <!-- Modal Ghi Chú -->
        <div class="modal fade" id="noteModal{{ $item->id }}" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <form method="POST" action="{{ route('cart.update', $item->id) }}">
                @csrf
                @method('PUT')
                <div class="modal-header">
                  <h5 class="modal-title">Ghi Chú Cho Khóa Học</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <div class="mb-3">
                    <label class="form-label">Khóa Học:</label>
                    <p class="fw-bold">{{ $item->course->title }}</p>
                  </div>
                  <div class="mb-3">
                    <label for="note{{ $item->id }}" class="form-label">Ghi Chú</label>
                    <textarea class="form-control" id="note{{ $item->id }}" name="note" rows="4" placeholder="Nhập Ghi Chú Của Bạn...">{{ $item->note }}</textarea>
                    <div class="form-text">Tối Đa 500 Ký Tự</div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                  <button type="submit" class="btn btn-primary">
                    <i class="fa fa-save me-2"></i>Lưu Ghi Chú
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
        @endforeach
      </div>
    </div>
  </div>

  <!-- Sidebar Tổng Kết -->
  <div class="col-lg-4">
    <div class="card sticky-top" style="top: 20px;">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Tổng Kết Đơn Hàng
        </h5>
      </div>
      <div class="card-body">
        <div class="d-flex justify-content-between mb-3">
          <span>Số Lượng Khóa Học:</span>
          <span class="fw-bold">{{ $cartItems->count() }}</span>
        </div>
        
        <div class="d-flex justify-content-between mb-3">
          <span>Tạm Tính:</span>
          <span class="fw-bold">{{ number_format($total, 0, ',', '.') }}đ</span>
        </div>
        
        <hr>
        
        <div class="d-flex justify-content-between mb-4">
          <span class="h6">Tổng Cộng:</span>
          <span class="h5 text-primary fw-bold">{{ number_format($total, 0, ',', '.') }}đ</span>
        </div>
        
        <div class="d-grid gap-2">
          <button type="button" class="btn btn-primary btn-lg w-100" data-bs-toggle="modal" data-bs-target="#orderModal">
            <i class="fa fa-paper-plane me-2"></i>Gửi Yêu Cầu Mua Hàng
          </button>
          
          <a href="{{ route('courses.index') }}" class="btn btn-outline-primary">
            <i class="fa fa-plus me-2"></i>Thêm Khóa Học Khác
          </a>
        </div>
        
        <hr>
        
        <div class="text-center">
          <h6>Lưu Ý:</h6>
          <ul class="list-unstyled text-muted small">
            <li><i class="fa fa-check text-success me-2"></i>Thanh Toán Thủ Công</li>
            <li><i class="fa fa-check text-success me-2"></i>Hỗ Trợ 24/7</li>
            <li><i class="fa fa-check text-success me-2"></i>Truy Cập Trọn Đời</li>
            <li><i class="fa fa-check text-success me-2"></i>Chứng Chỉ Hoàn Thành</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

@else
<!-- Giỏ Hàng Trống -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-body text-center py-5">
        <i class="fa fa-shopping-cart fa-4x text-muted mb-4"></i>
        <h4 class="text-muted mb-3">Giỏ Hàng Trống</h4>
        <p class="text-muted mb-4">Bạn Chưa Thêm Khóa Học Nào Vào Giỏ Hàng</p>
        <a href="{{ route('courses.index') }}" class="btn btn-primary">
          <i class="fa fa-code me-2"></i>Khám Phá Khóa Học
        </a>
      </div>
    </div>
  </div>
</div>
@endif

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mt-4">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Trang Chủ</a></li>
    <li class="breadcrumb-item active" aria-current="page">Giỏ Hàng</li>
  </ol>
</nav>
<!-- Modal Gửi Yêu Cầu Mua Hàng -->
<div class="modal fade" id="orderModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fa fa-paper-plane me-2"></i>Gửi Yêu Cầu Mua Hàng
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>

      <form method="POST" action="{{ route('orders.store') }}">
        @csrf
        <div class="modal-body">
          <div class="mb-4">
            <h6>Thông Tin Đơn Hàng</h6>
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Khóa Học</th>
                    <th>Giá</th>
                  </tr>
                </thead>
                <tbody>
                  @foreach($cartItems as $item)
                  <tr>
                    <td>{{ $item->course->title }}</td>
                    <td class="fw-bold text-success">
                      @if($item->course->price > 0)
                      {{ number_format($item->course->price, 0, ',', '.') }}đ
                      @else
                      Miễn Phí
                      @endif
                    </td>
                  </tr>
                  @endforeach
                </tbody>
                <tfoot>
                  <tr class="table-primary">
                    <th>Tổng Cộng</th>
                    <th class="text-success">{{ number_format($total, 0, ',', '.') }}đ</th>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          <div class="mb-3">
            <label for="payment_method" class="form-label">Phương Thức Thanh Toán <span class="text-danger">*</span></label>
            <select class="form-select @error('payment_method') is-invalid @enderror" id="payment_method" name="payment_method" required>
              <option value="">Chọn Phương Thức</option>
              <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>Chuyển Khoản Ngân Hàng</option>
              <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Tiền Mặt</option>
              <option value="other" {{ old('payment_method') == 'other' ? 'selected' : '' }}>Khác</option>
            </select>
            @error('payment_method')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>

          <div class="mb-3">
            <label for="note" class="form-label">Ghi Chú</label>
            <textarea class="form-control @error('note') is-invalid @enderror" id="note" name="note" rows="4" placeholder="Nhập ghi chú về đơn hàng (tùy chọn)">{{ old('note') }}</textarea>
            @error('note')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Tối đa 1000 ký tự</div>
          </div>

          <div class="alert alert-info">
            <i class="fa fa-info-circle me-2"></i>
            <strong>Lưu ý:</strong> Sau khi gửi yêu cầu, admin sẽ xem xét và phản hồi trong vòng 24 giờ.
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fa fa-times me-2"></i>Hủy
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fa fa-paper-plane me-2"></i>Gửi Yêu Cầu
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

@if($errors->any())
<script>
document.addEventListener('DOMContentLoaded', function() {
  var orderModal = new bootstrap.Modal(document.getElementById('orderModal'));
  orderModal.show();
});
</script>
@endif

@endsection
