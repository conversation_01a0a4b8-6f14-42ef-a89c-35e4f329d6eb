@extends('layouts.admin')

@section('title', 'Cài Đặt SEO')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Cài Đặt SEO</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Cài Đặt</a></li>
            <li class="breadcrumb-item active">SEO</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <form action="{{ route('admin.settings.seo.update') }}" method="POST">
    @csrf
    <div class="row">
      <!-- Meta Tags -->
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-tags me-2"></i>Meta Tags
            </h5>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label class="form-label">SEO Title</label>
              <input type="text" class="form-control @error('seo_title') is-invalid @enderror" 
                     name="seo_title" value="{{ old('seo_title', $settings['seo_title']) }}" 
                     maxlength="60" placeholder="Tiêu đề SEO (tối đa 60 ký tự)">
              <small class="text-muted">Để trống sẽ sử dụng tên website</small>
              @error('seo_title')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="mb-3">
              <label class="form-label">SEO Description</label>
              <textarea class="form-control @error('seo_description') is-invalid @enderror" 
                        name="seo_description" rows="3" maxlength="160" 
                        placeholder="Mô tả SEO (tối đa 160 ký tự)">{{ old('seo_description', $settings['seo_description']) }}</textarea>
              <small class="text-muted">Để trống sẽ sử dụng mô tả website</small>
              @error('seo_description')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="mb-3">
              <label class="form-label">SEO Keywords</label>
              <input type="text" class="form-control @error('seo_keywords') is-invalid @enderror" 
                     name="seo_keywords" value="{{ old('seo_keywords', $settings['seo_keywords']) }}" 
                     placeholder="từ khóa 1, từ khóa 2, từ khóa 3">
              <small class="text-muted">Các từ khóa cách nhau bằng dấu phẩy</small>
              @error('seo_keywords')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>

        <!-- Analytics -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-chart-line me-2"></i>Analytics & Tracking
            </h5>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label class="form-label">Google Analytics ID</label>
              <input type="text" class="form-control @error('google_analytics') is-invalid @enderror" 
                     name="google_analytics" value="{{ old('google_analytics', $settings['google_analytics']) }}" 
                     placeholder="G-XXXXXXXXXX hoặc UA-XXXXXXXX-X">
              @error('google_analytics')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="mb-3">
              <label class="form-label">Google Tag Manager ID</label>
              <input type="text" class="form-control @error('google_tag_manager') is-invalid @enderror" 
                     name="google_tag_manager" value="{{ old('google_tag_manager', $settings['google_tag_manager']) }}" 
                     placeholder="GTM-XXXXXXX">
              @error('google_tag_manager')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="mb-3">
              <label class="form-label">Facebook Pixel ID</label>
              <input type="text" class="form-control @error('facebook_pixel') is-invalid @enderror" 
                     name="facebook_pixel" value="{{ old('facebook_pixel', $settings['facebook_pixel']) }}" 
                     placeholder="123456789012345">
              @error('facebook_pixel')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>

        <!-- Robots & Sitemap -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-robot me-2"></i>Robots.txt & Sitemap
            </h5>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label class="form-label">Robots.txt Content</label>
              <textarea class="form-control @error('robots_txt') is-invalid @enderror" 
                        name="robots_txt" rows="5" 
                        placeholder="User-agent: *&#10;Disallow: /admin&#10;Allow: /&#10;&#10;Sitemap: {{ url('/sitemap.xml') }}">{{ old('robots_txt', $settings['robots_txt']) }}</textarea>
              @error('robots_txt')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="sitemap_enabled" value="1" 
                     {{ old('sitemap_enabled', $settings['sitemap_enabled']) ? 'checked' : '' }}>
              <label class="form-check-label">
                Bật Sitemap Tự Động
              </label>
              <small class="d-block text-muted">Tự động tạo sitemap.xml cho website</small>
            </div>
          </div>
        </div>
      </div>

      <!-- SEO Tools -->
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-tools me-2"></i>SEO Tools
            </h5>
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <a href="https://search.google.com/search-console" target="_blank" class="btn btn-outline-primary">
                <i class="fab fa-google me-2"></i>Google Search Console
              </a>
              <a href="https://www.bing.com/webmasters" target="_blank" class="btn btn-outline-info">
                <i class="fab fa-microsoft me-2"></i>Bing Webmaster Tools
              </a>
              <a href="https://analytics.google.com" target="_blank" class="btn btn-outline-warning">
                <i class="fa fa-chart-bar me-2"></i>Google Analytics
              </a>
              <a href="https://pagespeed.web.dev" target="_blank" class="btn btn-outline-success">
                <i class="fa fa-tachometer-alt me-2"></i>PageSpeed Insights
              </a>
            </div>
          </div>
        </div>

        <!-- SEO Preview -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-eye me-2"></i>Google Preview
            </h5>
          </div>
          <div class="card-body">
            <div class="seo-preview">
              <div class="seo-title text-primary" style="font-size: 18px; line-height: 1.2;">
                {{ $settings['seo_title'] ?: $settings['site_name'] ?: 'Tiêu đề website' }}
              </div>
              <div class="seo-url text-success small">
                {{ url('/') }}
              </div>
              <div class="seo-description text-muted small mt-1">
                {{ $settings['seo_description'] ?: $settings['site_description'] ?: 'Mô tả website sẽ hiển thị ở đây...' }}
              </div>
            </div>
          </div>
        </div>

        <!-- SEO Checklist -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-check-circle me-2"></i>SEO Checklist
            </h5>
          </div>
          <div class="card-body">
            <div class="list-group list-group-flush">
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">Meta Title</span>
                <i class="fa fa-{{ $settings['seo_title'] ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">Meta Description</span>
                <i class="fa fa-{{ $settings['seo_description'] ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">Google Analytics</span>
                <i class="fa fa-{{ $settings['google_analytics'] ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">Sitemap</span>
                <i class="fa fa-{{ $settings['sitemap_enabled'] ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">Robots.txt</span>
                <i class="fa fa-{{ $settings['robots_txt'] ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Submit Buttons -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left me-2"></i>Quay Lại
              </a>
              <div>
                <button type="reset" class="btn btn-outline-secondary me-2">
                  <i class="fa fa-undo me-2"></i>Đặt Lại
                </button>
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-save me-2"></i>Lưu Cài Đặt
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<script>
// Live preview updates
document.querySelector('input[name="seo_title"]').addEventListener('input', function(e) {
  const preview = document.querySelector('.seo-title');
  if (preview) {
    preview.textContent = e.target.value || '{{ $settings["site_name"] ?: "Tiêu đề website" }}';
  }
});

document.querySelector('textarea[name="seo_description"]').addEventListener('input', function(e) {
  const preview = document.querySelector('.seo-description');
  if (preview) {
    preview.textContent = e.target.value || '{{ $settings["site_description"] ?: "Mô tả website sẽ hiển thị ở đây..." }}';
  }
});

// Character counter
document.querySelector('input[name="seo_title"]').addEventListener('input', function(e) {
  const length = e.target.value.length;
  const color = length > 60 ? 'text-danger' : length > 50 ? 'text-warning' : 'text-muted';
  // Add character counter if needed
});

document.querySelector('textarea[name="seo_description"]').addEventListener('input', function(e) {
  const length = e.target.value.length;
  const color = length > 160 ? 'text-danger' : length > 140 ? 'text-warning' : 'text-muted';
  // Add character counter if needed
});
</script>
@endsection
