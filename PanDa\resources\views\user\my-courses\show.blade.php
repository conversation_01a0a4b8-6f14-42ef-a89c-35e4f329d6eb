@extends('layouts.user')

@section('title', '<PERSON> Tiết Khóa Học - ' . $userCourse->course->title)

@section('content')
<div class="row">
  <div class="col-lg-8">
    <!-- Thông Tin Khóa Học -->
    <div class="card mb-4">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">{{ $userCourse->course->title }}</h5>
          <div>
            @if($userCourse->completed_at)
            <span class="badge bg-success">Đ<PERSON>h<PERSON></span>
            @elseif($userCourse->started_at)
            <span class="badge bg-warning"><PERSON><PERSON></span>
            @else
            <span class="badge bg-secondary">Chưa Bắt Đầu</span>
            @endif
          </div>
        </div>
      </div>
      
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            @if($userCourse->course->image)
            <img src="{{ asset('storage/' . $userCourse->course->image) }}" class="img-fluid rounded" alt="{{ $userCourse->course->title }}">
            @else
            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
              <i class="fa fa-code fa-3x text-muted"></i>
            </div>
            @endif
          </div>
          <div class="col-md-8">
            <p class="text-muted">{{ $userCourse->course->description }}</p>
            
            <div class="row text-center mb-3">
              <div class="col-4">
                <small class="text-muted d-block">Tiến Độ</small>
                <strong>{{ number_format($userCourse->progress_percentage, 1) }}%</strong>
              </div>
              <div class="col-4">
                <small class="text-muted d-block">Đã Học</small>
                <strong>{{ count($userCourse->completed_lessons ?? []) }}</strong>
              </div>
              <div class="col-4">
                <small class="text-muted d-block">Tổng Số</small>
                <strong>{{ $userCourse->course->total_lessons }}</strong>
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress mb-3">
              <div class="progress-bar" role="progressbar" style="width: {{ $userCourse->progress_percentage }}%" aria-valuenow="{{ $userCourse->progress_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <!-- Hành Động -->
            <div class="d-grid gap-2">
              @if(!$userCourse->started_at)
              @php
                $firstChapter = $userCourse->course->chapters->sortBy('position')->first();
                $firstLesson = $firstChapter ? $firstChapter->lessons->sortBy('position')->first() : null;
              @endphp
              @if($firstLesson)
              <a href="{{ route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $firstLesson->chapter_id, 'lesson' => $firstLesson->id]) }}" class="btn btn-success">
                <i class="fa fa-play me-2"></i>Bắt Đầu Học
              </a>
              @else
              <button class="btn btn-secondary" disabled>
                <i class="fa fa-exclamation-triangle me-2"></i>Chưa Có Bài Học
              </button>
              @endif
              @elseif($userCourse->completed_at)
              <div class="alert alert-success">
                <i class="fa fa-check-circle me-2"></i>Chúc Mừng! Bạn Đã Hoàn Thành Khóa Học Này
              </div>
              @elseif($userCourse->progress_percentage >= 100 && !$userCourse->course->is_completed)
              <div class="alert alert-warning">
                <i class="fa fa-clock me-2"></i>Bạn Đã Học Xong Tất Cả Bài Học. Đang Chờ Admin Xác Nhận Hoàn Thành Khóa Học.
              </div>
              @else
              @php
                $nextLesson = $userCourse->getNextLesson() ?? $userCourse->currentLesson;
                if (!$nextLesson) {
                  $firstChapter = $userCourse->course->chapters->sortBy('position')->first();
                  $nextLesson = $firstChapter ? $firstChapter->lessons->sortBy('position')->first() : null;
                }
              @endphp
              @if($nextLesson)
              <a href="{{ route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $nextLesson->chapter_id, 'lesson' => $nextLesson->id]) }}" class="btn btn-warning">
                <i class="fa fa-play me-2"></i>Tiếp Tục Học
              </a>
              @else
              <button class="btn btn-secondary" disabled>
                <i class="fa fa-check-circle me-2"></i>Đã Hoàn Thành
              </button>
              @endif
              @endif
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Danh Sách Chương -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Nội Dung Khóa Học
        </h6>
      </div>
      <div class="card-body p-0">
        @foreach($userCourse->course->chapters as $chapter)
        <div class="border-bottom">
          <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="mb-0">{{ $chapter->title }}</h6>
              <small class="text-muted">{{ $chapter->lessons->count() }} Bài Học</small>
            </div>
            @if($chapter->description)
            <p class="text-muted small mb-3">{{ $chapter->description }}</p>
            @endif
            
            <!-- Danh Sách Bài Học -->
            <div class="list-group list-group-flush">
              @foreach($chapter->lessons as $lesson)
              <div class="list-group-item border-0 px-0">
                <div class="d-flex justify-content-between align-items-center">
                  <div class="d-flex align-items-center">
                    @if($lesson->type == 'video')
                    <i class="fa fa-play-circle me-2 text-primary"></i>
                    @elseif($lesson->type == 'text')
                    <i class="fa fa-file-text me-2 text-success"></i>
                    @elseif($lesson->type == 'pdf')
                    <i class="fa fa-file-pdf me-2 text-danger"></i>
                    @else
                    <i class="fa fa-question-circle me-2 text-warning"></i>
                    @endif
                    
                    <div>
                      @if($userCourse->started_at)
                      <a href="{{ route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $lesson->chapter_id, 'lesson' => $lesson->id]) }}" class="text-decoration-none">
                        {{ $lesson->title }}
                      </a>
                      @else
                      <span class="text-muted">{{ $lesson->title }}</span>
                      @endif
                      
                      @if($lesson->is_preview)
                      <span class="badge bg-success ms-1">Preview</span>
                      @endif
                      
                      @if($userCourse->isLessonCompleted($lesson->id))
                      <i class="fa fa-check-circle ms-1 text-success"></i>
                      @endif
                    </div>
                  </div>
                  
                  <small class="text-muted">{{ $lesson->duration_minutes }}p</small>
                </div>
              </div>
              @endforeach
            </div>
          </div>
        </div>
        @endforeach
      </div>
    </div>
  </div>

  <div class="col-lg-4">
    <!-- Thống Kê -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-chart-bar me-2"></i>Thống Kê Học Tập
        </h6>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-6 mb-3">
            <div class="border-end">
              <small class="text-muted d-block">Ngày Bắt Đầu</small>
              <strong>{{ $userCourse->started_at ? $userCourse->started_at->format('d/m/Y') : 'Chưa Bắt Đầu' }}</strong>
            </div>
          </div>
          <div class="col-6 mb-3">
            <small class="text-muted d-block">Lần Cuối Truy Cập</small>
            <strong>{{ $userCourse->last_accessed_at ? $userCourse->last_accessed_at->format('d/m/Y') : 'Chưa Có' }}</strong>
          </div>
          <div class="col-6">
            <div class="border-end">
              <small class="text-muted d-block">Thời Gian Học</small>
              <strong>{{ $userCourse->course->duration_hours }}h</strong>
            </div>
          </div>
          <div class="col-6">
            <small class="text-muted d-block">Cấp Độ</small>
            <strong>{{ ucfirst($userCourse->course->level) }}</strong>
          </div>
        </div>
      </div>
    </div>

    <!-- Quay Lại -->
    <div class="card">
      <div class="card-body">
        <div class="d-grid">
          <a href="{{ route('my-courses.index') }}" class="btn btn-outline-primary">
            <i class="fa fa-arrow-left me-2"></i>Quay Lại Khóa Học Của Tôi
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mt-4">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Trang Chủ</a></li>
    <li class="breadcrumb-item"><a href="{{ route('my-courses.index') }}">Khóa Học Của Tôi</a></li>
    <li class="breadcrumb-item active" aria-current="page">{{ $userCourse->course->title }}</li>
  </ol>
</nav>
@endsection
