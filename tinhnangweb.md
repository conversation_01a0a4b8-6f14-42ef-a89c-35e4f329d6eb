🔢 Giai Đoạn 1: <PERSON><PERSON><PERSON> Thực Người Dùng
🎯 Mục Tiêu:
Cho phép người dùng đăng ký, đă<PERSON> nhập, và quản lý hồ sơ cá nhân.

🔧 Backend
🗂 Bảng CSDL:

users: id, name, email, password, phone, avatar, role (user/admin)

📦 API:

Method	Endpoint	Mô Tả
POST	/register	Đăng ký tài khoản
POST	/login	Đăng nhập
POST	/logout	Đăng xuất
GET	/user	Lấy thông tin user
PUT	/user	Cập nhật hồ sơ cá nhân

🎨 Frontend:
Form đăng ký / đăng nhập chuẩn

<PERSON>rang "Hồ Sơ Cá Nhân": hiển thị + chỉnh sửa tên, avatar, số điện thoại

Header có nút “Đăng Nhập / Hồ Sơ”

Responsive full mobile + desktop

🔢 Giai Đoạn 2: <PERSON><PERSON><PERSON> + Chương + <PERSON>ài Họ<PERSON>
🎯 Mụ<PERSON>:
<PERSON><PERSON><PERSON> thị danh sách khoá họ<PERSON>, chi tiết 1 k<PERSON><PERSON> họ<PERSON> (ch<PERSON><PERSON><PERSON>, b<PERSON><PERSON> họ<PERSON>), b<PERSON><PERSON> học chi tiết.

🔧 Backend
🗂 Bảng CSDL:

courses: id, title, slug, description, image, price, level, category_id, status

categories: id, name, slug

chapters: id, course_id, title, position

lessons: id, chapter_id, title, type, content, position, is_preview

📦 API:

Method	Endpoint	Mô Tả
GET	/courses	Danh sách tất cả khoá học
GET	/courses/{slug}	Chi tiết khoá học
GET	/lessons/{id}	Lấy nội dung bài học

🎨 Frontend:
1. Trang “Tất Cả Khoá Học” (/courses)
Grid layout hiển thị: Ảnh thumbnail, Tên khoá học, Level, Giá, Nút “Xem Chi Tiết”

Bộ lọc theo danh mục hoặc từ khoá

2. Trang “Chi Tiết Khoá Học” (/courses/{slug})
Tiêu đề, mô tả, ảnh bìa lớn

Accordion danh sách chương → xổ ra bài học

Bài học is_preview = true thì được xem (link đến /lessons/{id})

Nút “Mua Khoá Học” hoặc “Học Ngay” nếu đã mua

3. Trang “Học Bài” (/lessons/{id})
Nếu type = video → nhúng YouTube/file

Nếu type = text → hiển thị nội dung HTML/text

Nếu type = pdf → nút tải xuống

Nút “Bài Trước / Sau”, thanh tiến trình bên phải

🔢 Giai Đoạn 3: Giỏ Hàng + Ghi Chú Mua Hàng
🎯 Mục Tiêu:
Cho phép người dùng thêm khoá học vào giỏ, ghi chú và gửi yêu cầu mua thủ công.

🔧 Backend
🗂 Bảng CSDL:

cart_items: id, user_id, course_id, note, created_at

📦 API:

Method	Endpoint	Mô Tả
POST	/cart	Thêm khoá học vào giỏ
GET	/cart	Lấy danh sách trong giỏ
DELETE	/cart/{id}	Xoá khoá học khỏi giỏ

🎨 Frontend:
Trang “Giỏ Hàng”: Hiển thị danh sách khoá học đã thêm

Mỗi mục có nút xóa + ô nhập ghi chú

Nút “Gửi Yêu Cầu Mua” → chuyển sang giai đoạn chuyển khoản

🔢 Giai Đoạn 4: Xác Nhận Chuyển Khoản Thủ Công
🎯 Mục Tiêu:
Cho phép người dùng xác nhận đã chuyển khoản, đính kèm hoá đơn.

🔧 Backend
🗂 Bảng CSDL:

transactions: id, user_id, course_ids (json), note, payment_method, proof_image, status

📦 API:

Method	Endpoint	Mô Tả
POST	/transactions	Gửi thông tin chuyển khoản
GET	/transactions/me	Lịch sử giao dịch của tôi
PUT	/transactions/{id}/status	Admin duyệt thanh toán

🎨 Frontend:
Popup khi nhấn “Mua Khoá Học”

Hiển thị thông tin chuyển khoản (Momo, ngân hàng…)

Form gửi bằng chứng: ảnh hoá đơn + note

Trang “Lịch Sử Giao Dịch”: trạng thái Đang Duyệt / Đã Duyệt

🔢 Giai Đoạn 5: Quản Trị Khoá Học (Admin)
🎯 Mục Tiêu:
Trang quản lý danh sách khoá học và thao tác thêm/sửa/xoá.

🔧 Backend
📦 API (Admin):

Method	Endpoint	Mô Tả
POST	/admin/courses	Tạo khoá học
PUT	/admin/courses/{id}	Cập nhật khoá học
DELETE	/admin/courses/{id}	Xoá khoá học

🎨 Frontend:
Trang “Quản Lý Khoá Học”: bảng list có filter

Nút Thêm Mới, Chỉnh Sửa (popup hoặc trang riêng)

Upload ảnh khoá học, chọn danh mục, level, giá

🔢 Giai Đoạn 6: Khoá Học Của Tôi
🎯 Mục Tiêu:
Người dùng xem danh sách khoá học đã mua.

🔧 Backend
📦 API:

Method	Endpoint	Mô Tả
GET	/my-courses	Lấy danh sách khoá học đã mua

🎨 Frontend:
Trang “Khoá Học Của Tôi”: Grid các khoá học đã mua

Mỗi thẻ có nút “Vào Học”

Cho phép sắp xếp theo thời gian mua

🔢 Giai Đoạn 7: Bình Luận Bài Học
🎯 Mục Tiêu:
Cho phép học viên bình luận dưới mỗi bài học.

🔧 Backend
🗂 Bảng:

comments: id, user_id, lesson_id, content, created_at

📦 API:

Method	Endpoint	Mô Tả
GET	/lessons/{id}/comments	Lấy bình luận
POST	/lessons/{id}/comments	Gửi bình luận

🎨 Frontend:
Dưới mỗi bài học hiển thị danh sách bình luận

Form gửi bình luận mới

Hiển thị avatar, tên người bình luận

🔢 Giai Đoạn 8: Đánh Giá Khoá Học
🎯 Mục Tiêu:
Cho phép học viên đánh giá sao và viết nhận xét sau khi học xong.

🔧 Backend
🗂 Bảng:

reviews: id, user_id, course_id, rating (1–5), comment

📦 API:

Method	Endpoint	Mô Tả
GET	/courses/{id}/reviews	Xem đánh giá
POST	/reviews	Gửi đánh giá khoá học

🎨 Frontend:
Tab “Đánh Giá” trong trang chi tiết khoá học

Form đánh giá: chọn số sao + bình luận

Trung bình sao và số lượt đánh giá

🔢 Giai Đoạn 9: Khoá Học Yêu Thích
🎯 Mục Tiêu:
Người dùng lưu khoá học vào danh sách yêu thích.

🔧 Backend
🗂 Bảng:

favorites: id, user_id, course_id

📦 API:

Method	Endpoint	Mô Tả
POST	/favorites/{id}	Thêm vào yêu thích
GET	/favorites	Xem danh sách yêu thích
DELETE	/favorites/{id}	Bỏ yêu thích

🎨 Frontend:
Nút ❤️ trong mỗi thẻ khoá học

Trang “Khoá Học Yêu Thích”: danh sách khoá học đã lưu

🔢 Giai Đoạn 10: Tài Liệu Học Tập
🎯 Mục Tiêu:
Cung cấp tài liệu học tập tải về (PDF, link, file do admin đăng).

🔧 Backend
🗂 Bảng:

documents: id, title, file_url, course_id (null nếu free), is_free

📦 API:

Method	Endpoint	Mô Tả
GET	/documents	Danh sách tài liệu
POST	/documents	Admin đăng tài liệu

🎨 Frontend:
Trang “Tài Liệu Học Tập”: hiển thị tất cả

Tài liệu free → tải ngay, còn lại chỉ tải nếu đã mua khoá học