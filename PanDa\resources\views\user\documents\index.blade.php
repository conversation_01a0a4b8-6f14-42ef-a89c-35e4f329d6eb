@extends('layouts.user')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">T<PERSON><PERSON>p</h4>
        <p class="text-muted mb-0">Quản Lý V<PERSON> Tải <PERSON> T<PERSON> Li<PERSON> Khó<PERSON> H<PERSON></p>
      </div>
      <div>
        <span class="badge bg-primary fs-3">{{ $documents->count() }} Tà<PERSON></span>
      </div>
    </div>
  </div>
</div>

<!-- Bộ Lọc -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" class="row g-3">
          <div class="col-md-3">
            <label class="form-label"><PERSON><PERSON><PERSON><PERSON></label>
            <select name="course" class="form-select">
              <option value="">Tất <PERSON>h<PERSON> H<PERSON></option>
              @foreach($courses as $course)
              <option value="{{ $course->id }}" {{ request('course') == $course->id ? 'selected' : '' }}>
                {{ $course->title }}
              </option>
              @endforeach
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Loại Tài Liệu</label>
            <select name="type" class="form-select">
              <option value="">Tất Cả Loại</option>
              <option value="pdf" {{ request('type') == 'pdf' ? 'selected' : '' }}>PDF</option>
              <option value="document" {{ request('type') == 'document' ? 'selected' : '' }}>Tài Liệu</option>
              <option value="resource" {{ request('type') == 'resource' ? 'selected' : '' }}>Tài Nguyên</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Sắp Xếp</label>
            <select name="sort" class="form-select">
              <option value="recent" {{ request('sort') == 'recent' ? 'selected' : '' }}>Mới Nhất</option>
              <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Tên A-Z</option>
              <option value="course" {{ request('sort') == 'course' ? 'selected' : '' }}>Theo Khóa Học</option>
            </select>
          </div>
          <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
              <i class="fa fa-filter me-1"></i>Lọc
            </button>
            <a href="{{ route('documents.index') }}" class="btn btn-outline-secondary">
              <i class="fa fa-refresh me-1"></i>Reset
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

@if($documentsCollection->count() > 0)
<!-- Danh Sách Tài Liệu -->
<div class="row">
  @foreach($documentsCollection as $document)
  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      <div class="card-header d-flex justify-content-between align-items-center">
        <span class="badge bg-info">
          @if($document['lesson']->type === 'pdf')
            <i class="fa fa-file-pdf me-1"></i>PDF
          @else
            <i class="fa fa-file me-1"></i>{{ strtoupper($document['lesson']->type) }}
          @endif
        </span>
        @if($document['is_completed'])
        <span class="badge bg-success">
          <i class="fa fa-check me-1"></i>Đã Học
        </span>
        @endif
      </div>
      
      <div class="card-body">
        <h5 class="card-title">{{ $document['lesson']->title }}</h5>
        <p class="card-text text-muted">{{ Str::limit($document['lesson']->content, 100) }}</p>
        
        <!-- Thông Tin Khóa Học -->
        <div class="mb-3">
          <small class="text-muted d-block">
            <i class="fa fa-graduation-cap me-1"></i>
            {{ $document['course']->title }}
          </small>
          <small class="text-muted d-block">
            <i class="fa fa-folder me-1"></i>
            {{ $document['chapter']->title }}
          </small>
        </div>

        <!-- Thông Tin File -->
        @if($document['lesson']->attachment_url)
        <div class="mb-3">
          <small class="text-muted">
            <i class="fa fa-download me-1"></i>
            Có Thể Tải Xuống
          </small>
        </div>
        @endif

        <!-- Thời Gian -->
        <div class="mb-3">
          <small class="text-muted">
            <i class="fa fa-clock me-1"></i>
            Thời Lượng: {{ $document['lesson']->duration_minutes ?? 0 }} phút
          </small>
          <br><small class="text-muted">
            <i class="fa fa-calendar me-1"></i>
            Tạo: {{ $document['lesson']->created_at->format('d/m/Y') }}
          </small>
        </div>
      </div>

      <div class="card-footer bg-transparent">
        <div class="d-grid gap-2">
          @if($document['lesson']->type === 'pdf' || $document['lesson']->attachment_url)
          <div class="btn-group">
            <a href="{{ route('documents.view', $document['lesson']->id) }}" class="btn btn-primary">
              <i class="fa fa-eye me-1"></i>Xem
            </a>
            <a href="{{ route('documents.download', $document['lesson']->id) }}" class="btn btn-outline-primary">
              <i class="fa fa-download me-1"></i>Tải
            </a>
          </div>
          @endif
          
          <a href="{{ route('user.courses.lessons.show', ['course' => $document['course']->id, 'chapter' => $document['lesson']->chapter_id, 'lesson' => $document['lesson']->id]) }}" class="btn btn-outline-secondary btn-sm">
            <i class="fa fa-play me-1"></i>Đến Bài Học
          </a>
        </div>
      </div>
    </div>
  </div>
  @endforeach
</div>

<!-- Phân Trang Đơn Giản -->
<div class="row mt-4">
  <div class="col-12">
    <nav aria-label="Document pagination">
      <ul class="pagination justify-content-center">
        @if(request('page', 1) > 1)
        <li class="page-item">
          <a class="page-link" href="{{ request()->fullUrlWithQuery(['page' => request('page', 1) - 1]) }}">
            <i class="fa fa-chevron-left"></i> Trước
          </a>
        </li>
        @endif
        
        <li class="page-item active">
          <span class="page-link">Trang {{ request('page', 1) }}</span>
        </li>
        
        @if($documentsCollection->count() >= 12)
        <li class="page-item">
          <a class="page-link" href="{{ request()->fullUrlWithQuery(['page' => request('page', 1) + 1]) }}">
            Sau <i class="fa fa-chevron-right"></i>
          </a>
        </li>
        @endif
      </ul>
    </nav>
  </div>
</div>

@else
<!-- Trống -->
<div class="row">
  <div class="col-12">
    <div class="text-center py-5">
      <i class="fa fa-file-text fa-4x text-muted mb-4"></i>
      <h4 class="text-muted mb-3">Chưa Có Tài Liệu Nào</h4>
      <p class="text-muted mb-4">Tài Liệu Sẽ Xuất Hiện Khi Bạn Đăng Ký Các Khóa Học</p>
      <a href="{{ route('my-courses.index') }}" class="btn btn-primary">
        <i class="fa fa-graduation-cap me-2"></i>Xem Khóa Học Của Tôi
      </a>
    </div>
  </div>
</div>
@endif

<!-- Thống Kê Nhanh -->
<div class="row mt-4">
  <div class="col-md-3">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $documents->count() }}</h4>
        <small>Tổng Tài Liệu</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $documents->where('lesson.type', 'pdf')->count() }}</h4>
        <small>File PDF</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $documents->where('is_completed', true)->count() }}</h4>
        <small>Đã Học</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-warning text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $courses->count() }}</h4>
        <small>Khóa Học</small>
      </div>
    </div>
  </div>
</div>
@endsection

@push('scripts')
<script>
// Auto submit form when select changes
document.querySelectorAll('select[name="course"], select[name="type"], select[name="sort"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
@endpush
