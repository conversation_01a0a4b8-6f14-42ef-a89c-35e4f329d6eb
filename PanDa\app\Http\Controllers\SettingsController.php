<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class SettingsController extends Controller
{
    /**
     * Hi<PERSON>n Thị Trang Cài Đặt
     */
    public function index()
    {
        $user = auth()->user();

        // Lấy settings từ session hoặc database
        $settings = [
            'notifications' => [
                'email_notifications' => session('email_notifications', true),
                'order_updates' => session('order_updates', true),
                'course_reminders' => session('course_reminders', true),
                'marketing_emails' => session('marketing_emails', false),
            ],
            'privacy' => [
                'profile_visibility' => session('profile_visibility', 'public'),
                'show_progress' => session('show_progress', true),
                'show_certificates' => session('show_certificates', true),
            ],
            'learning' => [
                'daily_goal_minutes' => session('learning_goal_daily_minutes', 30),
                'weekly_goal_lessons' => session('learning_goal_weekly_lessons', 5),
                'auto_play_next' => session('auto_play_next', true),
                'video_quality' => session('video_quality', 'auto'),
            ],
            'interface' => [
                'theme' => session('theme', 'light'),
                'language' => session('language', 'vi'),
                'timezone' => session('timezone', 'Asia/Ho_Chi_Minh'),
            ]
        ];

        return view('user.settings.index', compact('settings'));
    }

    /**
     * Cập Nhật Cài Đặt Thông Báo
     */
    public function updateNotifications(Request $request)
    {
        $settings = $request->validate([
            'email_notifications' => 'boolean',
            'order_updates' => 'boolean',
            'course_reminders' => 'boolean',
            'marketing_emails' => 'boolean',
        ]);

        foreach ($settings as $key => $value) {
            session([$key => $value]);
        }

        return back()->with('success', 'Cập Nhật Cài Đặt Thông Báo Thành Công!');
    }

    /**
     * Cập Nhật Cài Đặt Quyền Riêng Tư
     */
    public function updatePrivacy(Request $request)
    {
        $settings = $request->validate([
            'profile_visibility' => 'required|in:public,private,friends',
            'show_progress' => 'boolean',
            'show_certificates' => 'boolean',
        ]);

        foreach ($settings as $key => $value) {
            session([$key => $value]);
        }

        return back()->with('success', 'Cập Nhật Cài Đặt Quyền Riêng Tư Thành Công!');
    }

    /**
     * Cập Nhật Cài Đặt Học Tập
     */
    public function updateLearning(Request $request)
    {
        $settings = $request->validate([
            'daily_goal_minutes' => 'required|integer|min:5|max:480',
            'weekly_goal_lessons' => 'required|integer|min:1|max:50',
            'auto_play_next' => 'boolean',
            'video_quality' => 'required|in:auto,720p,480p,360p',
        ]);

        // Cập nhật learning goals
        session([
            'learning_goal_daily_minutes' => $settings['daily_goal_minutes'],
            'learning_goal_weekly_lessons' => $settings['weekly_goal_lessons'],
        ]);

        foreach ($settings as $key => $value) {
            session([$key => $value]);
        }

        return back()->with('success', 'Cập Nhật Cài Đặt Học Tập Thành Công!');
    }

    /**
     * Cập Nhật Cài Đặt Giao Diện
     */
    public function updateInterface(Request $request)
    {
        $settings = $request->validate([
            'theme' => 'required|in:light,dark,auto',
            'language' => 'required|in:vi,en',
            'timezone' => 'required|string',
        ]);

        foreach ($settings as $key => $value) {
            session([$key => $value]);
        }

        return back()->with('success', 'Cập Nhật Cài Đặt Giao Diện Thành Công!');
    }

    /**
     * Reset Tất Cả Cài Đặt
     */
    public function reset()
    {
        // Xóa tất cả settings từ session
        $settingsKeys = [
            'email_notifications', 'order_updates', 'course_reminders', 'marketing_emails',
            'profile_visibility', 'show_progress', 'show_certificates',
            'learning_goal_daily_minutes', 'learning_goal_weekly_lessons', 'auto_play_next', 'video_quality',
            'theme', 'language', 'timezone'
        ];

        foreach ($settingsKeys as $key) {
            session()->forget($key);
        }

        return back()->with('success', 'Đã Reset Tất Cả Cài Đặt Về Mặc Định!');
    }

    /**
     * Xuất Cài Đặt
     */
    public function export()
    {
        $settings = [
            'notifications' => [
                'email_notifications' => session('email_notifications', true),
                'order_updates' => session('order_updates', true),
                'course_reminders' => session('course_reminders', true),
                'marketing_emails' => session('marketing_emails', false),
            ],
            'privacy' => [
                'profile_visibility' => session('profile_visibility', 'public'),
                'show_progress' => session('show_progress', true),
                'show_certificates' => session('show_certificates', true),
            ],
            'learning' => [
                'daily_goal_minutes' => session('learning_goal_daily_minutes', 30),
                'weekly_goal_lessons' => session('learning_goal_weekly_lessons', 5),
                'auto_play_next' => session('auto_play_next', true),
                'video_quality' => session('video_quality', 'auto'),
            ],
            'interface' => [
                'theme' => session('theme', 'light'),
                'language' => session('language', 'vi'),
                'timezone' => session('timezone', 'Asia/Ho_Chi_Minh'),
            ],
            'exported_at' => now()->toISOString(),
            'user_id' => auth()->id(),
        ];

        $fileName = 'settings_' . auth()->id() . '_' . date('Y-m-d') . '.json';

        return response()->json($settings)
            ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');
    }

    /**
     * Nhập Cài Đặt
     */
    public function import(Request $request)
    {
        $request->validate([
            'settings_file' => 'required|file|mimes:json|max:1024', // Max 1MB
        ]);

        try {
            $fileContent = file_get_contents($request->file('settings_file')->getRealPath());
            $settings = json_decode($fileContent, true);

            if (!$settings || !isset($settings['notifications'])) {
                throw new \Exception('File cài đặt không hợp lệ');
            }

            // Import settings
            foreach ($settings['notifications'] as $key => $value) {
                session([$key => $value]);
            }
            foreach ($settings['privacy'] as $key => $value) {
                session([$key => $value]);
            }
            foreach ($settings['learning'] as $key => $value) {
                if ($key === 'daily_goal_minutes') {
                    session(['learning_goal_daily_minutes' => $value]);
                } elseif ($key === 'weekly_goal_lessons') {
                    session(['learning_goal_weekly_lessons' => $value]);
                } else {
                    session([$key => $value]);
                }
            }
            foreach ($settings['interface'] as $key => $value) {
                session([$key => $value]);
            }

            return back()->with('success', 'Nhập Cài Đặt Thành Công!');

        } catch (\Exception $e) {
            return back()->with('error', 'Lỗi Nhập Cài Đặt: ' . $e->getMessage());
        }
    }
}
