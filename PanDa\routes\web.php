<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\LessonController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\Admin\CourseController as AdminCourseController;
use App\Http\Controllers\Admin\CategoryController as AdminCategoryController;
use App\Http\Controllers\Admin\ChapterController as AdminChapterController;
use App\Http\Controllers\Admin\LessonController as AdminLessonController;
use App\Http\Controllers\Admin\OrderController as AdminOrderController;
use App\Http\Controllers\Admin\ReviewController as AdminReviewController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\MyCourseController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\ContinueLearningController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\WishlistController;
use App\Http\Controllers\CertificateController;

// Trang Chủ
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// Public Routes
Route::get('/courses', [CourseController::class, 'index'])->name('courses.index');
Route::get('/courses/{slug}', [CourseController::class, 'show'])->name('courses.show');
Route::get('/categories', [App\Http\Controllers\CategoryController::class, 'index'])->name('categories.index');
Route::get('/categories/{category}', [App\Http\Controllers\CategoryController::class, 'show'])->name('categories.show');

// Certificate Verification (Public)
Route::get('/verify-certificate/{code}', [CertificateController::class, 'verify'])->name('certificates.verify');

// Routes Xác Thực - Guest Only
Route::middleware('guest')->group(function () {
    // Đăng Nhập
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);

    // Đăng Ký
    Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);

    // Quên Mật Khẩu
    Route::get('/forgot-password', function () {
        return view('auth.forgot-password');
    })->name('password.request');

    Route::post('/forgot-password', function () {
        // TODO: Implement password reset logic
        return back()->with('status', 'Link Đặt Lại Mật Khẩu Đã Được Gửi!');
    })->name('password.email');

    // Google Authentication
    Route::get('/auth/google', [AuthController::class, 'redirectToGoogle'])->name('auth.google');
    Route::get('/auth/google/callback', [AuthController::class, 'handleGoogleCallback'])->name('auth.google.callback');
});

// Routes Chung Cho Tất Cả User Đã Đăng Nhập
Route::middleware('auth')->group(function () {
    // Hồ Sơ Cá Nhân
    Route::get('/profile', [AuthController::class, 'profile'])->name('profile');
    Route::put('/profile', [AuthController::class, 'updateProfile'])->name('profile.update');

    // Đăng Xuất
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
});

// Routes Công Khai Cho Khóa Học
Route::prefix('courses')->name('courses.')->group(function () {
    Route::get('/', [CourseController::class, 'index'])->name('index');
    Route::get('/{slug}', [CourseController::class, 'show'])->name('show');
});

// Routes Cho Bài Học
Route::prefix('lessons')->name('lessons.')->group(function () {
    Route::get('/{id}', [LessonController::class, 'show'])->name('show');
});

// Routes Cho Giỏ Hàng (Dành Cho Tất Cả User Đã Đăng Nhập)
Route::middleware('auth')->prefix('cart')->name('cart.')->group(function () {
    Route::get('/', [CartController::class, 'index'])->name('index');
    Route::post('/', [CartController::class, 'store'])->name('store');
    Route::put('/{id}', [CartController::class, 'update'])->name('update');
    Route::delete('/{id}', [CartController::class, 'destroy'])->name('destroy');
    Route::post('/submit-request', [CartController::class, 'submitRequest'])->name('submit');
});

// Routes Cho Đơn Hàng (Dành Cho Tất Cả User Đã Đăng Nhập)
Route::middleware('auth')->prefix('orders')->name('orders.')->group(function () {
    Route::get('/', [OrderController::class, 'index'])->name('index');
    Route::get('/{id}', [OrderController::class, 'show'])->name('show');
    Route::post('/', [OrderController::class, 'store'])->name('store');
});

// Routes Cho Thông Báo (Dành Cho Tất Cả User Đã Đăng Nhập)
Route::middleware('auth')->prefix('notifications')->name('notifications.')->group(function () {
    Route::get('/', [NotificationController::class, 'index'])->name('index');
    Route::post('/{id}/read', [NotificationController::class, 'markAsRead'])->name('read');
    Route::post('/read-all', [NotificationController::class, 'markAllAsRead'])->name('read-all');
    Route::delete('/{id}', [NotificationController::class, 'destroy'])->name('destroy');
    Route::get('/api/unread', [NotificationController::class, 'getUnread'])
        ->name('api.unread')
        ->middleware('rate.limit:30,1'); // 30 requests per minute
});

// Routes Cho Khóa Học Của Tôi (Dành Cho User)
Route::middleware(['auth', 'user'])->prefix('my-courses')->name('my-courses.')->group(function () {
    Route::get('/', [MyCourseController::class, 'index'])->name('index');
    Route::get('/{course}', [MyCourseController::class, 'show'])->name('show');
    Route::post('/{course}/start', [MyCourseController::class, 'start'])->name('start');
    Route::post('/{course}/continue', [MyCourseController::class, 'continue'])->name('continue');
    Route::post('/{course}/lessons/{lesson}/complete', [MyCourseController::class, 'completeLesson'])->name('complete-lesson');
    Route::get('/{course}/lessons/{lesson}', [MyCourseController::class, 'showLesson'])->name('lessons.show');
});

// Routes Cho Tài Liệu (Dành Cho User)
Route::middleware(['auth', 'user'])->prefix('documents')->name('documents.')->group(function () {
    Route::get('/', [DocumentController::class, 'index'])->name('index');
    Route::get('/{lesson}/view', [DocumentController::class, 'view'])->name('view');
    Route::get('/{lesson}/download', [DocumentController::class, 'download'])->name('download');
    Route::get('/api/course/{course}', [DocumentController::class, 'getByCourse'])->name('api.course');
});

// Routes Cho Tiếp Tục Học (Dành Cho User)
Route::middleware(['auth', 'user'])->prefix('continue-learning')->name('continue-learning.')->group(function () {
    Route::get('/', [ContinueLearningController::class, 'index'])->name('index');
    Route::post('/recent', [ContinueLearningController::class, 'continueRecent'])->name('recent');
    Route::post('/update-goal', [ContinueLearningController::class, 'updateGoal'])->name('update-goal');
    Route::get('/api/suggestions', [ContinueLearningController::class, 'getSuggestions'])->name('suggestions');
    Route::get('/api/stats', [ContinueLearningController::class, 'getStats'])->name('stats');
});

// Routes Cho Profile (Dành Cho User)
Route::middleware(['auth', 'user'])->prefix('profile')->name('profile.')->group(function () {
    Route::get('/', [ProfileController::class, 'index'])->name('index');
    Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
    Route::put('/', [ProfileController::class, 'update'])->name('update');
    Route::post('/change-password', [ProfileController::class, 'changePassword'])->name('change-password');
    Route::delete('/avatar', [ProfileController::class, 'deleteAvatar'])->name('delete-avatar');
    Route::get('/export', [ProfileController::class, 'exportData'])->name('export');
});

// Routes Cho Settings (Dành Cho User)
Route::middleware(['auth', 'user'])->prefix('settings')->name('settings.')->group(function () {
    Route::get('/', [SettingsController::class, 'index'])->name('index');
    Route::post('/notifications', [SettingsController::class, 'updateNotifications'])->name('notifications');
    Route::post('/privacy', [SettingsController::class, 'updatePrivacy'])->name('privacy');
    Route::post('/learning', [SettingsController::class, 'updateLearning'])->name('learning');
    Route::post('/interface', [SettingsController::class, 'updateInterface'])->name('interface');
    Route::get('/reset', [SettingsController::class, 'reset'])->name('reset');
    Route::get('/export', [SettingsController::class, 'export'])->name('export');
    Route::post('/import', [SettingsController::class, 'import'])->name('import');
});

// Routes Cho Wishlist (Dành Cho User)
Route::middleware(['auth', 'user'])->prefix('wishlist')->name('wishlist.')->group(function () {
    Route::get('/', [WishlistController::class, 'index'])->name('index');
    Route::post('/{course}/toggle', [WishlistController::class, 'toggle'])->name('toggle');
    Route::delete('/{course}', [WishlistController::class, 'remove'])->name('remove');
    Route::post('/add-all-to-cart', [WishlistController::class, 'addAllToCart'])->name('add-all-to-cart');
    Route::delete('/clear', [WishlistController::class, 'clear'])->name('clear');
    Route::get('/api/count', [WishlistController::class, 'getCount'])->name('api.count');
    Route::get('/api/check/{course}', [WishlistController::class, 'checkCourse'])->name('api.check');
});

// Routes Cho Certificates (Dành Cho User)
Route::middleware(['auth', 'user'])->prefix('user')->name('user.')->group(function () {
    Route::prefix('certificates')->name('certificates.')->group(function () {
        Route::get('/', [CertificateController::class, 'index'])->name('index');
        Route::get('/{certificate}', [CertificateController::class, 'show'])->name('show');
        Route::get('/{certificate}/download', [CertificateController::class, 'download'])->name('download');
        Route::get('/{certificate}/share', [CertificateController::class, 'share'])->name('share');
        Route::post('/{certificate}/regenerate', [CertificateController::class, 'regenerate'])->name('regenerate');
    });

    // Routes Cho Khóa Học Của User
    Route::prefix('courses')->name('courses.')->group(function () {
        Route::get('/', [MyCourseController::class, 'index'])->name('index');
        Route::get('/{course}', [MyCourseController::class, 'show'])->name('show');
        Route::post('/{course}/start', [MyCourseController::class, 'start'])->name('start');
        Route::post('/{course}/continue', [MyCourseController::class, 'continue'])->name('continue');
        Route::post('/{course}/lessons/{lesson}/complete', [MyCourseController::class, 'completeLesson'])->name('complete-lesson');
        Route::get('/{course}/lessons/{lesson}', [MyCourseController::class, 'showLesson'])->name('lessons.show');

        // Download routes cho lessons
        Route::get('/{course}/chapters/{chapter}/lessons/{lesson}/download/{fileIndex}', [App\Http\Controllers\UserLessonController::class, 'downloadFile'])
            ->name('lessons.download.file');
        Route::get('/{course}/chapters/{chapter}/lessons/{lesson}/download-pdf', [App\Http\Controllers\UserLessonController::class, 'downloadPdf'])
            ->name('lessons.download.pdf');
    });

    // Routes Cho Documents
    Route::prefix('documents')->name('documents.')->group(function () {
        Route::get('/', [DocumentController::class, 'index'])->name('index');
        Route::get('/lesson/{lesson}', [DocumentController::class, 'view'])->name('view');
        Route::get('/lesson/{lesson}/download', [DocumentController::class, 'download'])->name('download');
    });
});

// Routes Dành Cho User Thường
Route::middleware(['auth', 'user'])->prefix('user')->name('user.')->group(function () {
    // Dashboard User
    Route::get('/dashboard', function () {
        return view('user.dashboard');
    })->name('dashboard');

    // Profile User
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'index'])->name('index');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/', [ProfileController::class, 'update'])->name('update');
        Route::post('/change-password', [ProfileController::class, 'changePassword'])->name('change-password');
        Route::delete('/avatar', [ProfileController::class, 'deleteAvatar'])->name('delete-avatar');
        Route::get('/export', [ProfileController::class, 'exportData'])->name('export');
    });

    // Routes Cho Lessons Với Pattern Mới
    Route::prefix('courses')->name('courses.')->group(function () {
        Route::get('/{course}/chapters/{chapter}/lessons/{lesson}', [App\Http\Controllers\UserLessonController::class, 'show'])
            ->name('lessons.show');
        Route::post('/{course}/chapters/{chapter}/lessons/{lesson}/complete', [App\Http\Controllers\UserLessonController::class, 'complete'])
            ->name('lessons.complete');
    });



    // Đánh Giá (Rate Limited)
    Route::post('/courses/{course}/reviews', [ReviewController::class, 'store'])
        ->name('reviews.store')
        ->middleware('rate.limit:5,60'); // 5 reviews per hour
    Route::put('/reviews/{review}', [ReviewController::class, 'update'])
        ->name('reviews.update')
        ->middleware('rate.limit:10,60'); // 10 updates per hour
    Route::delete('/reviews/{review}', [ReviewController::class, 'destroy'])
        ->name('reviews.destroy')
        ->middleware('rate.limit:10,60'); // 10 deletes per hour

    // Comments (Rate Limited)
    Route::post('/lessons/{lesson}/comments', [App\Http\Controllers\CommentController::class, 'store'])
        ->name('comments.store')
        ->middleware('rate.limit:10,60'); // 10 comments per hour
    Route::put('/comments/{comment}', [App\Http\Controllers\CommentController::class, 'update'])
        ->name('comments.update')
        ->middleware('rate.limit:20,60'); // 20 updates per hour
    Route::delete('/comments/{comment}', [App\Http\Controllers\CommentController::class, 'destroy'])
        ->name('comments.destroy')
        ->middleware('rate.limit:20,60'); // 20 deletes per hour

    // File Downloads
    Route::get('/lessons/{lesson}/download/{fileIndex}', [App\Http\Controllers\UserLessonController::class, 'downloadFile'])
        ->name('lessons.download.file');
    Route::get('/lessons/{lesson}/download-pdf', [App\Http\Controllers\UserLessonController::class, 'downloadPdf'])
        ->name('lessons.download.pdf');








});

// Documents Routes (Public with auth middleware)
Route::middleware(['auth'])->group(function () {
    Route::get('/documents', [DocumentController::class, 'index'])->name('documents.index');
    Route::get('/documents/lesson/{lesson}', [DocumentController::class, 'view'])->name('documents.view');
    Route::get('/documents/lesson/{lesson}/download', [DocumentController::class, 'download'])->name('documents.download');
});

// Routes Dành Cho Admin
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard Admin
    Route::get('/dashboard', function () {
        return view('admin.dashboard');
    })->name('dashboard');



    // Quản Lý Khóa Học
    Route::resource('courses', AdminCourseController::class);

    // Quản Lý Danh Mục
    Route::resource('categories', AdminCategoryController::class);

    // Quản Lý Chương
    Route::resource('chapters', AdminChapterController::class);

    // Quản Lý Bài Học
    Route::resource('lessons', AdminLessonController::class);

    // Quản Lý Người Dùng
    Route::resource('users', App\Http\Controllers\Admin\UserController::class)->except(['create', 'store']);
    Route::patch('users/{user}/toggle-status', [App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])
        ->name('users.toggle-status');

    // Quản Lý Tài Liệu
    Route::prefix('documents')->name('documents.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\DocumentController::class, 'index'])->name('index');
        Route::get('/stats', [App\Http\Controllers\Admin\DocumentController::class, 'stats'])->name('stats');
        Route::get('/{id}', [App\Http\Controllers\Admin\DocumentController::class, 'show'])->name('show');
        Route::delete('/{id}/pdf', [App\Http\Controllers\Admin\DocumentController::class, 'deletePdf'])->name('delete-pdf');
        Route::delete('/{id}/exercise/{fileIndex}', [App\Http\Controllers\Admin\DocumentController::class, 'deleteExerciseFile'])->name('delete-exercise');
        Route::get('/{id}/download/{type}/{fileIndex?}', [App\Http\Controllers\Admin\DocumentController::class, 'download'])->name('download');
    });

    // Quản Lý Bình Luận
    Route::prefix('comments')->name('comments.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\CommentController::class, 'index'])->name('index');
        Route::get('/stats', [App\Http\Controllers\Admin\CommentController::class, 'stats'])->name('stats');
        Route::get('/{id}', [App\Http\Controllers\Admin\CommentController::class, 'show'])->name('show');
        Route::patch('/{id}/approve', [App\Http\Controllers\Admin\CommentController::class, 'approve'])->name('approve');
        Route::patch('/{id}/reject', [App\Http\Controllers\Admin\CommentController::class, 'reject'])->name('reject');
        Route::delete('/{id}', [App\Http\Controllers\Admin\CommentController::class, 'destroy'])->name('destroy');
        Route::post('/bulk-approve', [App\Http\Controllers\Admin\CommentController::class, 'bulkApprove'])->name('bulk-approve');
        Route::post('/bulk-delete', [App\Http\Controllers\Admin\CommentController::class, 'bulkDelete'])->name('bulk-delete');
    });

    // Quản Lý Đánh Giá
    Route::prefix('reviews')->name('reviews.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ReviewController::class, 'index'])->name('index');
        Route::get('/stats', [App\Http\Controllers\Admin\ReviewController::class, 'stats'])->name('stats');
        Route::patch('/{id}/approve', [App\Http\Controllers\Admin\ReviewController::class, 'approve'])->name('approve');
        Route::patch('/{id}/reject', [App\Http\Controllers\Admin\ReviewController::class, 'reject'])->name('reject');
        Route::delete('/{id}', [App\Http\Controllers\Admin\ReviewController::class, 'destroy'])->name('destroy');
        Route::post('/bulk-approve', [App\Http\Controllers\Admin\ReviewController::class, 'bulkApprove'])->name('bulk-approve');
        Route::post('/bulk-delete', [App\Http\Controllers\Admin\ReviewController::class, 'bulkDelete'])->name('bulk-delete');
    });

    // Cài Đặt Hệ Thống
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('index');

        // General Settings
        Route::get('/general', [App\Http\Controllers\Admin\SettingsController::class, 'general'])->name('general');
        Route::post('/general', [App\Http\Controllers\Admin\SettingsController::class, 'updateGeneral'])->name('general.update');

        // Email Settings
        Route::get('/email', [App\Http\Controllers\Admin\SettingsController::class, 'email'])->name('email');
        Route::post('/email', [App\Http\Controllers\Admin\SettingsController::class, 'updateEmail'])->name('email.update');

        // SEO Settings
        Route::get('/seo', [App\Http\Controllers\Admin\SettingsController::class, 'seo'])->name('seo');
        Route::post('/seo', [App\Http\Controllers\Admin\SettingsController::class, 'updateSeo'])->name('seo.update');

        // Security Settings
        Route::get('/security', [App\Http\Controllers\Admin\SettingsController::class, 'security'])->name('security');
        Route::post('/security', [App\Http\Controllers\Admin\SettingsController::class, 'updateSecurity'])->name('security.update');

        // Social Settings
        Route::get('/social', [App\Http\Controllers\Admin\SettingsController::class, 'social'])->name('social');
        Route::post('/social', [App\Http\Controllers\Admin\SettingsController::class, 'updateSocial'])->name('social.update');

        // System Settings
        Route::get('/system', [App\Http\Controllers\Admin\SettingsController::class, 'system'])->name('system');
        Route::post('/system', [App\Http\Controllers\Admin\SettingsController::class, 'updateSystem'])->name('system.update');

        // System Actions
        Route::post('/clear-cache', [App\Http\Controllers\Admin\SettingsController::class, 'clearCache'])->name('clear-cache');
        Route::post('/optimize', [App\Http\Controllers\Admin\SettingsController::class, 'optimize'])->name('optimize');
        Route::post('/backup', [App\Http\Controllers\Admin\SettingsController::class, 'backup'])->name('backup');
    });

    // Quản Lý Đơn Hàng
    Route::resource('orders', AdminOrderController::class)->except(['create', 'store', 'edit', 'update']);
    Route::post('/orders/{id}/approve', [AdminOrderController::class, 'approve'])->name('orders.approve');
    Route::post('/orders/{id}/reject', [AdminOrderController::class, 'reject'])->name('orders.reject');
    Route::post('/orders/{id}/complete', [AdminOrderController::class, 'complete'])->name('orders.complete');

    // Quản Lý Đánh Giá
    Route::resource('reviews', AdminReviewController::class)->except(['create', 'store', 'edit', 'update']);
    Route::post('/reviews/{id}/approve', [AdminReviewController::class, 'approve'])->name('reviews.approve');
    Route::post('/reviews/{id}/reject', [AdminReviewController::class, 'reject'])->name('reviews.reject');
});

// Route Chuyển Hướng Mặc Định
Route::middleware('auth')->get('/dashboard', function () {
    $user = auth()->user();
    if ($user->role === 'admin') {
        return redirect()->route('admin.dashboard');
    }
    return redirect()->route('user.dashboard');
})->name('dashboard');
