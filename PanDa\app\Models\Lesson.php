<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Lesson extends Model
{
    protected $fillable = [
        'chapter_id',
        'title',
        'type',
        'content',
        'video_url',
        'file_path',
        'exercise_files',
        'duration_minutes',
        'position',
        'is_preview',
        'is_active',
    ];

    protected $casts = [
        'exercise_files' => 'array',
        'duration_minutes' => 'integer',
        'position' => 'integer',
        'is_preview' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Quan Hệ Với Chapter
     */
    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class);
    }

    /**
     * Scope Chỉ Lấy Lesson Đang Hoạt Động
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope Chỉ Lấy Lesson Preview
     */
    public function scopePreview($query)
    {
        return $query->where('is_preview', true);
    }

    /**
     * <PERSON><PERSON><PERSON> Tra Có Phải Video Không
     */
    public function isVideo()
    {
        return $this->type === 'video';
    }

    /**
     * Kiểm Tra Có Phải Text Không
     */
    public function isText()
    {
        return $this->type === 'text';
    }

    /**
     * Kiểm Tra Có Phải PDF Không
     */
    public function isPdf()
    {
        return $this->type === 'pdf';
    }

    /**
     * Lấy URL Video YouTube
     */
    public function getYoutubeEmbedUrlAttribute()
    {
        if ($this->video_url && strpos($this->video_url, 'youtube.com') !== false) {
            $videoId = '';
            if (preg_match('/[\\?\\&]v=([^\\?\\&]+)/', $this->video_url, $matches)) {
                $videoId = $matches[1];
            }
            return "https://www.youtube.com/embed/{$videoId}";
        }
        return $this->video_url;
    }

    /**
     * Quan Hệ Với Comments
     */
    public function comments()
    {
        return $this->hasMany(Comment::class)->orderBy('created_at', 'desc');
    }
}
