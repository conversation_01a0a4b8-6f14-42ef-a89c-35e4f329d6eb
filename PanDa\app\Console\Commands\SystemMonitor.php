<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\User;
use App\Models\Course;
use App\Models\Order;

class SystemMonitor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor:system';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Giám Sát Hệ <PERSON>hống Và Ghi Log Thống Kê';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Bắt Đầu Giám Sát Hệ Thống...');

        try {
            $stats = $this->collectSystemStats();
            $this->logSystemStats($stats);
            $this->displayStats($stats);

            // Kiểm tra cảnh báo
            $this->checkAlerts($stats);

            $this->info('✅ <PERSON>à<PERSON>iám Sát Hệ Thống');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Lỗi Giám Sát: {$e->getMessage()}");
            Log::channel('security')->error('System Monitor Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Thu Thập Thống Kê Hệ Thống
     */
    private function collectSystemStats(): array
    {
        return [
            'timestamp' => now()->toISOString(),
            'database' => $this->getDatabaseStats(),
            'users' => $this->getUserStats(),
            'courses' => $this->getCourseStats(),
            'orders' => $this->getOrderStats(),
            'system' => $this->getSystemStats(),
            'cache' => $this->getCacheStats(),
        ];
    }

    /**
     * Thống Kê Database
     */
    private function getDatabaseStats(): array
    {
        $start = microtime(true);
        $result = DB::select('SELECT 1');
        $responseTime = round((microtime(true) - $start) * 1000, 2);

        return [
            'connection_status' => !empty($result) ? 'connected' : 'disconnected',
            'response_time_ms' => $responseTime,
            'total_tables' => $this->getTableCount(),
        ];
    }

    /**
     * Thống Kê Users
     */
    private function getUserStats(): array
    {
        return [
            'total_users' => User::count(),
            'active_today' => User::whereDate('updated_at', today())->count(),
            'new_this_week' => User::where('created_at', '>=', now()->subWeek())->count(),
            'admin_count' => User::where('role', 'admin')->count(),
        ];
    }

    /**
     * Thống Kê Courses
     */
    private function getCourseStats(): array
    {
        return [
            'total_courses' => Course::count(),
            'published_courses' => Course::where('status', 'published')->count(),
            'draft_courses' => Course::where('status', 'draft')->count(),
            'new_this_month' => Course::where('created_at', '>=', now()->subMonth())->count(),
        ];
    }

    /**
     * Thống Kê Orders
     */
    private function getOrderStats(): array
    {
        return [
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'approved_orders' => Order::where('status', 'approved')->count(),
            'orders_today' => Order::whereDate('created_at', today())->count(),
            'revenue_this_month' => Order::where('status', 'approved')
                ->where('created_at', '>=', now()->subMonth())
                ->sum('total_amount'),
        ];
    }

    /**
     * Thống Kê System
     */
    private function getSystemStats(): array
    {
        return [
            'memory_usage' => $this->formatBytes(memory_get_usage(true)),
            'memory_peak' => $this->formatBytes(memory_get_peak_usage(true)),
            'disk_free' => $this->formatBytes(disk_free_space('.')),
            'disk_total' => $this->formatBytes(disk_total_space('.')),
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
        ];
    }

    /**
     * Thống Kê Cache
     */
    private function getCacheStats(): array
    {
        try {
            Cache::put('monitor_test', 'test', 60);
            $cacheWorking = Cache::get('monitor_test') === 'test';
            Cache::forget('monitor_test');
        } catch (\Exception $e) {
            $cacheWorking = false;
        }

        return [
            'cache_working' => $cacheWorking,
            'cache_driver' => config('cache.default'),
        ];
    }

    /**
     * Ghi Log Thống Kê
     */
    private function logSystemStats(array $stats): void
    {
        Log::channel('performance')->info('System Stats', $stats);
    }

    /**
     * Hiển Thị Thống Kê
     */
    private function displayStats(array $stats): void
    {
        $this->table(['Metric', 'Value'], [
            ['Total Users', $stats['users']['total_users']],
            ['Active Today', $stats['users']['active_today']],
            ['Total Courses', $stats['courses']['total_courses']],
            ['Published Courses', $stats['courses']['published_courses']],
            ['Total Orders', $stats['orders']['total_orders']],
            ['Pending Orders', $stats['orders']['pending_orders']],
            ['Memory Usage', $stats['system']['memory_usage']],
            ['DB Response Time', $stats['database']['response_time_ms'] . 'ms'],
            ['Cache Status', $stats['cache']['cache_working'] ? '✅ Working' : '❌ Failed'],
        ]);
    }

    /**
     * Kiểm Tra Cảnh Báo
     */
    private function checkAlerts(array $stats): void
    {
        // Cảnh báo DB chậm
        if ($stats['database']['response_time_ms'] > 100) {
            $this->warn("⚠️  Database Response Time Cao: {$stats['database']['response_time_ms']}ms");
            Log::channel('security')->warning('High Database Response Time', [
                'response_time_ms' => $stats['database']['response_time_ms']
            ]);
        }

        // Cảnh báo đơn hàng pending nhiều
        if ($stats['orders']['pending_orders'] > 10) {
            $this->warn("⚠️  Có {$stats['orders']['pending_orders']} Đơn Hàng Chờ Duyệt");
        }

        // Cảnh báo cache không hoạt động
        if (!$stats['cache']['cache_working']) {
            $this->error("❌ Cache Không Hoạt Động!");
            Log::channel('security')->error('Cache System Failed');
        }
    }

    /**
     * Đếm Số Bảng
     */
    private function getTableCount(): int
    {
        try {
            $tables = DB::select('SHOW TABLES');
            return count($tables);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Format Bytes
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
