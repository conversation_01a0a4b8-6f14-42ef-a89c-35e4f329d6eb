<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Review;
use Illuminate\Http\Request;

class ReviewController extends Controller
{
    /**
     * Hiển Th<PERSON>h <PERSON>ch <PERSON>
     */
    public function index(Request $request)
    {
        $query = Review::with(['user:id,name,email', 'course:id,title']);

        // Lọc theo trạng thái
        if ($request->filled('status')) {
            if ($request->status === 'approved') {
                $query->approved();
            } elseif ($request->status === 'pending') {
                $query->pending();
            }
        }

        // Lọc theo rating
        if ($request->filled('rating')) {
            $query->where('rating', $request->rating);
        }

        // Tìm kiếm
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('comment', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('course', function ($courseQuery) use ($search) {
                      $courseQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        $reviews = $query->latest()->paginate(15);

        return view('admin.reviews.index', compact('reviews'));
    }

    /**
     * Hiển Thị Chi Tiết Đánh Giá
     */
    public function show($id)
    {
        $review = Review::with(['user', 'course'])->findOrFail($id);
        return view('admin.reviews.show', compact('review'));
    }

    /**
     * Duyệt Đánh Giá
     */
    public function approve($id)
    {
        $review = Review::findOrFail($id);
        $review->update(['is_approved' => true]);

        return back()->with('success', 'Duyệt Đánh Giá Thành Công!');
    }

    /**
     * Từ Chối Đánh Giá
     */
    public function reject($id)
    {
        $review = Review::findOrFail($id);
        $review->update(['is_approved' => false]);

        return back()->with('success', 'Từ Chối Đánh Giá Thành Công!');
    }

    /**
     * Xóa Đánh Giá
     */
    public function destroy($id)
    {
        $review = Review::findOrFail($id);
        $review->delete();

        return redirect()->route('admin.reviews.index')
            ->with('success', 'Xóa Đánh Giá Thành Công!');
    }

    /**
     * Thống Kê Đánh Giá
     */
    public function stats()
    {
        $stats = [
            'total_reviews' => Review::count(),
            'approved_reviews' => Review::where('is_approved', true)->count(),
            'pending_reviews' => Review::where('is_approved', false)->count(),
            'average_rating' => round(Review::where('is_approved', true)->avg('rating'), 1),
            'five_star' => Review::where('rating', 5)->where('is_approved', true)->count(),
            'four_star' => Review::where('rating', 4)->where('is_approved', true)->count(),
            'three_star' => Review::where('rating', 3)->where('is_approved', true)->count(),
            'two_star' => Review::where('rating', 2)->where('is_approved', true)->count(),
            'one_star' => Review::where('rating', 1)->where('is_approved', true)->count(),
            'today_reviews' => Review::whereDate('created_at', today())->count(),
            'this_week_reviews' => Review::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month_reviews' => Review::whereMonth('created_at', now()->month)->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Duyệt Hàng Loạt
     */
    public function bulkApprove(Request $request)
    {
        $reviewIds = $request->review_ids;

        if ($reviewIds) {
            Review::whereIn('id', $reviewIds)->update(['is_approved' => true]);
            return back()->with('success', 'Đã Duyệt ' . count($reviewIds) . ' Đánh Giá!');
        }

        return back()->with('error', 'Vui Lòng Chọn Đánh Giá!');
    }

    /**
     * Xóa Hàng Loạt
     */
    public function bulkDelete(Request $request)
    {
        $reviewIds = $request->review_ids;

        if ($reviewIds) {
            Review::whereIn('id', $reviewIds)->delete();
            return back()->with('success', 'Đã Xóa ' . count($reviewIds) . ' Đánh Giá!');
        }

        return back()->with('error', 'Vui Lòng Chọn Đánh Giá!');
    }
}
