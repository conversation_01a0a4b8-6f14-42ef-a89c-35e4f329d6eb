@extends('layouts.admin')

@section('title', 'Quản Lý Đánh Giá')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Quản Lý Đánh Giá</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active">Đánh Giá</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="total-reviews">0</h4>
              <p class="mb-0">Tổng Đánh Giá</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-star fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="approved-reviews">0</h4>
              <p class="mb-0">Đã Duyệt</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-check-circle fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-warning text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="pending-reviews">0</h4>
              <p class="mb-0">Chờ Duyệt</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-clock fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-info text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="average-rating">0</h4>
              <p class="mb-0">Điểm TB</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-chart-line fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Rating Distribution -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="fa fa-chart-bar me-2"></i>Phân Bố Đánh Giá
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-2">
              <div class="text-center">
                <h5 id="five-star">0</h5>
                <div class="text-warning">
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <h5 id="four-star">0</h5>
                <div class="text-warning">
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                  <i class="far fa-star"></i>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <h5 id="three-star">0</h5>
                <div class="text-warning">
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                  <i class="far fa-star"></i>
                  <i class="far fa-star"></i>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <h5 id="two-star">0</h5>
                <div class="text-warning">
                  <i class="fa fa-star"></i>
                  <i class="fa fa-star"></i>
                  <i class="far fa-star"></i>
                  <i class="far fa-star"></i>
                  <i class="far fa-star"></i>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <h5 id="one-star">0</h5>
                <div class="text-warning">
                  <i class="fa fa-star"></i>
                  <i class="far fa-star"></i>
                  <i class="far fa-star"></i>
                  <i class="far fa-star"></i>
                  <i class="far fa-star"></i>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="text-center">
                <h5 id="today-reviews">0</h5>
                <small class="text-muted">Hôm Nay</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="GET" class="row g-3">
            <div class="col-md-4">
              <label class="form-label">Tìm Kiếm</label>
              <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                     placeholder="Nội dung, tên user, khóa học...">
            </div>
            <div class="col-md-2">
              <label class="form-label">Trạng Thái</label>
              <select class="form-select" name="status">
                <option value="">Tất Cả</option>
                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Đã Duyệt</option>
                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Chờ Duyệt</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">Đánh Giá</label>
              <select class="form-select" name="rating">
                <option value="">Tất Cả</option>
                <option value="5" {{ request('rating') == '5' ? 'selected' : '' }}>5 Sao</option>
                <option value="4" {{ request('rating') == '4' ? 'selected' : '' }}>4 Sao</option>
                <option value="3" {{ request('rating') == '3' ? 'selected' : '' }}>3 Sao</option>
                <option value="2" {{ request('rating') == '2' ? 'selected' : '' }}>2 Sao</option>
                <option value="1" {{ request('rating') == '1' ? 'selected' : '' }}>1 Sao</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">Khóa Học</label>
              <select class="form-select" name="course_id">
                <option value="">Tất Cả Khóa Học</option>
                @foreach(\App\Models\Course::orderBy('title')->get() as $course)
                <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                  {{ $course->title }}
                </option>
                @endforeach
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-search me-1"></i>Tìm
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Bulk Actions -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form id="bulk-form" method="POST">
            @csrf
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <input type="checkbox" id="select-all" class="form-check-input me-2">
                <label for="select-all" class="form-check-label">Chọn Tất Cả</label>
              </div>
              <div>
                <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('approve')">
                  <i class="fa fa-check me-1"></i>Duyệt Đã Chọn
                </button>
                <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                  <i class="fa fa-trash me-1"></i>Xóa Đã Chọn
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Reviews List -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-star me-2"></i>Danh Sách Đánh Giá ({{ $reviews->total() }})
          </h5>
        </div>
        <div class="card-body p-0">
          @if($reviews->count() > 0)
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th width="50">
                    <input type="checkbox" class="form-check-input">
                  </th>
                  <th>Người Dùng</th>
                  <th>Khóa Học</th>
                  <th>Đánh Giá</th>
                  <th>Nội Dung</th>
                  <th>Trạng Thái</th>
                  <th>Ngày Tạo</th>
                  <th>Thao Tác</th>
                </tr>
              </thead>
              <tbody>
                @foreach($reviews as $review)
                <tr>
                  <td>
                    <input type="checkbox" class="form-check-input review-checkbox" value="{{ $review->id }}">
                  </td>
                  
                  <td>
                    <div class="d-flex align-items-center">
                      @if($review->user->avatar)
                      <img src="{{ asset('storage/' . $review->user->avatar) }}" alt="{{ $review->user->name }}" 
                           class="rounded-circle me-2" width="32" height="32">
                      @else
                      <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                           style="width: 32px; height: 32px;">
                        <span class="text-white fw-bold">{{ strtoupper(substr($review->user->name, 0, 1)) }}</span>
                      </div>
                      @endif
                      <div>
                        <h6 class="mb-0">{{ $review->user->name }}</h6>
                        <small class="text-muted">{{ $review->user->email }}</small>
                      </div>
                    </div>
                  </td>
                  
                  <td>
                    <span class="badge bg-primary">{{ $review->course->title }}</span>
                  </td>
                  
                  <td>
                    <div class="text-warning">
                      @for($i = 1; $i <= 5; $i++)
                        @if($i <= $review->rating)
                        <i class="fa fa-star"></i>
                        @else
                        <i class="far fa-star"></i>
                        @endif
                      @endfor
                      <span class="ms-1">({{ $review->rating }})</span>
                    </div>
                  </td>
                  
                  <td>
                    <p class="mb-0">{{ Str::limit($review->comment, 100) }}</p>
                  </td>
                  
                  <td>
                    @if($review->is_approved)
                    <span class="badge bg-success">Đã Duyệt</span>
                    @else
                    <span class="badge bg-warning">Chờ Duyệt</span>
                    @endif
                  </td>
                  
                  <td>{{ $review->created_at->format('d/m/Y H:i') }}</td>
                  
                  <td>
                    @include('admin.partials.action-buttons', [
                      'deleteRoute' => route('admin.reviews.destroy', $review->id),
                      'deleteMessage' => 'Bạn có chắc muốn xóa đánh giá này?'
                    ])

                    @if(!$review->is_approved)
                    <form action="{{ route('admin.reviews.approve', $review->id) }}" method="POST" class="d-inline" style="margin-left: 2px;">
                      @csrf
                      @method('PATCH')
                      <button type="submit" class="btn btn-outline-success d-flex align-items-center justify-content-center"
                              data-bs-toggle="tooltip"
                              style="width: 32px; height: 32px;"
                              aria-label="Duyệt"
                              data-bs-original-title="Duyệt">
                        <i class="fa fa-check" style="font-size: 14px;"></i>
                      </button>
                    </form>
                    @else
                    <form action="{{ route('admin.reviews.reject', $review->id) }}" method="POST" class="d-inline" style="margin-left: 2px;">
                      @csrf
                      @method('PATCH')
                      <button type="submit" class="btn btn-outline-warning d-flex align-items-center justify-content-center"
                              data-bs-toggle="tooltip"
                              style="width: 32px; height: 32px;"
                              aria-label="Từ Chối"
                              data-bs-original-title="Từ Chối">
                        <i class="fa fa-times" style="font-size: 14px;"></i>
                      </button>
                    </form>
                    @endif
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          <div class="card-footer">
            {{ $reviews->links() }}
          </div>
          @else
          <div class="text-center py-5">
            <i class="fa fa-star fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Không Tìm Thấy Đánh Giá</h5>
            <p class="text-muted">Thử thay đổi bộ lọc để tìm kiếm</p>
          </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Load stats
fetch('{{ route("admin.reviews.stats") }}')
  .then(response => response.json())
  .then(data => {
    document.getElementById('total-reviews').textContent = data.total_reviews;
    document.getElementById('approved-reviews').textContent = data.approved_reviews;
    document.getElementById('pending-reviews').textContent = data.pending_reviews;
    document.getElementById('average-rating').textContent = data.average_rating;
    document.getElementById('five-star').textContent = data.five_star;
    document.getElementById('four-star').textContent = data.four_star;
    document.getElementById('three-star').textContent = data.three_star;
    document.getElementById('two-star').textContent = data.two_star;
    document.getElementById('one-star').textContent = data.one_star;
    document.getElementById('today-reviews').textContent = data.today_reviews;
  })
  .catch(error => console.error('Error loading stats:', error));

// Select all checkbox
document.getElementById('select-all').addEventListener('change', function() {
  const checkboxes = document.querySelectorAll('.review-checkbox');
  checkboxes.forEach(checkbox => {
    checkbox.checked = this.checked;
  });
});

// Bulk actions
function bulkAction(action) {
  const checkedBoxes = document.querySelectorAll('.review-checkbox:checked');
  if (checkedBoxes.length === 0) {
    alert('Vui lòng chọn ít nhất một đánh giá!');
    return;
  }
  
  const reviewIds = Array.from(checkedBoxes).map(cb => cb.value);
  const form = document.getElementById('bulk-form');
  
  // Clear existing hidden inputs
  form.querySelectorAll('input[name="review_ids[]"]').forEach(input => input.remove());
  
  // Add review IDs
  reviewIds.forEach(id => {
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'review_ids[]';
    input.value = id;
    form.appendChild(input);
  });
  
  if (action === 'approve') {
    form.action = '{{ route("admin.reviews.bulk-approve") }}';
  } else if (action === 'delete') {
    if (!confirm('Bạn có chắc muốn xóa các đánh giá đã chọn?')) {
      return;
    }
    form.action = '{{ route("admin.reviews.bulk-delete") }}';
  }
  
  form.submit();
}
</script>
@endsection
