<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Helper function để xử lý ảnh
        if (!function_exists('safe_image_url')) {
            function safe_image_url($url, $default = null) {
                if (empty($url)) {
                    return $default ?: asset('assets/images/default-image.png');
                }

                // Kiểm tra nếu là URL đầy đủ
                if (filter_var($url, FILTER_VALIDATE_URL)) {
                    return $url;
                }

                // Kiểm tra nếu là đường dẫn storage
                if (strpos($url, 'storage/') === 0 || strpos($url, '/storage/') === 0) {
                    return asset($url);
                }

                // Mặc định trả về storage path
                return asset('storage/' . $url);
            }
        }

        if (!function_exists('default_course_image')) {
            function default_course_image() {
                return asset('assets/images/default-course.png');
            }
        }

        if (!function_exists('default_category_image')) {
            function default_category_image() {
                return asset('assets/images/default-category.png');
            }
        }
    }
}
