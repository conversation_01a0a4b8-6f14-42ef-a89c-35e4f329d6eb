<?php $__env->startSection('title', 'Tất <PERSON> - <PERSON>ệ <PERSON> H<PERSON>p Online'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Tất <PERSON>ả Khó<PERSON> H<PERSON></h4>
        <p class="text-muted mb-0">Khám Phá Các Khóa Học Chất Lượng Cao</p>
      </div>
      <div>
        <span class="badge bg-primary fs-3"><?php echo e($courses->total()); ?> Kh<PERSON><PERSON> H<PERSON></span>
      </div>
    </div>
  </div>
</div>

<!-- Bộ Lọc -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" action="<?php echo e(route('courses.index')); ?>">
          <div class="row">
            <div class="col-md-4 mb-3">
              <label class="form-label">Tìm Kiếm</label>
              <input type="text" class="form-control" name="search" value="<?php echo e(request('search')); ?>" placeholder="Nhập Từ Khóa...">
            </div>
            
            <div class="col-md-3 mb-3">
              <label class="form-label">Danh Mục</label>
              <select class="form-select" name="category">
                <option value="">Tất Cả Danh Mục</option>
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                  <?php echo e($category->name); ?>

                </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
            
            <div class="col-md-3 mb-3">
              <label class="form-label">Cấp Độ</label>
              <select class="form-select" name="level">
                <option value="">Tất Cả Cấp Độ</option>
                <option value="beginner" <?php echo e(request('level') == 'beginner' ? 'selected' : ''); ?>>Người Mới Bắt Đầu</option>
                <option value="intermediate" <?php echo e(request('level') == 'intermediate' ? 'selected' : ''); ?>>Trung Cấp</option>
                <option value="advanced" <?php echo e(request('level') == 'advanced' ? 'selected' : ''); ?>>Nâng Cao</option>
              </select>
            </div>
            
            <div class="col-md-2 mb-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-search me-2"></i>Lọc
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Danh Sách Khóa Học -->
<div class="row">
  <?php $__empty_1 = true; $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      <?php if($course->image_url): ?>
      <img src="<?php echo e($course->image_url); ?>"
           class="card-img-top"
           alt="<?php echo e($course->title); ?>"
           style="height: 200px; object-fit: cover;"
           onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
      <div class="card-img-top bg-light align-items-center justify-content-center"
           style="height: 200px; display: none;">
        <div class="text-center">
          <i class="fa fa-book fa-3x text-muted mb-2"></i>
          <div class="small text-muted">Không Có Ảnh</div>
        </div>
      </div>
      <?php else: ?>
      <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
           style="height: 200px;">
        <div class="text-center">
          <i class="fa fa-book fa-3x text-muted mb-2"></i>
          <div class="small text-muted">Không Có Ảnh</div>
        </div>
      </div>
      <?php endif; ?>
      
      <div class="card-body d-flex flex-column">
        <div class="mb-2">
          <span class="badge bg-primary"><?php echo e($course->category->name); ?></span>
          <span class="badge bg-secondary ms-1">
            <?php if($course->level == 'beginner'): ?> Người Mới
            <?php elseif($course->level == 'intermediate'): ?> Trung Cấp
            <?php else: ?> Nâng Cao
            <?php endif; ?>
          </span>

          <?php if(isset($userCourses[$course->id])): ?>
          <?php $userCourse = $userCourses[$course->id]; ?>
          <?php if($userCourse->completed_at): ?>
          <span class="badge bg-success ms-1">
            <i class="fa fa-check-circle me-1"></i>Đã Hoàn Thành
          </span>
          <?php elseif($userCourse->started_at): ?>
          <span class="badge bg-warning ms-1">
            <i class="fa fa-play me-1"></i>Đang Học (<?php echo e($userCourse->progress_percentage); ?>%)
          </span>
          <?php else: ?>
          <span class="badge bg-info ms-1">
            <i class="fa fa-bookmark me-1"></i>Đã Đăng Ký
          </span>
          <?php endif; ?>
          <?php endif; ?>
        </div>
        
        <h5 class="card-title"><?php echo e($course->title); ?></h5>
        <p class="card-text text-muted flex-grow-1"><?php echo e(Str::limit($course->description, 100)); ?></p>
        
        <div class="mt-auto">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="text-muted small">
              <i class="fa fa-clock me-1"></i><?php echo e($course->duration_hours); ?>h
              <i class="fa fa-play-circle ms-2 me-1"></i><?php echo e($course->total_lessons); ?> Bài
            </div>
            <div class="fw-bold text-primary">
              <?php if($course->price > 0): ?>
              <?php echo e(number_format($course->price, 0, ',', '.')); ?>đ
              <?php else: ?>
              Miễn Phí
              <?php endif; ?>
            </div>
          </div>
          
          <?php if(isset($userCourses[$course->id])): ?>
          <?php $userCourse = $userCourses[$course->id]; ?>
          <?php if($userCourse->completed_at): ?>
          <a href="<?php echo e(route('my-courses.show', $course->id)); ?>" class="btn btn-success w-100">
            <i class="fa fa-certificate me-2"></i>Xem Chứng Chỉ
          </a>
          <?php elseif($userCourse->started_at): ?>
          <?php
            $nextLesson = $userCourse->getNextLesson() ?? $userCourse->currentLesson;
            if (!$nextLesson) {
              $firstChapter = $userCourse->course->chapters->sortBy('position')->first();
              $nextLesson = $firstChapter ? $firstChapter->lessons->sortBy('position')->first() : null;
            }
          ?>
          <?php if($nextLesson): ?>
          <a href="<?php echo e(route('user.courses.lessons.show', ['course' => $course->id, 'chapter' => $nextLesson->chapter_id, 'lesson' => $nextLesson->id])); ?>" class="btn btn-warning w-100">
            <i class="fa fa-play me-2"></i>Tiếp Tục Học
          </a>
          <?php else: ?>
          <a href="<?php echo e(route('my-courses.show', $course->id)); ?>" class="btn btn-outline-primary w-100">
            <i class="fa fa-eye me-2"></i>Xem Chi Tiết
          </a>
          <?php endif; ?>
          <?php else: ?>
          <a href="<?php echo e(route('my-courses.show', $course->id)); ?>" class="btn btn-primary w-100">
            <i class="fa fa-play me-2"></i>Bắt Đầu Học
          </a>
          <?php endif; ?>
          <?php else: ?>
          <a href="<?php echo e(route('courses.show', $course->slug)); ?>" class="btn btn-primary w-100">
            <i class="fa fa-code me-2"></i>Xem Chi Tiết
          </a>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
  <div class="col-12">
    <div class="text-center py-5">
      <i class="fa fa-search fa-4x text-muted mb-3"></i>
      <h5 class="text-muted mb-3">Không Tìm Thấy Khóa Học</h5>
      <p class="text-muted mb-4">Thử Thay Đổi Bộ Lọc Hoặc Từ Khóa Tìm Kiếm</p>
      <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-primary">
        <i class="fa fa-code me-2"></i>Xem Tất Cả Khóa Học
      </a>
    </div>
  </div>
  <?php endif; ?>
</div>

<!-- Phân Trang -->
<?php if($courses->hasPages()): ?>
<div class="row mt-4">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      <?php echo e($courses->appends(request()->query())->links()); ?>

    </div>
  </div>
</div>
<?php endif; ?>

<!-- Thống Kê -->
<div class="row mt-5">
  <div class="col-12">
    <div class="card bg-light">
      <div class="card-body text-center">
        <div class="row">
          <div class="col-md-3">
            <h4 class="text-primary mb-0"><?php echo e($courses->total()); ?></h4>
            <small class="text-muted">Tổng Khóa Học</small>
          </div>
          <div class="col-md-3">
            <h4 class="text-success mb-0"><?php echo e($categories->count()); ?></h4>
            <small class="text-muted">Danh Mục</small>
          </div>
          <div class="col-md-3">
            <h4 class="text-warning mb-0">0</h4>
            <small class="text-muted">Học Viên</small>
          </div>
          <div class="col-md-3">
            <h4 class="text-info mb-0">0</h4>
            <small class="text-muted">Giờ Học</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Src Công Việc\PanDa\resources\views/user/courses/index.blade.php ENDPATH**/ ?>