@extends('layouts.user')

@section('title', '<PERSON><PERSON>ch Yêu Thích')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0"><PERSON><PERSON>ách Yêu Thích</h4>
        <p class="text-muted mb-0">Các Khóa Học Bạn Quan Tâm</p>
      </div>
      <div>
        @if($wishlists->count() > 0)
        <div class="btn-group">
          <a href="{{ route('wishlist.add-all-to-cart') }}" class="btn btn-primary" onclick="return confirm('Thêm tất cả vào giỏ hàng?')">
            <i class="fa fa-shopping-cart me-2"></i>Thêm Tất Cả Vào Giỏ
          </a>
          <a href="{{ route('wishlist.clear') }}" class="btn btn-outline-danger" onclick="return confirm('<PERSON><PERSON><PERSON> tất cả khỏi danh sách yêu thích?')">
            <i class="fa fa-trash me-2"></i>Xóa Tất Cả
          </a>
        </div>
        @endif
      </div>
    </div>
  </div>
</div>

<!-- Filters -->
@if($wishlists->count() > 0)
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" action="{{ route('wishlist.index') }}">
          <div class="row">
            <div class="col-md-3 mb-3">
              <label class="form-label">Danh Mục</label>
              <select class="form-select" name="category">
                <option value="">Tất Cả Danh Mục</option>
                @foreach($categories as $category)
                <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                  {{ $category->name }}
                </option>
                @endforeach
              </select>
            </div>
            
            <div class="col-md-3 mb-3">
              <label class="form-label">Cấp Độ</label>
              <select class="form-select" name="level">
                <option value="">Tất Cả Cấp Độ</option>
                <option value="beginner" {{ request('level') == 'beginner' ? 'selected' : '' }}>Cơ Bản</option>
                <option value="intermediate" {{ request('level') == 'intermediate' ? 'selected' : '' }}>Trung Cấp</option>
                <option value="advanced" {{ request('level') == 'advanced' ? 'selected' : '' }}>Nâng Cao</option>
              </select>
            </div>
            
            <div class="col-md-3 mb-3">
              <label class="form-label">Giá</label>
              <select class="form-select" name="price_filter">
                <option value="">Tất Cả</option>
                <option value="free" {{ request('price_filter') == 'free' ? 'selected' : '' }}>Miễn Phí</option>
                <option value="paid" {{ request('price_filter') == 'paid' ? 'selected' : '' }}>Có Phí</option>
              </select>
            </div>
            
            <div class="col-md-3 mb-3">
              <label class="form-label">Sắp Xếp</label>
              <select class="form-select" name="sort">
                <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Mới Nhất</option>
                <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Tên A-Z</option>
                <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Giá Thấp</option>
                <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Giá Cao</option>
              </select>
            </div>
          </div>
          
          <div class="text-end">
            <button type="submit" class="btn btn-primary me-2">
              <i class="fa fa-filter me-1"></i>Lọc
            </button>
            <a href="{{ route('wishlist.index') }}" class="btn btn-outline-secondary">
              <i class="fa fa-refresh me-1"></i>Reset
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@endif

<!-- Wishlist Items -->
<div class="row">
  @if($wishlists->count() > 0)
    @foreach($wishlists as $wishlist)
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100">
        <div class="position-relative">
          @if($wishlist->course->image_url)
          <img src="{{ $wishlist->course->image_url }}" class="card-img-top" alt="{{ $wishlist->course->title }}" style="height: 200px; object-fit: cover;">
          @else
          <div class="bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
            <i class="fa fa-book fa-3x text-muted"></i>
          </div>
          @endif
          
          <!-- Price Badge -->
          <div class="position-absolute top-0 end-0 m-2">
            @if($wishlist->course->price > 0)
            <span class="badge bg-primary">{{ number_format($wishlist->course->price) }}đ</span>
            @else
            <span class="badge bg-success">Miễn Phí</span>
            @endif
          </div>
          
          <!-- Level Badge -->
          <div class="position-absolute top-0 start-0 m-2">
            <span class="badge bg-secondary">{{ ucfirst($wishlist->course->level) }}</span>
          </div>
        </div>
        
        <div class="card-body d-flex flex-column">
          <div class="mb-2">
            <small class="text-muted">{{ $wishlist->course->category->name ?? 'Chưa phân loại' }}</small>
          </div>
          
          <h6 class="card-title">{{ $wishlist->course->title }}</h6>
          <p class="card-text text-muted small flex-grow-1">
            {{ Str::limit($wishlist->course->description, 100) }}
          </p>
          
          <div class="mb-3">
            <small class="text-muted">
              <i class="fa fa-heart me-1"></i>Đã thêm: {{ $wishlist->created_at->diffForHumans() }}
            </small>
          </div>
          
          <div class="d-grid gap-2">
            <a href="{{ route('courses.show', $wishlist->course->slug) }}" class="btn btn-primary">
              <i class="fa fa-eye me-2"></i>Xem Chi Tiết
            </a>
            
            @if($wishlist->course->price > 0)
            <form method="POST" action="{{ route('cart.store') }}" class="d-inline">
              @csrf
              <input type="hidden" name="course_id" value="{{ $wishlist->course->id }}">
              <button type="submit" class="btn btn-outline-success w-100">
                <i class="fa fa-shopping-cart me-2"></i>Thêm Vào Giỏ
              </button>
            </form>
            @endif
            
            <form method="POST" action="{{ route('wishlist.remove', $wishlist->course->id) }}" class="d-inline">
              @csrf
              @method('DELETE')
              <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Xóa khỏi danh sách yêu thích?')">
                <i class="fa fa-trash me-2"></i>Xóa Khỏi Danh Sách
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
    @endforeach
  @else
  <!-- Empty State -->
  <div class="col-12">
    <div class="text-center py-5">
      <i class="fa fa-heart fa-4x text-muted mb-4"></i>
      <h4>Danh Sách Yêu Thích Trống</h4>
      <p class="text-muted mb-4">Bạn chưa thêm khóa học nào vào danh sách yêu thích</p>
      <a href="{{ route('courses.index') }}" class="btn btn-primary">
        <i class="fa fa-search me-2"></i>Khám Phá Khóa Học
      </a>
    </div>
  </div>
  @endif
</div>

<!-- Pagination -->
@if($wishlists->hasPages())
<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      {{ $wishlists->links() }}
    </div>
  </div>
</div>
@endif
@endsection
