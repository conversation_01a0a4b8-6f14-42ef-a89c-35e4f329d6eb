@extends('layouts.user')

@section('title', 'Thông Báo')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Thông Báo</h4>
        <p class="text-muted mb-0"><PERSON>ông Báo Quan Trọng</p>
      </div>
      <div>
        @if(auth()->user()->unread_notifications_count > 0)
        <form method="POST" action="{{ route('notifications.read-all') }}" class="d-inline">
          @csrf
          <button type="submit" class="btn btn-outline-primary">
            <i class="fa fa-check-double me-2"></i>Đ<PERSON>h <PERSON><PERSON><PERSON> T<PERSON>t Cả Đã Đọc
          </button>
        </form>
        @endif
      </div>
    </div>
  </div>
</div>

@if($notifications->count() > 0)
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-bell me-2"></i>Danh Sách Thông Báo ({{ $notifications->total() }})
        </h5>
      </div>
      <div class="card-body p-0">
        <div class="list-group list-group-flush">
          @foreach($notifications as $notification)
          <div class="list-group-item {{ $notification->isRead() ? '' : 'bg-light border-start border-primary border-3' }}">
            <div class="d-flex justify-content-between align-items-start">
              <div class="flex-grow-1">
                <div class="d-flex align-items-center mb-2">
                  <i class="fa {{ $notification->icon }} {{ $notification->css_class }} me-2"></i>
                  <h6 class="mb-0 {{ $notification->isRead() ? 'text-muted' : '' }}">
                    {{ $notification->title }}
                  </h6>
                  @if(!$notification->isRead())
                  <span class="badge bg-primary ms-2">Mới</span>
                  @endif
                </div>
                
                <p class="mb-2 {{ $notification->isRead() ? 'text-muted' : '' }}">
                  {{ $notification->message }}
                </p>
                
                <div class="d-flex align-items-center justify-content-between">
                  <small class="text-muted">
                    <i class="fa fa-clock me-1"></i>
                    {{ $notification->created_at->diffForHumans() }}
                  </small>
                  
                  <div class="btn-group btn-group-sm">
                    @if(!$notification->isRead())
                    <form method="POST" action="{{ route('notifications.read', $notification->id) }}" class="d-inline">
                      @csrf
                      <button type="submit" class="btn btn-outline-primary" title="Đánh Dấu Đã Đọc">
                        <i class="fa fa-check"></i>
                      </button>
                    </form>
                    @endif
                    
                    @if($notification->data && isset($notification->data['order_id']))
                    <a href="{{ route('orders.show', $notification->data['order_id']) }}" class="btn btn-outline-info" title="Xem Đơn Hàng">
                      <i class="fa fa-eye"></i>
                    </a>
                    @endif
                    
                    <form method="POST" action="{{ route('notifications.destroy', $notification->id) }}" class="d-inline">
                      @csrf
                      @method('DELETE')
                      <button type="submit" class="btn btn-outline-danger" title="Xóa" onclick="return confirm('Bạn có chắc muốn xóa thông báo này?')">
                        <i class="fa fa-trash"></i>
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Phân Trang -->
@if($notifications->hasPages())
<div class="row mt-4">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      {{ $notifications->links() }}
    </div>
  </div>
</div>
@endif

@else
<!-- Trống -->
<div class="row">
  <div class="col-12">
    <div class="text-center py-5">
      <i class="fa fa-bell-slash fa-4x text-muted mb-4"></i>
      <h4 class="text-muted mb-3">Chưa Có Thông Báo Nào</h4>
      <p class="text-muted mb-4">Các thông báo quan trọng sẽ hiển thị tại đây</p>
      <a href="{{ route('user.dashboard') }}" class="btn btn-primary">
        <i class="fa fa-home me-2"></i>Về Trang Chủ
      </a>
    </div>
  </div>
</div>
@endif

<!-- Thống Kê -->
<div class="row mt-4">
  <div class="col-md-4">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $notifications->total() }}</h4>
        <small>Tổng Thông Báo</small>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card bg-warning text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ auth()->user()->unread_notifications_count }}</h4>
        <small>Chưa Đọc</small>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ auth()->user()->notifications()->read()->count() }}</h4>
        <small>Đã Đọc</small>
      </div>
    </div>
  </div>
</div>
@endsection
