@extends('layouts.admin')

@section('title', 'Cài Đặt Mạng Xã Hội')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Cài Đặt Mạng Xã Hội</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Cài Đặt</a></li>
            <li class="breadcrumb-item active">Mạng Xã Hội</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <form action="{{ route('admin.settings.social.update') }}" method="POST">
    @csrf
    <div class="row">
      <!-- Social Links -->
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-link me-2"></i>Liên Kết Mạng Xã Hội
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="fab fa-facebook text-primary me-2"></i>Facebook
                </label>
                <input type="url" class="form-control" name="facebook_url" 
                       value="{{ old('facebook_url', $settings['facebook_url']) }}" 
                       placeholder="https://facebook.com/yourpage">
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="fab fa-twitter text-info me-2"></i>Twitter
                </label>
                <input type="url" class="form-control" name="twitter_url" 
                       value="{{ old('twitter_url', $settings['twitter_url']) }}" 
                       placeholder="https://twitter.com/youraccount">
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="fab fa-instagram text-danger me-2"></i>Instagram
                </label>
                <input type="url" class="form-control" name="instagram_url" 
                       value="{{ old('instagram_url', $settings['instagram_url']) }}" 
                       placeholder="https://instagram.com/youraccount">
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="fab fa-youtube text-danger me-2"></i>YouTube
                </label>
                <input type="url" class="form-control" name="youtube_url" 
                       value="{{ old('youtube_url', $settings['youtube_url']) }}" 
                       placeholder="https://youtube.com/c/yourchannel">
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="fab fa-linkedin text-primary me-2"></i>LinkedIn
                </label>
                <input type="url" class="form-control" name="linkedin_url" 
                       value="{{ old('linkedin_url', $settings['linkedin_url']) }}" 
                       placeholder="https://linkedin.com/company/yourcompany">
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="fab fa-tiktok text-dark me-2"></i>TikTok
                </label>
                <input type="url" class="form-control" name="tiktok_url" 
                       value="{{ old('tiktok_url', $settings['tiktok_url']) }}" 
                       placeholder="https://tiktok.com/@youraccount">
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="fab fa-discord text-primary me-2"></i>Discord
                </label>
                <input type="url" class="form-control" name="discord_url" 
                       value="{{ old('discord_url', $settings['discord_url']) }}" 
                       placeholder="https://discord.gg/yourserver">
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">
                  <i class="fab fa-telegram text-info me-2"></i>Telegram
                </label>
                <input type="url" class="form-control" name="telegram_url" 
                       value="{{ old('telegram_url', $settings['telegram_url']) }}" 
                       placeholder="https://t.me/yourchannel">
              </div>
            </div>
          </div>
        </div>

        <!-- Social Login -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-sign-in-alt me-2"></i>Đăng Nhập Mạng Xã Hội
            </h5>
          </div>
          <div class="card-body">
            <!-- Facebook Login -->
            <div class="row mb-4">
              <div class="col-12">
                <h6><i class="fab fa-facebook text-primary me-2"></i>Facebook Login</h6>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">Facebook App ID</label>
                <input type="text" class="form-control" name="facebook_app_id" 
                       value="{{ old('facebook_app_id', $settings['facebook_app_id']) }}" 
                       placeholder="123456789012345">
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">Facebook App Secret</label>
                <input type="text" class="form-control" name="facebook_app_secret" 
                       value="{{ old('facebook_app_secret', $settings['facebook_app_secret']) }}" 
                       placeholder="abcdef123456...">
              </div>
            </div>

            <!-- Google Login -->
            <div class="row mb-4">
              <div class="col-12">
                <h6><i class="fab fa-google text-danger me-2"></i>Google Login</h6>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">Google Client ID</label>
                <input type="text" class="form-control" name="google_client_id" 
                       value="{{ old('google_client_id', $settings['google_client_id']) }}" 
                       placeholder="123456789-abc.apps.googleusercontent.com">
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">Google Client Secret</label>
                <input type="text" class="form-control" name="google_client_secret" 
                       value="{{ old('google_client_secret', $settings['google_client_secret']) }}" 
                       placeholder="GOCSPX-...">
              </div>
            </div>

            <!-- GitHub Login -->
            <div class="row">
              <div class="col-12">
                <h6><i class="fab fa-github text-dark me-2"></i>GitHub Login</h6>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">GitHub Client ID</label>
                <input type="text" class="form-control" name="github_client_id" 
                       value="{{ old('github_client_id', $settings['github_client_id']) }}" 
                       placeholder="Iv1.abc123...">
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">GitHub Client Secret</label>
                <input type="text" class="form-control" name="github_client_secret" 
                       value="{{ old('github_client_secret', $settings['github_client_secret']) }}" 
                       placeholder="abc123...">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Preview & Guides -->
      <div class="col-lg-4">
        <!-- Social Links Preview -->
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-eye me-2"></i>Xem Trước
            </h5>
          </div>
          <div class="card-body">
            <div class="social-preview text-center">
              <h6 class="mb-3">Liên Kết Mạng Xã Hội</h6>
              <div class="social-links">
                @if($settings['facebook_url'])
                <a href="{{ $settings['facebook_url'] }}" class="btn btn-outline-primary btn-sm me-2 mb-2" target="_blank">
                  <i class="fab fa-facebook"></i>
                </a>
                @endif
                @if($settings['twitter_url'])
                <a href="{{ $settings['twitter_url'] }}" class="btn btn-outline-info btn-sm me-2 mb-2" target="_blank">
                  <i class="fab fa-twitter"></i>
                </a>
                @endif
                @if($settings['instagram_url'])
                <a href="{{ $settings['instagram_url'] }}" class="btn btn-outline-danger btn-sm me-2 mb-2" target="_blank">
                  <i class="fab fa-instagram"></i>
                </a>
                @endif
                @if($settings['youtube_url'])
                <a href="{{ $settings['youtube_url'] }}" class="btn btn-outline-danger btn-sm me-2 mb-2" target="_blank">
                  <i class="fab fa-youtube"></i>
                </a>
                @endif
                @if($settings['linkedin_url'])
                <a href="{{ $settings['linkedin_url'] }}" class="btn btn-outline-primary btn-sm me-2 mb-2" target="_blank">
                  <i class="fab fa-linkedin"></i>
                </a>
                @endif
                @if($settings['tiktok_url'])
                <a href="{{ $settings['tiktok_url'] }}" class="btn btn-outline-dark btn-sm me-2 mb-2" target="_blank">
                  <i class="fab fa-tiktok"></i>
                </a>
                @endif
              </div>
              
              @if(!$settings['facebook_url'] && !$settings['twitter_url'] && !$settings['instagram_url'])
              <p class="text-muted small">Chưa có liên kết nào được cấu hình</p>
              @endif
            </div>
          </div>
        </div>

        <!-- Social Login Status -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-check-circle me-2"></i>Trạng Thái Đăng Nhập
            </h5>
          </div>
          <div class="card-body">
            <div class="list-group list-group-flush">
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span><i class="fab fa-facebook text-primary me-2"></i>Facebook</span>
                <i class="fa fa-{{ ($settings['facebook_app_id'] && $settings['facebook_app_secret']) ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span><i class="fab fa-google text-danger me-2"></i>Google</span>
                <i class="fa fa-{{ ($settings['google_client_id'] && $settings['google_client_secret']) ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span><i class="fab fa-github text-dark me-2"></i>GitHub</span>
                <i class="fa fa-{{ ($settings['github_client_id'] && $settings['github_client_secret']) ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Setup Guides -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-book me-2"></i>Hướng Dẫn Cài Đặt
            </h5>
          </div>
          <div class="card-body">
            <div class="accordion" id="socialGuides">
              <!-- Facebook -->
              <div class="accordion-item">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#facebook-guide">
                    <i class="fab fa-facebook text-primary me-2"></i>Facebook
                  </button>
                </h2>
                <div id="facebook-guide" class="accordion-collapse collapse" data-bs-parent="#socialGuides">
                  <div class="accordion-body small">
                    <ol>
                      <li>Truy cập <a href="https://developers.facebook.com" target="_blank">Facebook Developers</a></li>
                      <li>Tạo ứng dụng mới</li>
                      <li>Thêm Facebook Login product</li>
                      <li>Cấu hình Valid OAuth Redirect URIs: <code>{{ url('/auth/facebook/callback') }}</code></li>
                      <li>Copy App ID và App Secret</li>
                    </ol>
                  </div>
                </div>
              </div>

              <!-- Google -->
              <div class="accordion-item">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#google-guide">
                    <i class="fab fa-google text-danger me-2"></i>Google
                  </button>
                </h2>
                <div id="google-guide" class="accordion-collapse collapse" data-bs-parent="#socialGuides">
                  <div class="accordion-body small">
                    <ol>
                      <li>Truy cập <a href="https://console.developers.google.com" target="_blank">Google Console</a></li>
                      <li>Tạo project mới hoặc chọn project</li>
                      <li>Bật Google+ API</li>
                      <li>Tạo OAuth 2.0 credentials</li>
                      <li>Thêm Redirect URI: <code>{{ url('/auth/google/callback') }}</code></li>
                      <li>Copy Client ID và Client Secret</li>
                    </ol>
                  </div>
                </div>
              </div>

              <!-- GitHub -->
              <div class="accordion-item">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#github-guide">
                    <i class="fab fa-github text-dark me-2"></i>GitHub
                  </button>
                </h2>
                <div id="github-guide" class="accordion-collapse collapse" data-bs-parent="#socialGuides">
                  <div class="accordion-body small">
                    <ol>
                      <li>Truy cập <a href="https://github.com/settings/applications/new" target="_blank">GitHub OAuth Apps</a></li>
                      <li>Tạo OAuth App mới</li>
                      <li>Cấu hình Authorization callback URL: <code>{{ url('/auth/github/callback') }}</code></li>
                      <li>Copy Client ID và Client Secret</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Submit Buttons -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left me-2"></i>Quay Lại
              </a>
              <div>
                <button type="reset" class="btn btn-outline-secondary me-2">
                  <i class="fa fa-undo me-2"></i>Đặt Lại
                </button>
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-save me-2"></i>Lưu Cài Đặt
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
@endsection
