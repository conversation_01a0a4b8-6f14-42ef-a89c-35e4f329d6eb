<?php

namespace App\Http\Controllers;

use App\Models\UserCourse;
use App\Models\Lesson;
use Illuminate\Http\Request;

class MyCourseController extends Controller
{
    /**
     * Hiển Thị <PERSON>h Sách <PERSON>hóa Học <PERSON>a Tôi
     */
    public function index(Request $request)
    {
        $query = auth()->user()->userCourses()
            ->with(['course.category', 'course.chapters.lessons'])
            ->distinct();

        // Lọc theo trạng thái
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'not_started':
                    $query->notStarted();
                    break;
                case 'in_progress':
                    $query->inProgress();
                    break;
                case 'completed':
                    $query->completed();
                    break;
            }
        }

        // Sắp xếp
        $sortBy = $request->get('sort', 'recent');
        switch ($sortBy) {
            case 'progress':
                $query->orderBy('progress_percentage', 'desc');
                break;
            case 'name':
                $query->join('courses', 'user_courses.course_id', '=', 'courses.id')
                     ->orderBy('courses.title');
                break;
            default:
                $query->orderBy('last_accessed_at', 'desc')
                     ->orderBy('enrolled_at', 'desc');
        }

        $userCourses = $query->paginate(12);

        return view('user.my-courses.index', compact('userCourses'));
    }

    /**
     * Hiển Thị Chi Tiết Khóa Học Đang Học
     */
    public function show($courseId)
    {
        $userCourse = auth()->user()->userCourses()
            ->with(['course.category', 'course.chapters.lessons', 'currentLesson'])
            ->where('course_id', $courseId)
            ->firstOrFail();

        // Cập nhật last accessed
        $userCourse->update(['last_accessed_at' => now()]);

        return view('user.my-courses.show', compact('userCourse'));
    }

    /**
     * Bắt Đầu Học Khóa Học
     */
    public function start($courseId)
    {
        $userCourse = auth()->user()->userCourses()
            ->where('course_id', $courseId)
            ->firstOrFail();

        $userCourse->startLearning();

        // Lấy bài học đầu tiên
        $firstChapter = $userCourse->course->chapters()
            ->orderBy('position')
            ->first();

        $firstLesson = null;
        if ($firstChapter) {
            $firstLesson = $firstChapter->lessons()
                ->orderBy('position')
                ->first();
        }

        if ($firstLesson) {
            return redirect()->route('user.courses.lessons.show', [
                'course' => $courseId,
                'chapter' => $firstLesson->chapter_id,
                'lesson' => $firstLesson->id
            ]);
        }

        return redirect()->route('my-courses.show', $courseId)
            ->with('error', 'Khóa Học Chưa Có Bài Học Nào');
    }

    /**
     * Tiếp Tục Học
     */
    public function continue($courseId)
    {
        $userCourse = auth()->user()->userCourses()
            ->where('course_id', $courseId)
            ->firstOrFail();

        // Lấy bài học tiếp theo hoặc bài học hiện tại
        $nextLesson = $userCourse->getNextLesson() ?? $userCourse->currentLesson;

        if ($nextLesson) {
            return redirect()->route('user.courses.lessons.show', [
                'course' => $courseId,
                'chapter' => $nextLesson->chapter_id,
                'lesson' => $nextLesson->id
            ]);
        }

        return redirect()->route('my-courses.show', $courseId)
            ->with('success', 'Bạn Đã Hoàn Thành Tất Cả Bài Học!');
    }

    /**
     * Đánh Dấu Bài Học Hoàn Thành
     */
    public function completeLesson(Request $request, $courseId, $lessonId)
    {
        $userCourse = auth()->user()->userCourses()
            ->where('course_id', $courseId)
            ->firstOrFail();

        $lesson = Lesson::findOrFail($lessonId);

        // Kiểm tra lesson thuộc course
        if (!$lesson->chapter->course_id == $courseId) {
            abort(403);
        }

        $userCourse->updateProgress($lessonId);

        return response()->json([
            'success' => true,
            'progress' => $userCourse->fresh()->progress_percentage,
            'message' => 'Đã Hoàn Thành Bài Học!'
        ]);
    }

    /**
     * Hiển Thị Bài Học
     */
    public function showLesson($courseId, $lessonId)
    {
        $userCourse = auth()->user()->userCourses()
            ->with(['course.chapters.lessons'])
            ->where('course_id', $courseId)
            ->firstOrFail();

        $lesson = Lesson::with(['chapter'])->findOrFail($lessonId);

        // Kiểm tra lesson thuộc course
        if ($lesson->chapter->course_id != $courseId) {
            abort(404);
        }

        // Cập nhật current lesson và last accessed
        $userCourse->update([
            'current_lesson_id' => $lessonId,
            'last_accessed_at' => now()
        ]);

        // Bắt đầu học nếu chưa bắt đầu
        if (!$userCourse->started_at) {
            $userCourse->startLearning();
        }

        return view('user.lessons.show', compact('userCourse', 'lesson'));
    }
}
