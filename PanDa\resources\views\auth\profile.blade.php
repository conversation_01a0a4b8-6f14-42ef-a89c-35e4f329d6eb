<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="csrf-token" content="{{ csrf_token() }}">

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="{{ asset('assets/images/logos/favicon.png') }}" />

  <!-- Core Css -->
  <link rel="stylesheet" href="{{ asset('assets/css/styles.css') }}" />

  <!-- Custom Css -->
  <link rel="stylesheet" href="{{ asset('assets/css/custom.css') }}" />

  <title><PERSON><PERSON> - <PERSON><PERSON> T<PERSON>ống <PERSON></title>
</head>

<body>
  <!-- Preloader -->
  <div class="preloader">
    <img src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/images/logos/favicon.png') }}" alt="loader" class="lds-ripple img-fluid" />
  </div>
  
  <div id="main-wrapper">
    <div class="position-relative overflow-hidden min-vh-100 w-100 d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
      <div class="d-flex align-items-center justify-content-center w-100">
        <div class="row justify-content-center w-100 my-5">
          <div class="col-md-8 col-lg-6">
            <div class="card mb-0 bg-body shadow-lg">
              <div class="card-header bg-primary text-white text-center py-4">
                <h3 class="mb-0"><i class="fa fa-code me-2"></i>Hồ Sơ Cá Nhân</h3>
              </div>
              
              <div class="card-body p-4">
                @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                  <i class="fa fa-check-circle me-2"></i>{{ session('success') }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                @endif

                @if ($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                  <ul class="mb-0">
                    @foreach ($errors->all() as $error)
                    <li><i class="fa fa-exclamation-circle me-2"></i>{{ $error }}</li>
                    @endforeach
                  </ul>
                  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                @endif

                <!-- Avatar Section -->
                <div class="text-center mb-4">
                  <div class="position-relative d-inline-block">
                    @if($user->avatar)
                    <img src="{{ asset('storage/' . $user->avatar) }}" alt="Avatar" class="rounded-circle border border-3 border-primary" width="120" height="120" style="object-fit: cover;">
                    @else
                    <div class="rounded-circle border border-3 border-primary d-flex align-items-center justify-content-center bg-light" style="width: 120px; height: 120px;">
                      <i class="fa fa-user fa-3x text-muted"></i>
                    </div>
                    @endif
                  </div>
                  <h4 class="mt-3 mb-1">{{ $user->name }}</h4>
                  <p class="text-muted">{{ ucfirst($user->role) }}</p>
                </div>

                <form method="POST" action="{{ route('profile.update') }}" enctype="multipart/form-data">
                  @csrf
                  @method('PUT')
                  
                  <div class="row">
                    <div class="col-12 mb-3">
                      <label for="avatar" class="form-label">Ảnh Đại Diện</label>
                      <input type="file" class="form-control @error('avatar') is-invalid @enderror" id="avatar" name="avatar" accept="image/*">
                      @error('avatar')
                      <div class="invalid-feedback">{{ $message }}</div>
                      @enderror
                      <div class="form-text">Chọn File Ảnh (JPEG, PNG, JPG, GIF) - Tối Đa 2MB</div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="name" class="form-label">Họ Và Tên</label>
                      <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $user->name) }}" required>
                      @error('name')
                      <div class="invalid-feedback">{{ $message }}</div>
                      @enderror
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label for="email" class="form-label">Email</label>
                      <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                      @error('email')
                      <div class="invalid-feedback">{{ $message }}</div>
                      @enderror
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12 mb-3">
                      <label for="phone" class="form-label">Số Điện Thoại</label>
                      <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', $user->phone) }}" placeholder="Nhập Số Điện Thoại">
                      @error('phone')
                      <div class="invalid-feedback">{{ $message }}</div>
                      @enderror
                    </div>
                  </div>

                  <hr class="my-4">
                  <h5 class="mb-3">Đổi Mật Khẩu</h5>
                  
                  <div class="row">
                    <div class="col-12 mb-3">
                      <label for="current_password" class="form-label">Mật Khẩu Hiện Tại</label>
                      <input type="password" class="form-control @error('current_password') is-invalid @enderror" id="current_password" name="current_password" placeholder="Nhập Mật Khẩu Hiện Tại">
                      @error('current_password')
                      <div class="invalid-feedback">{{ $message }}</div>
                      @enderror
                      <div class="form-text">Chỉ Nhập Nếu Muốn Đổi Mật Khẩu</div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="password" class="form-label">Mật Khẩu Mới</label>
                      <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" placeholder="Nhập Mật Khẩu Mới">
                      @error('password')
                      <div class="invalid-feedback">{{ $message }}</div>
                      @enderror
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label for="password_confirmation" class="form-label">Xác Nhận Mật Khẩu Mới</label>
                      <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" placeholder="Nhập Lại Mật Khẩu Mới">
                    </div>
                  </div>

                  <div class="d-flex justify-content-between align-items-center mt-4">
                    <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary">
                      <i class="fa fa-arrow-left me-2"></i>Quay Lại Dashboard
                    </a>
                    
                    <div>
                      <form method="POST" action="{{ route('logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-outline-danger me-2">
                          <i class="fa fa-sign-out-alt me-2"></i>Đăng Xuất
                        </button>
                      </form>
                      
                      <button type="submit" class="btn btn-primary">
                        <i class="fa fa-code me-2"></i>Cập Nhật Hồ Sơ
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Import Js Files -->
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/libs/simplebar/dist/simplebar.min.js') }}"></script>
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/js/theme/app.init.js') }}"></script>
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/js/theme/theme.js') }}"></script>
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/js/theme/app.min.js') }}"></script>

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  
  <!-- Preview Avatar Script -->
  <script>
    document.getElementById('avatar').addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          const avatarImg = document.querySelector('img[alt="Avatar"]');
          if (avatarImg) {
            avatarImg.src = e.target.result;
          }
        };
        reader.readAsDataURL(file);
      }
    });
  </script>
</body>

</html>
