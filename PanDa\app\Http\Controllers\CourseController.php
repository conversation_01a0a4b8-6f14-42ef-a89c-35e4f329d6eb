<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Category;
use App\Models\Lesson;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    /**
     * Hi<PERSON><PERSON>
     */
    public function index(Request $request)
    {
        $query = Course::with('category')->published();

        // Lọc Theo Category
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Lọc Theo Level
        if ($request->filled('level')) {
            $query->byLevel($request->level);
        }

        // Tìm <PERSON>
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('requirements', 'like', "%{$search}%")
                  ->orWhere('what_you_learn', 'like', "%{$search}%")
                  ->orWhereHas('category', function ($categoryQuery) use ($search) {
                      $categoryQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Lọc Theo Khoảng Giá
        if ($request->filled('min_price') || $request->filled('max_price')) {
            $minPrice = $request->get('min_price', 0);
            $maxPrice = $request->get('max_price', 999999999);
            $query->whereBetween('price', [$minPrice, $maxPrice]);
        }

        // Lọc Theo Thời Lượng
        if ($request->filled('duration_filter')) {
            switch ($request->duration_filter) {
                case 'short':
                    $query->where('duration_hours', '<=', 10);
                    break;
                case 'medium':
                    $query->whereBetween('duration_hours', [11, 30]);
                    break;
                case 'long':
                    $query->where('duration_hours', '>', 30);
                    break;
            }
        }

        $courses = $query->paginate(12);

        // Lấy thông tin user courses nếu đã đăng nhập
        $userCourses = [];
        if (auth()->check()) {
            $userCourses = auth()->user()->userCourses()
                ->whereIn('course_id', $courses->pluck('id'))
                ->get()
                ->keyBy('course_id');
        }

        $categories = Category::active()->get();

        return view('user.courses.index', compact('courses', 'categories', 'userCourses'));
    }

    /**
     * Hiển Thị Chi Tiết Khóa Học
     */
    public function show($slug)
    {
        $course = Course::with(['category', 'chapters.lessons'])
            ->where('slug', $slug)
            ->published()
            ->firstOrFail();

        return view('user.courses.show', compact('course'));
    }

    /**
     * API: Lấy Danh Sách Khóa Học
     */
    public function apiIndex(Request $request)
    {
        $query = Course::with('category')->published();

        // Lọc Theo Category
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Lọc Theo Level
        if ($request->filled('level')) {
            $query->byLevel($request->level);
        }

        // Tìm Kiếm
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $courses = $query->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => $courses
        ]);
    }

    /**
     * API: Lấy Chi Tiết Khóa Học
     */
    public function apiShow($slug)
    {
        $course = Course::with(['category', 'chapters.lessons'])
            ->where('slug', $slug)
            ->published()
            ->first();

        if (!$course) {
            return response()->json([
                'success' => false,
                'message' => 'Khóa Học Không Tồn Tại'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $course
        ]);
    }
}
