<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UserMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Vui L<PERSON>ng <PERSON>');
        }

        // Cho phép admin truy cập giao diện user
        if (auth()->user()->role !== 'user' && auth()->user()->role !== 'admin') {
            return redirect()->route('login')->with('error', 'Bạn <PERSON>ng <PERSON>');
        }

        return $next($request);
    }
}
