@extends('layouts.admin')

@section('title', 'Cài Đặt Tổng Quan')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Cài Đặt Tổng Quan</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Cài Đặt</a></li>
            <li class="breadcrumb-item active">Tổng Quan</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <form action="{{ route('admin.settings.general.update') }}" method="POST" enctype="multipart/form-data">
    @csrf
    <div class="row">
      <!-- Thông Tin Website -->
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-globe me-2"></i>Thông Tin Website
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">Tên Website <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('site_name') is-invalid @enderror" 
                       name="site_name" value="{{ old('site_name', $settings['site_name']) }}" required>
                @error('site_name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Ngôn Ngữ <span class="text-danger">*</span></label>
                <select class="form-select @error('language') is-invalid @enderror" name="language" required>
                  <option value="vi" {{ old('language', $settings['language']) == 'vi' ? 'selected' : '' }}>Tiếng Việt</option>
                  <option value="en" {{ old('language', $settings['language']) == 'en' ? 'selected' : '' }}>English</option>
                </select>
                @error('language')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-12 mb-3">
                <label class="form-label">Mô Tả Website <span class="text-danger">*</span></label>
                <textarea class="form-control @error('site_description') is-invalid @enderror" 
                          name="site_description" rows="3" required>{{ old('site_description', $settings['site_description']) }}</textarea>
                @error('site_description')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-12 mb-3">
                <label class="form-label">Từ Khóa Website</label>
                <input type="text" class="form-control @error('site_keywords') is-invalid @enderror" 
                       name="site_keywords" value="{{ old('site_keywords', $settings['site_keywords']) }}"
                       placeholder="học trực tuyến, khóa học, giáo dục">
                <small class="text-muted">Các từ khóa cách nhau bằng dấu phẩy</small>
                @error('site_keywords')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Múi Giờ <span class="text-danger">*</span></label>
                <select class="form-select @error('timezone') is-invalid @enderror" name="timezone" required>
                  <option value="Asia/Ho_Chi_Minh" {{ old('timezone', $settings['timezone']) == 'Asia/Ho_Chi_Minh' ? 'selected' : '' }}>Việt Nam (UTC+7)</option>
                  <option value="Asia/Bangkok" {{ old('timezone', $settings['timezone']) == 'Asia/Bangkok' ? 'selected' : '' }}>Bangkok (UTC+7)</option>
                  <option value="Asia/Singapore" {{ old('timezone', $settings['timezone']) == 'Asia/Singapore' ? 'selected' : '' }}>Singapore (UTC+8)</option>
                  <option value="UTC" {{ old('timezone', $settings['timezone']) == 'UTC' ? 'selected' : '' }}>UTC (UTC+0)</option>
                </select>
                @error('timezone')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
          </div>
        </div>

        <!-- Thông Tin Liên Hệ -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-address-book me-2"></i>Thông Tin Liên Hệ
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">Email Admin <span class="text-danger">*</span></label>
                <input type="email" class="form-control @error('admin_email') is-invalid @enderror" 
                       name="admin_email" value="{{ old('admin_email', $settings['admin_email']) }}" required>
                @error('admin_email')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Email Liên Hệ <span class="text-danger">*</span></label>
                <input type="email" class="form-control @error('contact_email') is-invalid @enderror" 
                       name="contact_email" value="{{ old('contact_email', $settings['contact_email']) }}" required>
                @error('contact_email')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Số Điện Thoại</label>
                <input type="text" class="form-control @error('contact_phone') is-invalid @enderror" 
                       name="contact_phone" value="{{ old('contact_phone', $settings['contact_phone']) }}">
                @error('contact_phone')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-12 mb-3">
                <label class="form-label">Địa Chỉ</label>
                <textarea class="form-control @error('contact_address') is-invalid @enderror" 
                          name="contact_address" rows="2">{{ old('contact_address', $settings['contact_address']) }}</textarea>
                @error('contact_address')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Logo & Favicon -->
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-image me-2"></i>Logo & Favicon
            </h5>
          </div>
          <div class="card-body">
            <!-- Logo -->
            <div class="mb-4">
              <label class="form-label">Logo Website</label>
              <div class="text-center mb-3">
                @if(isset($settings['site_logo']) && $settings['site_logo'] && file_exists(storage_path('app/public/' . $settings['site_logo'])))
                <img src="{{ asset('storage/' . $settings['site_logo']) }}" alt="Logo"
                     class="img-fluid rounded border" style="max-height: 100px;">
                @else
                <div class="bg-light border rounded d-flex align-items-center justify-content-center"
                     style="height: 100px;">
                  <i class="fa fa-image fa-2x text-muted"></i>
                  <div class="ms-2">
                    <small class="text-muted">Chưa Có Logo</small>
                  </div>
                </div>
                @endif
              </div>
              <input type="file" class="form-control @error('site_logo') is-invalid @enderror" 
                     name="site_logo" accept="image/*">
              <small class="text-muted">Định dạng: JPG, PNG, GIF. Tối đa: 2MB</small>
              @error('site_logo')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>

            <!-- Favicon -->
            <div class="mb-3">
              <label class="form-label">Favicon</label>
              <div class="text-center mb-3">
                @if(isset($settings['site_favicon']) && $settings['site_favicon'] && file_exists(storage_path('app/public/' . $settings['site_favicon'])))
                <img src="{{ asset('storage/' . $settings['site_favicon']) }}" alt="Favicon"
                     class="border rounded" style="width: 32px; height: 32px;">
                @else
                <div class="bg-light border rounded d-flex align-items-center justify-content-center mx-auto"
                     style="width: 32px; height: 32px;">
                  <i class="fa fa-star text-muted"></i>
                </div>
                <small class="text-muted d-block mt-1">Chưa Có Favicon</small>
                @endif
              </div>
              <input type="file" class="form-control @error('site_favicon') is-invalid @enderror" 
                     name="site_favicon" accept=".ico,.png">
              <small class="text-muted">Định dạng: ICO, PNG. Tối đa: 512KB</small>
              @error('site_favicon')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>

        <!-- Preview -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-eye me-2"></i>Xem Trước
            </h5>
          </div>
          <div class="card-body">
            <div class="border rounded p-3 bg-light">
              <div class="d-flex align-items-center mb-2">
                @if(isset($settings['site_logo']) && $settings['site_logo'] && file_exists(storage_path('app/public/' . $settings['site_logo'])))
                <img src="{{ asset('storage/' . $settings['site_logo']) }}" alt="Logo"
                     class="me-2" style="height: 30px;">
                @else
                <div class="bg-secondary rounded d-flex align-items-center justify-content-center me-2"
                     style="width: 30px; height: 30px;">
                  <i class="fa fa-image text-white" style="font-size: 12px;"></i>
                </div>
                @endif
                <strong>{{ $settings['site_name'] ?? 'PanDa Learning Platform' }}</strong>
              </div>
              <p class="text-muted small mb-2">{{ $settings['site_description'] ?? 'Mô tả website...' }}</p>
              <div class="small text-muted">
                <i class="fa fa-envelope me-1"></i>{{ $settings['contact_email'] ?? '<EMAIL>' }}
                @if($settings['contact_phone'])
                <br><i class="fa fa-phone me-1"></i>{{ $settings['contact_phone'] }}
                @endif
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Submit Buttons -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left me-2"></i>Quay Lại
              </a>
              <div>
                <button type="reset" class="btn btn-outline-secondary me-2">
                  <i class="fa fa-undo me-2"></i>Đặt Lại
                </button>
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-save me-2"></i>Lưu Cài Đặt
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<script>
// Preview logo upload
document.querySelector('input[name="site_logo"]').addEventListener('change', function(e) {
  const file = e.target.files[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = function(e) {
      const preview = document.querySelector('img[alt="Logo"]');
      if (preview) {
        preview.src = e.target.result;
      }
    };
    reader.readAsDataURL(file);
  }
});

// Preview favicon upload
document.querySelector('input[name="site_favicon"]').addEventListener('change', function(e) {
  const file = e.target.files[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = function(e) {
      const preview = document.querySelector('img[alt="Favicon"]');
      if (preview) {
        preview.src = e.target.result;
      }
    };
    reader.readAsDataURL(file);
  }
});

// Live preview updates
document.querySelector('input[name="site_name"]').addEventListener('input', function(e) {
  const preview = document.querySelector('.card-body strong');
  if (preview) {
    preview.textContent = e.target.value || 'Tên Website';
  }
});

document.querySelector('textarea[name="site_description"]').addEventListener('input', function(e) {
  const preview = document.querySelector('.text-muted.small.mb-2');
  if (preview) {
    preview.textContent = e.target.value || 'Mô tả website...';
  }
});
</script>
@endsection
