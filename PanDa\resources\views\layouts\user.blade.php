<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="csrf-token" content="{{ csrf_token() }}">

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="{{ asset('assets/images/logos/favicon.png') }}" />

  <!-- Core Css -->
  <link rel="stylesheet" href="{{ asset('assets/css/styles.css') }}" />

  <!-- Custom Css -->
  <link rel="stylesheet" href="{{ asset('assets/css/custom.css') }}" />

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <title>@yield('title', 'Hệ Thống Học Tập Online')</title>
</head>

<body class="link-sidebar">
  <!-- Preloader -->
  <div class="preloader">
    <img src="{{ asset('assets/images/logos/favicon.png') }}" alt="loader" class="lds-ripple img-fluid" />
  </div>
  
  <div id="main-wrapper">
    <!-- Sidebar Start -->
    <aside class="left-sidebar with-vertical">
      <div>
        <div class="brand-logo d-flex align-items-center">
          <a href="{{ route('user.dashboard') }}" class="text-nowrap logo-img">
            <img src="{{ asset('assets/images/logos/logo.svg') }}" alt="Logo" />
          </a>
        </div>

        <!-- Sidebar Navigation -->
        <nav class="sidebar-nav scroll-sidebar" data-simplebar>
          <ul class="sidebar-menu" id="sidebarnav">
            <!-- Dashboard -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Trang Chủ</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('user.dashboard') }}" aria-expanded="false">
                <i class="fa fa-home"></i>
                <span class="hide-menu">Bảng Điều Khiển</span>
              </a>
            </li>

            <!-- Khóa Học -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Khóa Học</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('courses.index') }}" aria-expanded="false">
                <i class="fa fa-book"></i>
                <span class="hide-menu">Tất Cả Khóa Học</span>
              </a>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('my-courses.index') }}" aria-expanded="false">
                <i class="fa fa-graduation-cap"></i>
                <span class="hide-menu">Khóa Học Của Tôi</span>
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('wishlist.index') }}" aria-expanded="false">
                <i class="fa fa-heart"></i>
                <span class="hide-menu">Yêu Thích</span>
                @if(auth()->check() && auth()->user()->wishlists()->count() > 0)
                <span class="badge bg-danger rounded-pill ms-2">{{ auth()->user()->wishlists()->count() }}</span>
                @endif
              </a>
            </li>

            <!-- Học Tập -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Học Tập</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('continue-learning.index') }}" aria-expanded="false">
                <i class="fa fa-play-circle"></i>
                <span class="hide-menu">Tiếp Tục Học</span>
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('documents.index') }}" aria-expanded="false">
                <i class="fa fa-file-text"></i>
                <span class="hide-menu">Tài Liệu</span>
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('user.certificates.index') }}" aria-expanded="false">
                <i class="fa fa-certificate"></i>
                <span class="hide-menu">Chứng Chỉ</span>
              </a>
            </li>

            <!-- Mua Sắm -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Mua Sắm</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('cart.index') }}" aria-expanded="false">
                <i class="fa fa-shopping-cart"></i>
                <span class="hide-menu">Giỏ Hàng</span>
                @if(auth()->check() && auth()->user()->cart_count > 0)
                <span class="badge bg-danger rounded-pill ms-2">{{ auth()->user()->cart_count }}</span>
                @endif
              </a>
            </li>



            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('orders.index') }}" aria-expanded="false">
                <i class="fa fa-history"></i>
                <span class="hide-menu">Đơn Hàng Của Tôi</span>
              </a>
            </li>

            <!-- Tài Khoản -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Tài Khoản</span>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('notifications.index') }}" aria-expanded="false">
                <i class="fa fa-bell"></i>
                <span class="hide-menu">Thông Báo</span>
                @if(auth()->check() && auth()->user()->unread_notifications_count > 0)
                <span class="badge bg-danger rounded-pill ms-2">{{ auth()->user()->unread_notifications_count }}</span>
                @endif
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('profile.index') }}" aria-expanded="false">
                <i class="fa fa-user"></i>
                <span class="hide-menu">Hồ Sơ Cá Nhân</span>
              </a>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('settings.index') }}" aria-expanded="false">
                <i class="fa fa-cog"></i>
                <span class="hide-menu">Cài Đặt</span>
              </a>
            </li>

            @if(auth()->check() && auth()->user()->role === 'admin')
            <!-- Chuyển Đổi Giao Diện -->
            <li class="nav-small-cap">
              <i class="fa fa-code mini-icon"></i>
              <span class="hide-menu">Chuyển Đổi</span>
            </li>

            <li class="sidebar-item">
              <a class="sidebar-link" href="{{ route('admin.dashboard') }}" aria-expanded="false">
                <i class="fa fa-user-shield"></i>
                <span class="hide-menu"> Về Trang Quản Trị</span>
              </a>
            </li>
            @endif
          </ul>
        </nav>
      </div>
    </aside>
    <!-- Sidebar End -->

    <!-- Main wrapper -->
    <div class="page-wrapper">
      <!-- Header Start -->
      <header class="topbar">
        <div class="with-vertical">
          <nav class="navbar navbar-expand-lg p-0">
            <ul class="navbar-nav">
              <li class="nav-item nav-icon-hover-bg rounded-circle">
                <a class="nav-link sidebartoggler" id="headerCollapse" href="javascript:void(0)">
                  <i class="fa fa-bars"></i>
                </a>
              </li>
            </ul>

            <div class="d-block d-lg-none py-4">
              <a href="{{ route('user.dashboard') }}" class="text-nowrap logo-img">
                <img src="{{ asset('assets/images/logos/logo.svg') }}" alt="Logo" />
              </a>
            </div>

            <a class="navbar-toggler nav-icon-hover-bg rounded-circle p-0 mx-0 border-0" href="javascript:void(0)" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
              <i class="fa fa-ellipsis-v"></i>
            </a>

            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
              <div class="d-flex align-items-center justify-content-between">
                <!-- Search -->
                <div class="me-3 d-none d-lg-block">
                  <div class="input-group">
                    <input type="text" class="form-control" placeholder="Tìm Kiếm Khóa Học..." style="width: 300px;">
                    <button class="btn btn-outline-secondary" type="button">
                      <i class="fa fa-search"></i>
                    </button>
                  </div>
                </div>

                <ul class="navbar-nav flex-row ms-auto align-items-center justify-content-center">
                  <!-- Notifications -->
                  <li class="nav-item dropdown">
                    <a class="nav-link nav-icon-hover-bg rounded-circle" href="javascript:void(0)" id="drop1" data-bs-toggle="dropdown" aria-expanded="false">
                      <i class="fa fa-bell"></i>
                      @if(auth()->check() && auth()->user()->unread_notifications_count > 0)
                      <span class="badge bg-danger rounded-pill position-absolute top-0 start-100 translate-middle" style="font-size: 0.6rem;">{{ auth()->user()->unread_notifications_count }}</span>
                      @endif
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-animate-up" aria-labelledby="drop1" style="width: 300px;">
                      <div class="message-body">
                        <div class="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
                          <h6 class="fw-semibold mb-0">Thông Báo</h6>
                          <a href="{{ route('notifications.index') }}" class="btn btn-sm btn-outline-primary">Xem Tất Cả</a>
                        </div>

                        <div id="notification-dropdown-content">
                          @if(auth()->check() && auth()->user()->unread_notifications_count > 0)
                          <div class="px-3 py-2 text-center">
                            <small class="text-muted">Đang tải...</small>
                          </div>
                          @else
                          <div class="px-3 py-2 text-center">
                            <small class="text-muted">Chưa Có Thông Báo Mới</small>
                          </div>
                          @endif
                        </div>
                      </div>
                    </div>
                  </li>

                  <!-- User Profile -->
                  <li class="nav-item dropdown">
                    <a class="nav-link nav-icon-hover-bg rounded-circle" href="javascript:void(0)" id="drop2" aria-expanded="false">
                      @if(auth()->check() && auth()->user()->avatar)
                      <img src="{{ asset('storage/' . auth()->user()->avatar) }}" alt="Avatar" width="35" height="35" class="rounded-circle">
                      @else
                      <i class="fa fa-user"></i>
                      @endif
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-animate-up" aria-labelledby="drop2">
                      <div class="message-body">
                        <div class="mx-3 mt-2 mb-3 d-flex align-items-center">
                          @if(auth()->check() && auth()->user()->avatar)
                          <img src="{{ asset('storage/' . auth()->user()->avatar) }}" alt="Avatar" width="40" height="40" class="rounded-circle me-3">
                          @else
                          <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            <i class="fa fa-user text-muted"></i>
                          </div>
                          @endif
                          <div>
                            <h6 class="mb-0">{{ auth()->check() ? auth()->user()->name : 'Guest' }}</h6>
                            <span class="fs-12 text-muted">Học Viên</span>
                          </div>
                        </div>
                        
                        <a href="{{ route('profile.index') }}" class="d-flex align-items-center gap-2 dropdown-item">
                          <i class="fa fa-user fs-4"></i>
                          <p class="mb-0 fs-3">Hồ Sơ Cá Nhân</p>
                        </a>
                        
                        <a href="#" class="d-flex align-items-center gap-2 dropdown-item">
                          <i class="fa fa-cog fs-4"></i>
                          <p class="mb-0 fs-3">Cài Đặt</p>
                        </a>
                        
                        <form method="POST" action="{{ route('logout') }}">
                          @csrf
                          <button type="submit" class="btn btn-outline-primary mx-3 mt-2 d-block w-100">
                            <i class="fa fa-sign-out-alt me-2"></i>Đăng Xuất
                          </button>
                        </form>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </header>
      <!-- Header End -->

      <!-- Main Content -->
      <div class="body-wrapper">
        <div class="container-fluid">
          @if (session('success'))
          <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fa fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
          @endif

          @if (session('error'))
          <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fa fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
          @endif

          @yield('content')
        </div>
      </div>
    </div>
  </div>

  <!-- Import Js Files -->
  <script src="{{ asset('assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>
  <script src="{{ asset('assets/libs/simplebar/dist/simplebar.min.js') }}"></script>
  <script src="{{ asset('assets/js/theme/app.init.js') }}"></script>
  <script src="{{ asset('assets/js/theme/theme.js') }}"></script>
  <script src="{{ asset('assets/js/theme/app.min.js') }}"></script>

  <!-- Image Fallback Handler -->
  <script src="{{ asset('js/image-fallback.js') }}"></script>

  <!-- Solar Icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>

  @stack('scripts')

  <!-- Notification Script -->
  <script>
  document.addEventListener('DOMContentLoaded', function() {
    const notificationDropdown = document.getElementById('drop1');
    const notificationContent = document.getElementById('notification-dropdown-content');

    if (notificationDropdown && notificationContent) {
      notificationDropdown.addEventListener('click', function() {
        // Load notifications when dropdown is clicked
        if ({{ auth()->check() ? auth()->user()->unread_notifications_count : 0 }} > 0) {
          loadNotifications();
        }
      });
    }

    function loadNotifications() {
      fetch('{{ route("notifications.api.unread") }}')
        .then(response => response.json())
        .then(data => {
          if (data.notifications && data.notifications.length > 0) {
            let html = '';
            data.notifications.forEach(notification => {
              html += `
                <div class="px-3 py-2 border-bottom">
                  <div class="d-flex align-items-start">
                    <i class="fa ${getNotificationIcon(notification.type)} ${getNotificationClass(notification.type)} me-2 mt-1"></i>
                    <div class="flex-grow-1">
                      <h6 class="mb-1 fs-6">${notification.title}</h6>
                      <p class="mb-1 small text-muted">${notification.message}</p>
                      <small class="text-muted">${formatTime(notification.created_at)}</small>
                    </div>
                  </div>
                </div>
              `;
            });
            notificationContent.innerHTML = html;
          } else {
            notificationContent.innerHTML = '<div class="px-3 py-2 text-center"><small class="text-muted">Chưa Có Thông Báo Mới</small></div>';
          }
        })
        .catch(error => {
          console.error('Error loading notifications:', error);
        });
    }

    function getNotificationIcon(type) {
      switch(type) {
        case 'success': return 'fa-check-circle';
        case 'warning': return 'fa-exclamation-triangle';
        case 'error': return 'fa-times-circle';
        default: return 'fa-info-circle';
      }
    }

    function getNotificationClass(type) {
      switch(type) {
        case 'success': return 'text-success';
        case 'warning': return 'text-warning';
        case 'error': return 'text-danger';
        default: return 'text-info';
      }
    }

    function formatTime(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      const diff = now - date;
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);

      if (days > 0) return `${days} ngày trước`;
      if (hours > 0) return `${hours} giờ trước`;
      if (minutes > 0) return `${minutes} phút trước`;
      return 'Vừa xong';
    }
  });
  </script>
</body>

</html>
