@extends('layouts.admin')

@section('title', '<PERSON> Tiết Đơn Hàng')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Chi Tiết Đơn Hàng</h4>
        <p class="text-muted mb-0">{{ $order->order_number }}</p>
      </div>
      <div>
        <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-8">
    <!-- Thông Tin Đơn Hàng -->
    <div class="card mb-4">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>{{ $order->order_number }}
          </h5>
          <div>
            @if($order->status == 'pending')
            <span class="badge bg-warning fs-6">{{ $order->status_text }}</span>
            @elseif($order->status == 'approved')
            <span class="badge bg-success fs-6">{{ $order->status_text }}</span>
            @elseif($order->status == 'rejected')
            <span class="badge bg-danger fs-6">{{ $order->status_text }}</span>
            @else
            <span class="badge bg-info fs-6">{{ $order->status_text }}</span>
            @endif
          </div>
        </div>
      </div>
      
      <div class="card-body">
        <div class="row mb-4">
          <div class="col-md-6">
            <h6>Thông Tin Khách Hàng</h6>
            <div class="mb-2">
              <small class="text-muted">Tên:</small>
              <span class="fw-bold">{{ $order->user->name }}</span>
            </div>
            <div class="mb-2">
              <small class="text-muted">Email:</small>
              <span class="fw-bold">{{ $order->user->email }}</span>
            </div>
            <div class="mb-2">
              <small class="text-muted">Ngày Đăng Ký:</small>
              <span class="fw-bold">{{ $order->user->created_at->format('d/m/Y') }}</span>
            </div>
          </div>
          
          <div class="col-md-6">
            <h6>Thông Tin Đơn Hàng</h6>
            <div class="mb-2">
              <small class="text-muted">Mã Đơn:</small>
              <span class="fw-bold">{{ $order->order_number }}</span>
            </div>
            <div class="mb-2">
              <small class="text-muted">Ngày Tạo:</small>
              <span class="fw-bold">{{ $order->created_at->format('d/m/Y H:i') }}</span>
            </div>
            <div class="mb-2">
              <small class="text-muted">Phương Thức:</small>
              <span class="fw-bold">{{ $order->payment_method_text }}</span>
            </div>
          </div>
        </div>
        
        @if($order->note)
        <div class="mb-4">
          <h6>Ghi Chú Từ Khách Hàng</h6>
          <div class="alert alert-light">
            {{ $order->note }}
          </div>
        </div>
        @endif
        
        @if($order->admin_note)
        <div class="mb-4">
          <h6>Ghi Chú Admin</h6>
          <div class="alert alert-info">
            <i class="fa fa-info-circle me-2"></i>
            {{ $order->admin_note }}
          </div>
        </div>
        @endif
        
        @if($order->approved_at)
        <div class="mb-4">
          <h6>Thông Tin Duyệt</h6>
          <div class="row">
            <div class="col-md-6">
              <small class="text-muted">Ngày Duyệt:</small>
              <div class="fw-bold">{{ $order->approved_at->format('d/m/Y H:i') }}</div>
            </div>
            @if($order->approvedBy)
            <div class="col-md-6">
              <small class="text-muted">Người Duyệt:</small>
              <div class="fw-bold">{{ $order->approvedBy->name }}</div>
            </div>
            @endif
          </div>
        </div>
        @endif
      </div>
    </div>

    <!-- Danh Sách Khóa Học -->
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Khóa Học ({{ $order->items->count() }})
        </h5>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Khóa Học</th>
                <th>Danh Mục</th>
                <th>Ghi Chú</th>
                <th>Giá</th>
              </tr>
            </thead>
            <tbody>
              @foreach($order->items as $item)
              <tr>
                <td>
                  <div>
                    <h6 class="mb-1">{{ $item->course_title }}</h6>
                    @if($item->course)
                    <small class="text-muted">ID: {{ $item->course->id }}</small>
                    @else
                    <small class="text-danger">Khóa học đã bị xóa</small>
                    @endif
                  </div>
                </td>
                <td>
                  @if($item->course && $item->course->category)
                  <span class="badge bg-primary">{{ $item->course->category->name }}</span>
                  @else
                  <span class="text-muted">N/A</span>
                  @endif
                </td>
                <td>
                  @if($item->note)
                  <span class="text-muted">{{ $item->note }}</span>
                  @else
                  <span class="text-muted">-</span>
                  @endif
                </td>
                <td>
                  <span class="fw-bold text-success">
                    @if($item->course_price > 0)
                    {{ number_format($item->course_price, 0, ',', '.') }}đ
                    @else
                    Miễn Phí
                    @endif
                  </span>
                </td>
              </tr>
              @endforeach
            </tbody>
            <tfoot class="table-primary">
              <tr>
                <th colspan="3">Tổng Cộng</th>
                <th class="text-success">{{ number_format($order->total_amount, 0, ',', '.') }}đ</th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="col-lg-4">
    <!-- Tóm Tắt -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Tóm Tắt Đơn Hàng
        </h6>
      </div>
      <div class="card-body">
        <div class="d-flex justify-content-between mb-2">
          <span>Số Khóa Học:</span>
          <span class="fw-bold">{{ $order->items->count() }}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
          <span>Phương Thức:</span>
          <span class="fw-bold">{{ $order->payment_method_text }}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
          <span>Trạng Thái:</span>
          <span class="fw-bold">{{ $order->status_text }}</span>
        </div>
        <hr>
        <div class="d-flex justify-content-between">
          <span class="h6">Tổng Tiền:</span>
          <span class="h6 text-success">{{ number_format($order->total_amount, 0, ',', '.') }}đ</span>
        </div>
      </div>
    </div>

    <!-- Hành Động -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Hành Động
        </h6>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          @if($order->status == 'pending')
          <button class="btn btn-success" onclick="approveOrder({{ $order->id }})">
            <i class="fa fa-check me-2"></i>Duyệt Đơn Hàng
          </button>
          <button class="btn btn-danger" onclick="rejectOrder({{ $order->id }})">
            <i class="fa fa-times me-2"></i>Từ Chối
          </button>
          @elseif($order->status == 'approved')
          <button class="btn btn-primary" onclick="completeOrder({{ $order->id }})">
            <i class="fa fa-check-double me-2"></i>Hoàn Thành
          </button>
          @endif
          
          <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary">
            <i class="fa fa-list me-2"></i>Tất Cả Đơn Hàng
          </a>
          
          @if(in_array($order->status, ['pending', 'rejected']))
          <form method="POST" action="{{ route('admin.orders.destroy', $order->id) }}" class="d-inline">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Bạn Có Chắc Muốn Xóa?')">
              <i class="fa fa-trash me-2"></i>Xóa Đơn Hàng
            </button>
          </form>
          @endif
        </div>
      </div>
    </div>

    <!-- Thông Tin Khách Hàng -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Thông Tin Khách Hàng
        </h6>
      </div>
      <div class="card-body">
        <div class="text-center mb-3">
          <i class="fa fa-user-circle fa-3x text-muted"></i>
          <h6 class="mt-2">{{ $order->user->name }}</h6>
          <small class="text-muted">{{ $order->user->email }}</small>
        </div>
        
        <div class="mb-2">
          <small class="text-muted">Tổng Đơn Hàng:</small>
          <span class="fw-bold">{{ $order->user->orders()->count() }}</span>
        </div>
        <div class="mb-2">
          <small class="text-muted">Đã Duyệt:</small>
          <span class="fw-bold text-success">{{ $order->user->orders()->approved()->count() }}</span>
        </div>
        <div class="mb-2">
          <small class="text-muted">Tổng Chi Tiêu:</small>
          <span class="fw-bold text-primary">{{ number_format($order->user->orders()->approved()->sum('total_amount'), 0, ',', '.') }}đ</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal Duyệt -->
<div class="modal fade" id="approveModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Duyệt Đơn Hàng</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <form id="approveForm" method="POST">
        @csrf
        <div class="modal-body">
          <div class="alert alert-info">
            <i class="fa fa-info-circle me-2"></i>
            Bạn có chắc muốn duyệt đơn hàng <strong>{{ $order->order_number }}</strong>?
          </div>
          <div class="mb-3">
            <label for="admin_note_approve" class="form-label">Ghi Chú Admin (Tùy Chọn)</label>
            <textarea class="form-control" id="admin_note_approve" name="admin_note" rows="3" placeholder="Nhập ghi chú..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-success">Duyệt Đơn Hàng</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal Từ Chối -->
<div class="modal fade" id="rejectModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Từ Chối Đơn Hàng</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <form id="rejectForm" method="POST">
        @csrf
        <div class="modal-body">
          <div class="alert alert-warning">
            <i class="fa fa-exclamation-triangle me-2"></i>
            Bạn có chắc muốn từ chối đơn hàng <strong>{{ $order->order_number }}</strong>?
          </div>
          <div class="mb-3">
            <label for="admin_note_reject" class="form-label">Lý Do Từ Chối <span class="text-danger">*</span></label>
            <textarea class="form-control" id="admin_note_reject" name="admin_note" rows="3" placeholder="Nhập lý do từ chối..." required></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-danger">Từ Chối Đơn Hàng</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
function approveOrder(orderId) {
  document.getElementById('approveForm').action = `/admin/orders/${orderId}/approve`;
  new bootstrap.Modal(document.getElementById('approveModal')).show();
}

function rejectOrder(orderId) {
  document.getElementById('rejectForm').action = `/admin/orders/${orderId}/reject`;
  new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

function completeOrder(orderId) {
  if (confirm('Bạn có chắc muốn hoàn thành đơn hàng này?')) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/orders/${orderId}/complete`;
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    form.appendChild(csrfToken);
    document.body.appendChild(form);
    form.submit();
  }
}
</script>
@endsection
