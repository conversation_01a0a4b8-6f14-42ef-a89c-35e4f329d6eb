<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\Chapter;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class LessonController extends Controller
{
    /**
     * Hiển <PERSON>h<PERSON>
     */
    public function index(Request $request)
    {
        $chapterId = $request->get('chapter_id');
        $chapter = null;

        if ($chapterId) {
            $chapter = Chapter::with('course')->findOrFail($chapterId);
            $lessons = $chapter->lessons()->orderBy('position')->paginate(10);
        } else {
            $lessons = Lesson::with('chapter.course')->latest()->paginate(10);
        }

        $chapters = Chapter::with('course')->get();

        return view('admin.lessons.index', compact('lessons', 'chapters', 'chapter'));
    }

    /**
     * <PERSON>ển Thị Form Tạo Bà<PERSON>
     */
    public function create(Request $request)
    {
        $chapterId = $request->get('chapter_id');
        $chapter = $chapterId ? Chapter::with('course')->findOrFail($chapterId) : null;
        $chapters = Chapter::with('course')->get();

        return view('admin.lessons.create', compact('chapters', 'chapter'));
    }

    /**
     * Lưu Bài Học Mới
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'chapter_id' => 'required|exists:chapters,id',
            'title' => 'required|string|max:255',
            'type' => 'required|in:video,text,pdf,quiz',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url',
            'file_path' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
            'exercise_files.*' => 'nullable|file|mimes:pdf,doc,docx,zip,rar|max:20480',
            'duration_minutes' => 'required|integer|min:1',
            'position' => 'required|integer|min:1',
        ], [
            'chapter_id.required' => 'Chương Là Bắt Buộc',
            'title.required' => 'Tiêu Đề Bài Học Là Bắt Buộc',
            'type.required' => 'Loại Bài Học Là Bắt Buộc',
            'duration_minutes.required' => 'Thời Lượng Là Bắt Buộc',
            'position.required' => 'Vị Trí Là Bắt Buộc',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Xử Lý Upload File
        if ($request->hasFile('file_path')) {
            $data['file_path'] = $request->file('file_path')->store('lessons', 'public');
        }

        // Xử Lý Upload Nhiều File Bài Tập
        if ($request->hasFile('exercise_files')) {
            $exerciseFiles = [];
            foreach ($request->file('exercise_files') as $file) {
                if ($file) {
                    $exerciseFiles[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $file->store('exercises', 'public'),
                        'size' => $file->getSize(),
                        'type' => $file->getClientOriginalExtension()
                    ];
                }
            }
            $data['exercise_files'] = $exerciseFiles;
        }

        Lesson::create($data);

        return redirect()->route('admin.lessons.index', ['chapter_id' => $request->chapter_id])
            ->with('success', 'Tạo Bài Học Thành Công!');
    }

    /**
     * Hiển Thị Chi Tiết Bài Học
     */
    public function show($id)
    {
        $lesson = Lesson::with('chapter.course')->findOrFail($id);
        return view('admin.lessons.show', compact('lesson'));
    }

    /**
     * Hiển Thị Form Chỉnh Sửa
     */
    public function edit($id)
    {
        $lesson = Lesson::with('chapter.course')->findOrFail($id);
        $chapters = Chapter::with('course')->get();
        return view('admin.lessons.edit', compact('lesson', 'chapters'));
    }

    /**
     * Cập Nhật Bài Học
     */
    public function update(Request $request, $id)
    {
        $lesson = Lesson::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'chapter_id' => 'required|exists:chapters,id',
            'title' => 'required|string|max:255',
            'type' => 'required|in:video,text,pdf,quiz',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url',
            'file_path' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
            'exercise_files.*' => 'nullable|file|mimes:pdf,doc,docx,zip,rar|max:20480',
            'duration_minutes' => 'required|integer|min:1',
            'position' => 'required|integer|min:1',
        ], [
            'chapter_id.required' => 'Chương Là Bắt Buộc',
            'title.required' => 'Tiêu Đề Bài Học Là Bắt Buộc',
            'type.required' => 'Loại Bài Học Là Bắt Buộc',
            'duration_minutes.required' => 'Thời Lượng Là Bắt Buộc',
            'position.required' => 'Vị Trí Là Bắt Buộc',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Xử Lý Upload File
        if ($request->hasFile('file_path')) {
            // Xóa File Cũ
            if ($lesson->file_path && Storage::exists('public/' . $lesson->file_path)) {
                Storage::delete('public/' . $lesson->file_path);
            }
            $data['file_path'] = $request->file('file_path')->store('lessons', 'public');
        }

        // Xử Lý Upload Nhiều File Bài Tập
        if ($request->hasFile('exercise_files')) {
            // Xóa File Cũ
            if ($lesson->exercise_files) {
                foreach ($lesson->exercise_files as $oldFile) {
                    if (isset($oldFile['path']) && Storage::exists('public/' . $oldFile['path'])) {
                        Storage::delete('public/' . $oldFile['path']);
                    }
                }
            }

            $exerciseFiles = [];
            foreach ($request->file('exercise_files') as $file) {
                if ($file) {
                    $exerciseFiles[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $file->store('exercises', 'public'),
                        'size' => $file->getSize(),
                        'type' => $file->getClientOriginalExtension()
                    ];
                }
            }
            $data['exercise_files'] = $exerciseFiles;
        }

        $lesson->update($data);

        return redirect()->route('admin.lessons.index', ['chapter_id' => $lesson->chapter_id])
            ->with('success', 'Cập Nhật Bài Học Thành Công!');
    }

    /**
     * Xóa Bài Học
     */
    public function destroy($id)
    {
        $lesson = Lesson::findOrFail($id);
        $chapterId = $lesson->chapter_id;

        // Xóa File
        if ($lesson->file_path && Storage::exists('public/' . $lesson->file_path)) {
            Storage::delete('public/' . $lesson->file_path);
        }

        // Xóa File Bài Tập
        if ($lesson->exercise_files) {
            foreach ($lesson->exercise_files as $file) {
                if (isset($file['path']) && Storage::exists('public/' . $file['path'])) {
                    Storage::delete('public/' . $file['path']);
                }
            }
        }

        $lesson->delete();

        return redirect()->route('admin.lessons.index', ['chapter_id' => $chapterId])
            ->with('success', 'Xóa Bài Học Thành Công!');
    }
}
