@extends('layouts.user')

@section('title', 'Cài Đặt')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Cài Đặt</h4>
        <p class="text-muted mb-0">Tùy Chỉnh Trải Nghiệm Học Tập Của Bạn</p>
      </div>
      <div>
        <div class="dropdown">
          <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fa fa-cog me-2"></i>Tùy <PERSON>
          </button>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="{{ route('settings.export') }}">
              <i class="fa fa-download me-2"></i>Xuất Cài Đặt
            </a></li>
            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importModal">
              <i class="fa fa-upload me-2"></i>Nhập Cài Đặt
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-danger" href="{{ route('settings.reset') }}" onclick="return confirm('Bạn Có Chắc Muốn Reset Tất Cả Cài Đặt?')">
              <i class="fa fa-refresh me-2"></i>Reset Tất Cả
            </a></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-3">
    <!-- Navigation Tabs -->
    <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist">
      <button class="nav-link active" id="v-pills-notifications-tab" data-bs-toggle="pill" data-bs-target="#v-pills-notifications" type="button">
        <i class="fa fa-bell me-2"></i>Thông Báo
      </button>
      <button class="nav-link" id="v-pills-privacy-tab" data-bs-toggle="pill" data-bs-target="#v-pills-privacy" type="button">
        <i class="fa fa-shield me-2"></i>Quyền Riêng Tư
      </button>
      <button class="nav-link" id="v-pills-learning-tab" data-bs-toggle="pill" data-bs-target="#v-pills-learning" type="button">
        <i class="fa fa-graduation-cap me-2"></i>Học Tập
      </button>
      <button class="nav-link" id="v-pills-interface-tab" data-bs-toggle="pill" data-bs-target="#v-pills-interface" type="button">
        <i class="fa fa-desktop me-2"></i>Giao Diện
      </button>
    </div>
  </div>
  
  <div class="col-lg-9">
    <div class="tab-content" id="v-pills-tabContent">
      
      <!-- Cài Đặt Thông Báo -->
      <div class="tab-pane fade show active" id="v-pills-notifications">
        <div class="card">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="fa fa-bell me-2"></i>Cài Đặt Thông Báo
            </h6>
          </div>
          <div class="card-body">
            <form method="POST" action="{{ route('settings.notifications') }}">
              @csrf
              
              <div class="mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="email_notifications" value="1" 
                         {{ $settings['notifications']['email_notifications'] ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Thông Báo Email</strong>
                    <br><small class="text-muted">Nhận Thông Báo Qua Email</small>
                  </label>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="order_updates" value="1" 
                         {{ $settings['notifications']['order_updates'] ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Cập Nhật Đơn Hàng</strong>
                    <br><small class="text-muted">Thông Báo Khi Đơn Hàng Thay Đổi Trạng Thái</small>
                  </label>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="course_reminders" value="1" 
                         {{ $settings['notifications']['course_reminders'] ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Nhắc Nhở Học Tập</strong>
                    <br><small class="text-muted">Nhắc Nhở Tiếp Tục Học Các Khóa Học</small>
                  </label>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="marketing_emails" value="1" 
                         {{ $settings['notifications']['marketing_emails'] ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Email Marketing</strong>
                    <br><small class="text-muted">Nhận Thông Tin Khuyến Mãi Và Khóa Học Mới</small>
                  </label>
                </div>
              </div>
              
              <button type="submit" class="btn btn-primary">
                <i class="fa fa-save me-2"></i>Lưu Cài Đặt
              </button>
            </form>
          </div>
        </div>
      </div>
      
      <!-- Cài Đặt Quyền Riêng Tư -->
      <div class="tab-pane fade" id="v-pills-privacy">
        <div class="card">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="fa fa-shield me-2"></i>Cài Đặt Quyền Riêng Tư
            </h6>
          </div>
          <div class="card-body">
            <form method="POST" action="{{ route('settings.privacy') }}">
              @csrf
              
              <div class="mb-3">
                <label class="form-label">Hiển Thị Hồ Sơ</label>
                <select class="form-select" name="profile_visibility">
                  <option value="public" {{ $settings['privacy']['profile_visibility'] == 'public' ? 'selected' : '' }}>Công Khai</option>
                  <option value="private" {{ $settings['privacy']['profile_visibility'] == 'private' ? 'selected' : '' }}>Riêng Tư</option>
                  <option value="friends" {{ $settings['privacy']['profile_visibility'] == 'friends' ? 'selected' : '' }}>Chỉ Bạn Bè</option>
                </select>
                <small class="text-muted">Ai Có Thể Xem Hồ Sơ Của Bạn</small>
              </div>
              
              <div class="mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="show_progress" value="1" 
                         {{ $settings['privacy']['show_progress'] ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Hiển Thị Tiến Độ</strong>
                    <br><small class="text-muted">Cho Phép Người Khác Xem Tiến Độ Học Tập</small>
                  </label>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="show_certificates" value="1" 
                         {{ $settings['privacy']['show_certificates'] ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Hiển Thị Chứng Chỉ</strong>
                    <br><small class="text-muted">Cho Phép Người Khác Xem Chứng Chỉ Của Bạn</small>
                  </label>
                </div>
              </div>
              
              <button type="submit" class="btn btn-primary">
                <i class="fa fa-save me-2"></i>Lưu Cài Đặt
              </button>
            </form>
          </div>
        </div>
      </div>
      
      <!-- Cài Đặt Học Tập -->
      <div class="tab-pane fade" id="v-pills-learning">
        <div class="card">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="fa fa-graduation-cap me-2"></i>Cài Đặt Học Tập
            </h6>
          </div>
          <div class="card-body">
            <form method="POST" action="{{ route('settings.learning') }}">
              @csrf
              
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">Mục Tiêu Hàng Ngày (Phút)</label>
                  <input type="number" class="form-control" name="daily_goal_minutes" 
                         value="{{ $settings['learning']['daily_goal_minutes'] }}" min="5" max="480">
                  <small class="text-muted">Thời Gian Học Mỗi Ngày</small>
                </div>
                
                <div class="col-md-6 mb-3">
                  <label class="form-label">Mục Tiêu Hàng Tuần (Bài Học)</label>
                  <input type="number" class="form-control" name="weekly_goal_lessons" 
                         value="{{ $settings['learning']['weekly_goal_lessons'] }}" min="1" max="50">
                  <small class="text-muted">Số Bài Học Hoàn Thành Mỗi Tuần</small>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="auto_play_next" value="1" 
                         {{ $settings['learning']['auto_play_next'] ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Tự Động Phát Bài Tiếp Theo</strong>
                    <br><small class="text-muted">Tự Động Chuyển Sang Bài Học Tiếp Theo</small>
                  </label>
                </div>
              </div>
              
              <div class="mb-3">
                <label class="form-label">Chất Lượng Video</label>
                <select class="form-select" name="video_quality">
                  <option value="auto" {{ $settings['learning']['video_quality'] == 'auto' ? 'selected' : '' }}>Tự Động</option>
                  <option value="720p" {{ $settings['learning']['video_quality'] == '720p' ? 'selected' : '' }}>720p Hd</option>
                  <option value="480p" {{ $settings['learning']['video_quality'] == '480p' ? 'selected' : '' }}>480p</option>
                  <option value="360p" {{ $settings['learning']['video_quality'] == '360p' ? 'selected' : '' }}>360p</option>
                </select>
                <small class="text-muted">Chất Lượng Video Mặc Định</small>
              </div>
              
              <button type="submit" class="btn btn-primary">
                <i class="fa fa-save me-2"></i>Lưu Cài Đặt
              </button>
            </form>
          </div>
        </div>
      </div>
      
      <!-- Cài Đặt Giao Diện -->
      <div class="tab-pane fade" id="v-pills-interface">
        <div class="card">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="fa fa-desktop me-2"></i>Cài Đặt Giao Diện
            </h6>
          </div>
          <div class="card-body">
            <form method="POST" action="{{ route('settings.interface') }}">
              @csrf
              
              <div class="mb-3">
                <label class="form-label">Giao Diện</label>
                <select class="form-select" name="theme">
                  <option value="light" {{ $settings['interface']['theme'] == 'light' ? 'selected' : '' }}>Sáng</option>
                  <option value="dark" {{ $settings['interface']['theme'] == 'dark' ? 'selected' : '' }}>Tối</option>
                  <option value="auto" {{ $settings['interface']['theme'] == 'auto' ? 'selected' : '' }}>Tự Động</option>
                </select>
                <small class="text-muted">Chọn Giao Diện Hiển Thị</small>
              </div>
              
              <div class="mb-3">
                <label class="form-label">Ngôn Ngữ</label>
                <select class="form-select" name="language">
                  <option value="vi" {{ $settings['interface']['language'] == 'vi' ? 'selected' : '' }}>Tiếng Việt</option>
                  <option value="en" {{ $settings['interface']['language'] == 'en' ? 'selected' : '' }}>Tiếng Anh</option>
                </select>
                <small class="text-muted">Ngôn Ngữ Hiển Thị</small>
              </div>
              
              <div class="mb-3">
                <label class="form-label">Múi Giờ</label>
                <select class="form-select" name="timezone">
                  <option value="Asia/Ho_Chi_Minh" {{ $settings['interface']['timezone'] == 'Asia/Ho_Chi_Minh' ? 'selected' : '' }}>Việt Nam (Gmt+7)</option>
                  <option value="Asia/Bangkok" {{ $settings['interface']['timezone'] == 'Asia/Bangkok' ? 'selected' : '' }}>Bangkok (Gmt+7)</option>
                  <option value="Asia/Singapore" {{ $settings['interface']['timezone'] == 'Asia/Singapore' ? 'selected' : '' }}>Singapore (Gmt+8)</option>
                </select>
                <small class="text-muted">Múi Giờ Hiển Thị</small>
              </div>
              
              <button type="submit" class="btn btn-primary">
                <i class="fa fa-save me-2"></i>Lưu Cài Đặt
              </button>
            </form>
          </div>
        </div>
      </div>
      
    </div>
  </div>
</div>

<!-- Modal Nhập Cài Đặt -->
<div class="modal fade" id="importModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Nhập Cài Đặt</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <form method="POST" action="{{ route('settings.import') }}" enctype="multipart/form-data">
        @csrf
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">Chọn File Cài Đặt</label>
            <input type="file" class="form-control" name="settings_file" accept=".json" required>
            <small class="text-muted">Chỉ Chấp Nhận File Json Được Xuất Từ Hệ Thống</small>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-primary">Nhập Cài Đặt</button>
        </div>
      </form>
    </div>
  </div>
</div>
@endsection
