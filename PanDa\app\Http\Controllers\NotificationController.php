<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    /**
     * Hi<PERSON>n <PERSON>h<PERSON> Sách Thông Báo
     */
    public function index()
    {
        $notifications = auth()->user()->notifications()
            ->latest()
            ->paginate(15);

        return view('notifications.index', compact('notifications'));
    }

    /**
     * Đ<PERSON>h Dấu Đã Đ<PERSON>c
     */
    public function markAsRead($id)
    {
        $notification = auth()->user()->notifications()->findOrFail($id);
        $notification->markAsRead();

        return back()->with('success', 'Đ<PERSON> Đánh Dấu Đ<PERSON>c Thông Báo');
    }

    /**
     * Đ<PERSON>h <PERSON>u Tất Cả Đã Đọc
     */
    public function markAllAsRead()
    {
        auth()->user()->notifications()->unread()->update(['read_at' => now()]);

        return back()->with('success', 'Đ<PERSON> Đ<PERSON>h <PERSON>t Cả Thông Báo Là Đã Đọc');
    }

    /**
     * Xóa Thông Báo
     */
    public function destroy($id)
    {
        $notification = auth()->user()->notifications()->findOrFail($id);
        $notification->delete();

        return back()->with('success', 'Đã Xóa Thông Báo');
    }

    /**
     * Lấy Thông Báo Chưa Đọc (API)
     */
    public function getUnread()
    {
        $notifications = auth()->user()->notifications()
            ->unread()
            ->latest()
            ->limit(10)
            ->get();

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => auth()->user()->notifications()->unread()->count()
        ]);
    }
}
