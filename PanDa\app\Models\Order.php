<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    protected $fillable = [
        'order_number',
        'user_id',
        'total_amount',
        'status',
        'payment_method',
        'note',
        'admin_note',
        'approved_at',
        'approved_by',
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    /**
     * Tạo Số Đơn Hàng Tự Động
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (!$order->order_number) {
                $order->order_number = 'ORD-' . date('Ymd') . '-' . str_pad(static::whereDate('created_at', today())->count() + 1, 4, '0', STR_PAD_LEFT);
            }
        });
    }

    /**
     * <PERSON>uan Hệ Với User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Quan Hệ Với Order Items
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Quan Hệ Với Admin Duyệt
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope Theo Trạng Thái
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Accessor Cho Trạng Thái
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'pending' => 'Chờ Duyệt',
            'approved' => 'Đã Duyệt',
            'rejected' => 'Từ Chối',
            'completed' => 'Hoàn Thành',
            default => 'Không Xác Định'
        };
    }

    /**
     * Accessor Cho Phương Thức Thanh Toán
     */
    public function getPaymentMethodTextAttribute()
    {
        return match($this->payment_method) {
            'bank_transfer' => 'Chuyển Khoản',
            'cash' => 'Tiền Mặt',
            'other' => 'Khác',
            default => 'Không Xác Định'
        };
    }
}
