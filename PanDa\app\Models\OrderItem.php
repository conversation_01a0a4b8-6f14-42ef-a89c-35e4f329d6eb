<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    protected $fillable = [
        'order_id',
        'course_id',
        'course_title',
        'course_price',
        'note',
    ];

    protected $casts = [
        'course_price' => 'decimal:2',
    ];

    /**
     * Quan Hệ Với Order
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Quan Hệ Với Course
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }
}
