@extends('layouts.admin')

@section('title', 'Quản Lý Ng<PERSON>ời Dùng')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Quản Lý Người Dùng</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active">Người Dùng</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="GET" class="row g-3">
            <div class="col-md-4">
              <label class="form-label">Tìm <PERSON></label>
              <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                     placeholder="Tên hoặc Email...">
            </div>
            <div class="col-md-3">
              <label class="form-label">Role</label>
              <select class="form-select" name="role">
                <option value="">Tất Cả</option>
                <option value="user" {{ request('role') == 'user' ? 'selected' : '' }}>User</option>
                <option value="admin" {{ request('role') == 'admin' ? 'selected' : '' }}>Admin</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Trạng Thái</label>
              <select class="form-select" name="status">
                <option value="">Tất Cả</option>
                <option value="verified" {{ request('status') == 'verified' ? 'selected' : '' }}>Đã Xác Thực</option>
                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Chưa Xác Thực</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-search me-1"></i>Tìm
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Users List -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-users me-2"></i>Danh Sách Người Dùng ({{ $users->total() }})
          </h5>
        </div>
        <div class="card-body p-0">
          @if($users->count() > 0)
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Avatar</th>
                  <th>Thông Tin</th>
                  <th>Role</th>
                  <th>Khóa Học</th>
                  <th>Trạng Thái</th>
                  <th>Ngày Tạo</th>
                  <th>Thao Tác</th>
                </tr>
              </thead>
              <tbody>
                @foreach($users as $user)
                <tr>
                  <td>
                    @if($user->avatar)
                    <img src="{{ asset('storage/' . $user->avatar) }}" alt="{{ $user->name }}" 
                         class="rounded-circle" width="40" height="40">
                    @else
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                         style="width: 40px; height: 40px;">
                      <span class="text-white fw-bold">{{ strtoupper(substr($user->name, 0, 1)) }}</span>
                    </div>
                    @endif
                  </td>
                  
                  <td>
                    <div>
                      <h6 class="mb-1">
                        <a href="{{ route('admin.users.show', $user->id) }}" class="text-decoration-none">
                          {{ $user->name }}
                        </a>
                      </h6>
                      <small class="text-muted">{{ $user->email }}</small>
                    </div>
                  </td>
                  
                  <td>
                    @if($user->role == 'admin')
                    <span class="badge bg-danger">Admin</span>
                    @else
                    <span class="badge bg-primary">User</span>
                    @endif
                  </td>
                  
                  <td>
                    <span class="badge bg-info">{{ $user->userCourses->count() }} Khóa Học</span>
                  </td>
                  
                  <td>
                    @if($user->email_verified_at)
                    <span class="badge bg-success">Đã Xác Thực</span>
                    @else
                    <span class="badge bg-warning">Chưa Xác Thực</span>
                    @endif
                  </td>
                  
                  <td>{{ $user->created_at->format('d/m/Y') }}</td>
                  
                  <td>
                    @include('admin.partials.action-buttons', [
                      'showRoute' => route('admin.users.show', $user->id),
                      'editRoute' => route('admin.users.edit', $user->id),
                      'deleteRoute' => $user->role !== 'admin' ? route('admin.users.destroy', $user->id) : null,
                      'deleteMessage' => 'Bạn có chắc muốn xóa người dùng này?'
                    ])

                    @if($user->role !== 'admin')
                    <form action="{{ route('admin.users.toggle-status', $user->id) }}" method="POST" class="d-inline" style="margin-left: 2px;">
                      @csrf
                      @method('PATCH')
                      <button type="submit" class="btn btn-outline-{{ $user->email_verified_at ? 'secondary' : 'success' }} d-flex align-items-center justify-content-center"
                              data-bs-toggle="tooltip"
                              style="width: 32px; height: 32px;"
                              aria-label="{{ $user->email_verified_at ? 'Khóa' : 'Kích Hoạt' }}"
                              data-bs-original-title="{{ $user->email_verified_at ? 'Khóa' : 'Kích Hoạt' }}">
                        <i class="fa fa-{{ $user->email_verified_at ? 'lock' : 'unlock' }}" style="font-size: 14px;"></i>
                      </button>
                    </form>
                    @endif
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          <div class="card-footer">
            {{ $users->links() }}
          </div>
          @else
          <div class="text-center py-5">
            <i class="fa fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Không Tìm Thấy Người Dùng</h5>
            <p class="text-muted">Thử thay đổi bộ lọc để tìm kiếm</p>
          </div>
          @endif
        </div>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="row mt-4">
    <div class="col-md-3">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1">{{ \App\Models\User::count() }}</h4>
              <p class="mb-0">Tổng Người Dùng</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-users fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1">{{ \App\Models\User::whereNotNull('email_verified_at')->count() }}</h4>
              <p class="mb-0">Đã Xác Thực</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-check-circle fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-warning text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1">{{ \App\Models\User::where('role', 'admin')->count() }}</h4>
              <p class="mb-0">Admin</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-crown fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-info text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1">{{ \App\Models\User::whereDate('created_at', today())->count() }}</h4>
              <p class="mb-0">Đăng Ký Hôm Nay</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-calendar fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
