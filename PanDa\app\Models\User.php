<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'avatar',
        'role',
        'google_id',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * <PERSON>uan <PERSON>ệ <PERSON>ới <PERSON>t Items
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Quan Hệ Với Orders
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Quan Hệ Với Notifications
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Quan Hệ Với User Courses
     */
    public function userCourses()
    {
        return $this->hasMany(UserCourse::class);
    }

    /**
     * Quan Hệ Với Enrolled Courses
     */
    public function enrolledCourses()
    {
        return $this->belongsToMany(Course::class, 'user_courses')
            ->withPivot(['enrolled_at', 'started_at', 'completed_at', 'progress_percentage', 'last_accessed_at'])
            ->withTimestamps();
    }

    /**
     * Accessor Cho Số Thông Báo Chưa Đọc
     */
    public function getUnreadNotificationsCountAttribute()
    {
        return $this->notifications()->unread()->count();
    }

    /**
     * Kiểm Tra User Đã Mua Khóa Học
     */
    public function hasPurchasedCourse($courseId): bool
    {
        return $this->userCourses()->where('course_id', $courseId)->exists();
    }

    /**
     * Quan Hệ Với Certificates
     */
    public function certificates()
    {
        return $this->hasMany(\App\Models\Certificate::class);
    }

    /**
     * Quan Hệ Với Wishlist
     */
    public function wishlists()
    {
        return $this->hasMany(\App\Models\Wishlist::class);
    }

    /**
     * Quan Hệ Với Wishlist Courses
     */
    public function wishlistCourses()
    {
        return $this->belongsToMany(Course::class, 'wishlists')->withTimestamps();
    }

    /**
     * Lấy Tiến Độ Khóa Học
     */
    public function getCourseProgress($courseId)
    {
        return $this->userCourses()->where('course_id', $courseId)->first();
    }

    /**
     * Quan Hệ Với Comments
     */
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Kiểm Tra Course Có Trong Wishlist
     */
    public function hasInWishlist($courseId): bool
    {
        return $this->wishlists()->where('course_id', $courseId)->exists();
    }

    /**
     * Accessor Cho Wishlist Count
     */
    public function getWishlistCountAttribute(): int
    {
        return $this->wishlists()->count();
    }

    /**
     * Lấy Tổng Số Item Trong Giỏ Hàng
     */
    public function getCartCountAttribute()
    {
        return $this->cartItems()->count();
    }

    /**
     * Lấy Tổng Giá Trị Giỏ Hàng
     */
    public function getCartTotalAttribute()
    {
        return $this->cartItems()->with('course')->get()->sum(function ($item) {
            return $item->course->price;
        });
    }
}
