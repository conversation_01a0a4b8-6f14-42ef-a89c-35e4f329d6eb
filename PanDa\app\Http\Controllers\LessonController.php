<?php

namespace App\Http\Controllers;

use App\Models\Lesson;
use Illuminate\Http\Request;

class LessonController extends Controller
{
    /**
     * Hiển Thị Chi Tiết Bài Học
     */
    public function show($id)
    {
        $lesson = Lesson::with(['chapter.course'])
            ->where('id', $id)
            ->active()
            ->firstOrFail();

        // Kiểm Tra Quyền Truy Cập
        // TODO: Kiểm tra user đã mua khóa học hoặc bài học là preview
        if (!$lesson->is_preview) {
            // Tạm thời cho phép xem tất cả để test
            // return redirect()->route('courses.show', $lesson->chapter->course->slug)
            //     ->with('error', 'Bạn Cần <PERSON>a Kh<PERSON>a Học Để <PERSON>em Bà<PERSON> H<PERSON> Nà<PERSON>');
        }

        // Lấy danh sách bài học trong chapter để navigation
        $chapterLessons = $lesson->chapter->lessons()->active()->get();

        // Tìm bài học trước và sau
        $currentIndex = $chapterLessons->search(function ($item) use ($lesson) {
            return $item->id === $lesson->id;
        });

        $previousLesson = $currentIndex > 0 ? $chapterLessons[$currentIndex - 1] : null;
        $nextLesson = $currentIndex < $chapterLessons->count() - 1 ? $chapterLessons[$currentIndex + 1] : null;

        $chapter = $lesson->chapter;
        $course = $lesson->chapter->course;

        return view('lessons.show', compact('lesson', 'chapter', 'course', 'chapterLessons', 'previousLesson', 'nextLesson'));
    }

    /**
     * API: Lấy Nội Dung Bài Học
     */
    public function apiShow($id)
    {
        $lesson = Lesson::with(['chapter.course'])
            ->where('id', $id)
            ->active()
            ->first();

        if (!$lesson) {
            return response()->json([
                'success' => false,
                'message' => 'Bài Học Không Tồn Tại'
            ], 404);
        }

        // Kiểm Tra Quyền Truy Cập
        if (!$lesson->is_preview) {
            // TODO: Kiểm tra user đã mua khóa học
            // return response()->json([
            //     'success' => false,
            //     'message' => 'Bạn Cần Mua Khóa Học Để Xem Bài Học Này'
            // ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $lesson
        ]);
    }
}
