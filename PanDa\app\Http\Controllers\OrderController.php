<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\CartItem;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class OrderController extends Controller
{
    /**
     * Hiển <PERSON> Sách Đơn Hàng
     */
    public function index()
    {
        $orders = auth()->user()->orders()
            ->with('items.course')
            ->latest()
            ->paginate(10);

        return view('orders.index', compact('orders'));
    }

    /**
     * Hiển Thị Chi Tiết Đơn Hàng
     */
    public function show($id)
    {
        $order = auth()->user()->orders()
            ->with(['items.course', 'approvedBy'])
            ->findOrFail($id);

        return view('orders.show', compact('order'));
    }

    /**
     * Tạo Đơn Hàng Từ Giỏ Hàng
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:bank_transfer,cash,other',
            'note' => 'nullable|string|max:1000',
        ], [
            'payment_method.required' => 'Vui Lòng Chọn Phương Thức Thanh Toán',
            'note.max' => 'Ghi Chú Không Được Quá 1000 Ký Tự',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Lấy giỏ hàng
        $cartItems = auth()->user()->cartItems()->with('course')->get();

        if ($cartItems->isEmpty()) {
            return back()->with('error', 'Giỏ Hàng Trống!');
        }

        DB::beginTransaction();
        try {
            // Tính tổng tiền
            $totalAmount = $cartItems->sum(function ($item) {
                return $item->course->price;
            });

            // Tạo đơn hàng
            $order = Order::create([
                'user_id' => auth()->id(),
                'total_amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'note' => $request->note,
                'status' => 'pending',
            ]);

            // Tạo order items
            foreach ($cartItems as $cartItem) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'course_id' => $cartItem->course_id,
                    'course_title' => $cartItem->course->title,
                    'course_price' => $cartItem->course->price,
                    'note' => $cartItem->note,
                ]);
            }

            // Xóa giỏ hàng
            auth()->user()->cartItems()->delete();

            // Gửi thông báo cho user
            Notification::createForUser(
                auth()->id(),
                'Đơn Hàng Được Tạo',
                "Đơn Hàng {$order->order_number} Đã Được Tạo Thành Công. Chúng Tôi Sẽ Xem Xét Và Phản Hồi Trong Vòng 24 Giờ.",
                'info',
                ['order_id' => $order->id]
            );

            DB::commit();

            return redirect()->route('user.orders.show', $order->id)
                ->with('success', 'Gửi Yêu Cầu Mua Hàng Thành Công! Mã Đơn: ' . $order->order_number);

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Có Lỗi Xảy Ra: ' . $e->getMessage());
        }
    }
}