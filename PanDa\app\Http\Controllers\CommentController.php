<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Lesson;
use Illuminate\Http\Request;

class CommentController extends Controller
{
    /**
     * Lưu Comment Mới
     */
    public function store(Request $request, $lessonId)
    {
        $request->validate([
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:comments,id'
        ]);

        // Kiểm tra lesson tồn tại
        $lesson = Lesson::findOrFail($lessonId);

        // Tạo comment
        $comment = Comment::create([
            'user_id' => auth()->id(),
            'lesson_id' => $lessonId,
            'parent_id' => $request->parent_id,
            'content' => $request->content,
        ]);

        // Load relationships cho response
        $comment->load('user', 'replies.user');

        if ($request->ajax() || $request->expectsJson()) {
            try {
                $html = view('components.comment-item', compact('comment'))->render();
                return response()->json([
                    'success' => true,
                    'comment' => $comment,
                    'html' => $html
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lỗi khi render view: ' . $e->getMessage()
                ], 500);
            }
        }

        return back()->with('success', 'Bình Luận Đã Được Thêm!');
    }

    /**
     * Cập Nhật Comment
     */
    public function update(Request $request, Comment $comment)
    {
        // Kiểm tra quyền sở hữu
        if ($comment->user_id !== auth()->id()) {
            abort(403, 'Bạn Không Có Quyền Chỉnh Sửa Bình Luận Này');
        }

        $request->validate([
            'content' => 'required|string|max:1000'
        ]);

        try {
            $comment->update([
                'content' => $request->content
            ]);

            if ($request->ajax() || $request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Bình Luận Đã Được Cập Nhật!'
                ]);
            }
        } catch (\Exception $e) {
            if ($request->ajax() || $request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lỗi khi cập nhật: ' . $e->getMessage()
                ], 500);
            }
        }

        return back()->with('success', 'Bình Luận Đã Được Cập Nhật!');
    }

    /**
     * Xóa Comment
     */
    public function destroy(Comment $comment)
    {
        // Kiểm tra quyền sở hữu
        if ($comment->user_id !== auth()->id()) {
            abort(403, 'Bạn Không Có Quyền Xóa Bình Luận Này');
        }

        $comment->delete();

        if (request()->ajax() || request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Bình Luận Đã Được Xóa!'
            ]);
        }

        return back()->with('success', 'Bình Luận Đã Được Xóa!');
    }
}
