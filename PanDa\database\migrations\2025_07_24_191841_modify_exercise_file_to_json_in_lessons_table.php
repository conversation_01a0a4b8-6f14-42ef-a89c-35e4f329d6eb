<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            // Đổi exercise_file từ string thành json để lưu nhiều file
            $table->json('exercise_files')->nullable()->after('file_path');
            $table->dropColumn('exercise_file');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            $table->string('exercise_file')->nullable()->after('file_path');
            $table->dropColumn('exercise_files');
        });
    }
};
