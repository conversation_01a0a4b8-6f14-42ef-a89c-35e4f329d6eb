@extends('layouts.admin')

@section('title', 'Cài Đặt Bảo Mật')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Cài Đặt Bảo Mật</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Cài Đặt</a></li>
            <li class="breadcrumb-item active">Bảo Mật</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <form action="{{ route('admin.settings.security.update') }}" method="POST">
    @csrf
    <div class="row">
      <!-- Login Security -->
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-sign-in-alt me-2"></i>Bảo Mật Đăng Nhập
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">Số Lần Đăng Nhập Sai Tối Đa</label>
                <input type="number" class="form-control @error('login_attempts') is-invalid @enderror" 
                       name="login_attempts" value="{{ old('login_attempts', $settings['login_attempts']) }}" 
                       min="3" max="10" required>
                <small class="text-muted">Khóa tài khoản sau số lần đăng nhập sai</small>
                @error('login_attempts')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Thời Gian Session (phút)</label>
                <input type="number" class="form-control @error('session_timeout') is-invalid @enderror" 
                       name="session_timeout" value="{{ old('session_timeout', $settings['session_timeout']) }}" 
                       min="15" max="1440" required>
                <small class="text-muted">Tự động đăng xuất sau thời gian không hoạt động</small>
                @error('session_timeout')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Độ Dài Mật Khẩu Tối Thiểu</label>
                <input type="number" class="form-control @error('password_min_length') is-invalid @enderror" 
                       name="password_min_length" value="{{ old('password_min_length', $settings['password_min_length']) }}" 
                       min="6" max="20" required>
                @error('password_min_length')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <div class="form-check mt-4">
                  <input class="form-check-input" type="checkbox" name="require_email_verification" value="1" 
                         {{ old('require_email_verification', $settings['require_email_verification']) ? 'checked' : '' }}>
                  <label class="form-check-label">
                    Yêu Cầu Xác Thực Email
                  </label>
                  <small class="d-block text-muted">Người dùng phải xác thực email trước khi sử dụng</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Two-Factor Authentication -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-mobile-alt me-2"></i>Xác Thực Hai Yếu Tố (2FA)
            </h5>
          </div>
          <div class="card-body">
            <div class="form-check mb-3">
              <input class="form-check-input" type="checkbox" name="enable_2fa" value="1" 
                     {{ old('enable_2fa', $settings['enable_2fa']) ? 'checked' : '' }}>
              <label class="form-check-label">
                Bật Xác Thực Hai Yếu Tố
              </label>
              <small class="d-block text-muted">Yêu cầu mã OTP từ ứng dụng authenticator</small>
            </div>
            
            <div class="alert alert-info">
              <h6><i class="fa fa-info-circle me-2"></i>Hướng Dẫn Cài Đặt 2FA</h6>
              <ol class="mb-0">
                <li>Tải ứng dụng Google Authenticator hoặc Authy</li>
                <li>Quét mã QR hoặc nhập secret key</li>
                <li>Nhập mã 6 số để xác thực</li>
                <li>Lưu backup codes an toàn</li>
              </ol>
            </div>
          </div>
        </div>

        <!-- CAPTCHA -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-shield-alt me-2"></i>CAPTCHA Protection
            </h5>
          </div>
          <div class="card-body">
            <div class="form-check mb-3">
              <input class="form-check-input" type="checkbox" name="enable_captcha" value="1" 
                     {{ old('enable_captcha', $settings['enable_captcha']) ? 'checked' : '' }}>
              <label class="form-check-label">
                Bật Google reCAPTCHA
              </label>
              <small class="d-block text-muted">Bảo vệ khỏi bot và spam</small>
            </div>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">Site Key</label>
                <input type="text" class="form-control @error('captcha_site_key') is-invalid @enderror" 
                       name="captcha_site_key" value="{{ old('captcha_site_key', $settings['captcha_site_key']) }}" 
                       placeholder="6Lc...">
                @error('captcha_site_key')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Secret Key</label>
                <input type="text" class="form-control @error('captcha_secret_key') is-invalid @enderror" 
                       name="captcha_secret_key" value="{{ old('captcha_secret_key', $settings['captcha_secret_key']) }}" 
                       placeholder="6Lc...">
                @error('captcha_secret_key')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
            
            <div class="alert alert-warning">
              <h6><i class="fa fa-exclamation-triangle me-2"></i>Lấy reCAPTCHA Keys</h6>
              <p class="mb-2">Truy cập <a href="https://www.google.com/recaptcha/admin" target="_blank">Google reCAPTCHA Admin</a> để:</p>
              <ol class="mb-0">
                <li>Đăng ký domain của bạn</li>
                <li>Chọn reCAPTCHA v2 "I'm not a robot"</li>
                <li>Copy Site Key và Secret Key</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      <!-- Security Status -->
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-shield-check me-2"></i>Trạng Thái Bảo Mật
            </h5>
          </div>
          <div class="card-body">
            <div class="security-score text-center mb-4">
              <div class="score-circle mx-auto mb-3" style="width: 100px; height: 100px; position: relative;">
                <svg width="100" height="100" style="transform: rotate(-90deg);">
                  <circle cx="50" cy="50" r="40" stroke="#e9ecef" stroke-width="8" fill="none"></circle>
                  <circle cx="50" cy="50" r="40" stroke="#28a745" stroke-width="8" fill="none" 
                          stroke-dasharray="251.2" stroke-dashoffset="75.36" stroke-linecap="round"></circle>
                </svg>
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                  <h3 class="mb-0 text-success">70%</h3>
                  <small class="text-muted">Điểm Bảo Mật</small>
                </div>
              </div>
            </div>
            
            <div class="list-group list-group-flush">
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">Mật Khẩu Mạnh</span>
                <i class="fa fa-check text-success"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">Xác Thực Email</span>
                <i class="fa fa-{{ $settings['require_email_verification'] ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">2FA Enabled</span>
                <i class="fa fa-{{ $settings['enable_2fa'] ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">CAPTCHA Protection</span>
                <i class="fa fa-{{ $settings['enable_captcha'] ? 'check text-success' : 'times text-danger' }}"></i>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span class="small">Session Timeout</span>
                <i class="fa fa-check text-success"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Security Recommendations -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-lightbulb me-2"></i>Khuyến Nghị Bảo Mật
            </h5>
          </div>
          <div class="card-body">
            <div class="alert alert-warning small">
              <h6><i class="fa fa-exclamation-triangle me-2"></i>Cần Cải Thiện</h6>
              <ul class="mb-0">
                @if(!$settings['enable_2fa'])
                <li>Bật xác thực hai yếu tố (2FA)</li>
                @endif
                @if(!$settings['enable_captcha'])
                <li>Bật CAPTCHA protection</li>
                @endif
                @if($settings['password_min_length'] < 8)
                <li>Tăng độ dài mật khẩu tối thiểu</li>
                @endif
              </ul>
            </div>
            
            <div class="alert alert-success small">
              <h6><i class="fa fa-check-circle me-2"></i>Đã Tốt</h6>
              <ul class="mb-0">
                <li>Giới hạn số lần đăng nhập sai</li>
                <li>Session timeout được cấu hình</li>
                <li>Yêu cầu mật khẩu mạnh</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Security Logs -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-history me-2"></i>Nhật Ký Bảo Mật
            </h5>
          </div>
          <div class="card-body">
            <div class="timeline-sm">
              <div class="timeline-item">
                <div class="timeline-marker bg-success"></div>
                <div class="timeline-content">
                  <h6 class="timeline-title small">Đăng Nhập Thành Công</h6>
                  <p class="timeline-text small text-muted">Admin từ IP ***********</p>
                  <small class="text-muted">2 phút trước</small>
                </div>
              </div>
              <div class="timeline-item">
                <div class="timeline-marker bg-warning"></div>
                <div class="timeline-content">
                  <h6 class="timeline-title small">Đăng Nhập Sai</h6>
                  <p class="timeline-text small text-muted">IP ************* (3/5 lần)</p>
                  <small class="text-muted">1 giờ trước</small>
                </div>
              </div>
              <div class="timeline-item">
                <div class="timeline-marker bg-info"></div>
                <div class="timeline-content">
                  <h6 class="timeline-title small">Cập Nhật Cài Đặt</h6>
                  <p class="timeline-text small text-muted">Thay đổi cài đặt bảo mật</p>
                  <small class="text-muted">1 ngày trước</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Submit Buttons -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left me-2"></i>Quay Lại
              </a>
              <div>
                <button type="reset" class="btn btn-outline-secondary me-2">
                  <i class="fa fa-undo me-2"></i>Đặt Lại
                </button>
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-save me-2"></i>Lưu Cài Đặt
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<style>
.timeline-sm {
  position: relative;
  padding-left: 20px;
}

.timeline-sm::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 15px;
}

.timeline-marker {
  position: absolute;
  left: -16px;
  top: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid #fff;
}

.timeline-content {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 5px;
  border-left: 3px solid #007bff;
}
</style>
@endsection
