<?php

namespace App\Http\Controllers;

use App\Models\CartItem;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CartController extends Controller
{
    /**
     * Hiển Thị Giỏ Hàng
     */
    public function index()
    {
        $cartItems = Auth::user()->cartItems()->with('course.category')->get();
        $total = $cartItems->sum(function ($item) {
            return $item->course->price;
        });

        return view('cart.index', compact('cartItems', 'total'));
    }

    /**
     * Thêm Khóa Học Vào Giỏ Hàng
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'note' => 'nullable|string|max:500',
        ], [
            'course_id.required' => 'Vui <PERSON>òng <PERSON>ọ<PERSON>hóa H<PERSON>',
            'course_id.exists' => '<PERSON>h<PERSON><PERSON>c <PERSON>ông Tồn Tại',
            'note.max' => 'G<PERSON>ú <PERSON>ông Được <PERSON>ợt Quá 500 Ký Tự',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $course = Course::findOrFail($request->course_id);
        $user = Auth::user();

        // Kiểm tra khóa học đã có trong giỏ hàng chưa
        $existingItem = CartItem::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if ($existingItem) {
            return back()->with('error', 'Khóa Học Đã Có Trong Giỏ Hàng');
        }

        // Thêm vào giỏ hàng
        CartItem::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'note' => $request->note,
        ]);

        return back()->with('success', 'Đã Thêm Khóa Học Vào Giỏ Hàng');
    }

    /**
     * Cập Nhật Ghi Chú
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'note' => 'nullable|string|max:500',
        ], [
            'note.max' => 'Ghi Chú Không Được Vượt Quá 500 Ký Tự',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $cartItem = CartItem::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        $cartItem->update([
            'note' => $request->note,
        ]);

        return back()->with('success', 'Đã Cập Nhật Ghi Chú');
    }

    /**
     * Xóa Khóa Học Khỏi Giỏ Hàng
     */
    public function destroy($id)
    {
        $cartItem = CartItem::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        $cartItem->delete();

        return back()->with('success', 'Đã Xóa Khóa Học Khỏi Giỏ Hàng');
    }

    /**
     * Gửi Yêu Cầu Mua Hàng
     */
    public function submitRequest(Request $request)
    {
        $user = Auth::user();
        $cartItems = $user->cartItems()->with('course')->get();

        if ($cartItems->isEmpty()) {
            return back()->with('error', 'Giỏ Hàng Trống');
        }

        // TODO: Gửi email hoặc lưu vào bảng purchase_requests
        // Tạm thời chỉ hiển thị thông báo thành công

        return back()->with('success', 'Đã Gửi Yêu Cầu Mua Hàng. Chúng Tôi Sẽ Liên Hệ Với Bạn Sớm Nhất!');
    }

    /**
     * API: Lấy Giỏ Hàng
     */
    public function apiIndex()
    {
        $cartItems = Auth::user()->cartItems()->with('course.category')->get();
        $total = $cartItems->sum(function ($item) {
            return $item->course->price;
        });

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $cartItems,
                'total' => $total,
                'count' => $cartItems->count(),
            ]
        ]);
    }

    /**
     * API: Thêm Vào Giỏ Hàng
     */
    public function apiStore(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'note' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ Liệu Không Hợp Lệ',
                'errors' => $validator->errors()
            ], 422);
        }

        $course = Course::findOrFail($request->course_id);
        $user = Auth::user();

        // Kiểm tra khóa học đã có trong giỏ hàng chưa
        $existingItem = CartItem::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if ($existingItem) {
            return response()->json([
                'success' => false,
                'message' => 'Khóa Học Đã Có Trong Giỏ Hàng'
            ], 409);
        }

        // Thêm vào giỏ hàng
        $cartItem = CartItem::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'note' => $request->note,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Đã Thêm Khóa Học Vào Giỏ Hàng',
            'data' => $cartItem->load('course')
        ], 201);
    }

    /**
     * API: Xóa Khỏi Giỏ Hàng
     */
    public function apiDestroy($id)
    {
        $cartItem = CartItem::where('id', $id)
            ->where('user_id', Auth::id())
            ->first();

        if (!$cartItem) {
            return response()->json([
                'success' => false,
                'message' => 'Item Không Tồn Tại'
            ], 404);
        }

        $cartItem->delete();

        return response()->json([
            'success' => true,
            'message' => 'Đã Xóa Khóa Học Khỏi Giỏ Hàng'
        ]);
    }
}
