<?php

namespace App\Http\Controllers;

use App\Models\Review;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReviewController extends Controller
{
    /**
     * <PERSON><PERSON><PERSON>i<PERSON>i
     */
    public function store(Request $request, $courseId)
    {
        $course = Course::findOrFail($courseId);

        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ], [
            'rating.required' => 'Vui Lòng Chọn Số Sao',
            'rating.min' => 'Đánh Giá Tối Thiểu 1 Sao',
            'rating.max' => 'Đánh Giá Tối Đa 5 Sao',
            'comment.max' => 'Bình Luận Không Được Quá 1000 Ký Tự',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Kiểm tra user đã review chưa
        $existingReview = Review::where('user_id', auth()->id())
            ->where('course_id', $courseId)
            ->first();

        if ($existingReview) {
            return back()->with('error', 'Bạn Đã Đánh Giá Khóa Học Này Rồi!');
        }

        Review::create([
            'user_id' => auth()->id(),
            'course_id' => $courseId,
            'rating' => $request->rating,
            'comment' => $request->comment,
            'is_approved' => false, // Cần admin duyệt
        ]);

        return back()->with('success', 'Cảm Ơn Bạn Đã Đánh Giá! Đánh Giá Sẽ Được Hiển Thị Sau Khi Admin Duyệt.');
    }

    /**
     * Cập Nhật Đánh Giá
     */
    public function update(Request $request, $id)
    {
        $review = Review::where('id', $id)
            ->where('user_id', auth()->id())
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ], [
            'rating.required' => 'Vui Lòng Chọn Số Sao',
            'rating.min' => 'Đánh Giá Tối Thiểu 1 Sao',
            'rating.max' => 'Đánh Giá Tối Đa 5 Sao',
            'comment.max' => 'Bình Luận Không Được Quá 1000 Ký Tự',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $review->update([
            'rating' => $request->rating,
            'comment' => $request->comment,
            'is_approved' => false, // Cần duyệt lại
        ]);

        return back()->with('success', 'Cập Nhật Đánh Giá Thành Công!');
    }

    /**
     * Xóa Đánh Giá
     */
    public function destroy($id)
    {
        $review = Review::where('id', $id)
            ->where('user_id', auth()->id())
            ->firstOrFail();

        $review->delete();

        return back()->with('success', 'Xóa Đánh Giá Thành Công!');
    }
}
