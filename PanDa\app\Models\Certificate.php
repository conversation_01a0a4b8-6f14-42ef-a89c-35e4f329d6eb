<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Certificate extends Model
{
    protected $fillable = [
        'user_id',
        'course_id',
        'user_course_id',
        'certificate_number',
        'certificate_code',
        'issued_at',
        'file_path',
        'certificate_data',
        'is_verified',
        'verified_at',
    ];

    protected $casts = [
        'issued_at' => 'datetime',
        'verified_at' => 'datetime',
        'certificate_data' => 'array',
        'is_verified' => 'boolean',
    ];

    /**
     * Quan Hệ Với User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Quan Hệ Với Course
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Quan Hệ Với UserCourse
     */
    public function userCourse(): BelongsTo
    {
        return $this->belongsTo(UserCourse::class);
    }

    /**
     * Boot Method - Tự Động Tạo Certificate Number
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($certificate) {
            if (!$certificate->certificate_number) {
                $certificate->certificate_number = static::generateCertificateNumber();
            }

            if (!$certificate->certificate_code) {
                $certificate->certificate_code = static::generateCertificateCode();
            }

            if (!$certificate->issued_at) {
                $certificate->issued_at = now();
            }
        });
    }

    /**
     * Tạo Certificate Number Duy Nhất
     */
    public static function generateCertificateNumber(): string
    {
        do {
            $number = 'CERT-' . date('Y') . '-' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        } while (static::where('certificate_number', $number)->exists());

        return $number;
    }

    /**
     * Tạo Certificate Code Ngắn
     */
    public static function generateCertificateCode(): string
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (static::where('certificate_code', $code)->exists());

        return $code;
    }

    /**
     * Tạo Chứng Chỉ Cho User Course
     */
    public static function createForUserCourse(UserCourse $userCourse): self
    {
        // Kiểm tra đã có chứng chỉ chưa
        $existingCertificate = static::where('user_id', $userCourse->user_id)
            ->where('course_id', $userCourse->course_id)
            ->first();

        if ($existingCertificate) {
            return $existingCertificate;
        }

        // Tạo dữ liệu chứng chỉ
        $certificateData = [
            'user_name' => $userCourse->user->name,
            'course_title' => $userCourse->course->title,
            'course_duration' => $userCourse->course->duration_hours,
            'completion_date' => $userCourse->completed_at->format('d/m/Y'),
            'instructor' => 'PanDa Learning Team',
            'grade' => 'Hoàn Thành', // Có thể mở rộng với điểm số
        ];

        return static::create([
            'user_id' => $userCourse->user_id,
            'course_id' => $userCourse->course_id,
            'user_course_id' => $userCourse->id,
            'certificate_data' => $certificateData,
            'is_verified' => true,
            'verified_at' => now(),
        ]);
    }

    /**
     * Verify Certificate Bằng Code
     */
    public static function verifyByCode(string $code): ?self
    {
        return static::where('certificate_code', $code)
            ->where('is_verified', true)
            ->first();
    }

    /**
     * Accessor Cho Download URL
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('user.certificates.download', $this->id);
    }

    /**
     * Accessor Cho Verify URL
     */
    public function getVerifyUrlAttribute(): string
    {
        return route('certificates.verify', $this->certificate_code);
    }

    /**
     * Accessor Cho Formatted Issue Date
     */
    public function getFormattedIssueDateAttribute(): string
    {
        return $this->issued_at->format('d/m/Y');
    }

    /**
     * Scope Verified
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope By Year (SQLite compatible)
     */
    public function scopeByYear($query, $year)
    {
        return $query->whereRaw("strftime('%Y', issued_at) = ?", [$year]);
    }
}
