@extends('layouts.user')

@section('title', $lesson->title . ' - ' . $lesson->chapter->course->title)

@section('content')
<div class="row">
  <div class="col-lg-8">
    <!-- Nội Dung Bài Học -->
    <div class="card mb-4">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h5 class="mb-0">{{ $lesson->title }}</h5>
            <small class="text-muted">{{ $lesson->chapter->course->title }} - {{ $lesson->chapter->title }}</small>
          </div>
          <div>
            @if($lesson->type == 'video')
            <span class="badge bg-primary">Video</span>
            @elseif($lesson->type == 'text')
            <span class="badge bg-success">Văn Bản</span>
            @elseif($lesson->type == 'pdf')
            <span class="badge bg-danger">PDF</span>
            @else
            <span class="badge bg-warning">Khác</span>
            @endif

            @if($lesson->is_preview)
            <span class="badge bg-info ms-1">Preview</span>
            @endif
          </div>
        </div>
      </div>
      
      <div class="card-body">
        @if($lesson->type == 'video' && $lesson->video_url)
        <!-- Video Bài Học -->
        <div class="card mb-4">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="fa fa-code me-2"></i>Video Bài Học
            </h6>
          </div>
          <div class="card-body text-center">
            @if(strpos($lesson->video_url, 'youtube.com') !== false || strpos($lesson->video_url, 'youtu.be') !== false)
            <div class="mb-3">
              <i class="fab fa-youtube fa-3x text-danger"></i>
            </div>
            <p class="text-muted mb-3">Xem Video Trên YouTube Để Học Bài Này</p>
            <a href="{{ $lesson->video_url }}" target="_blank" class="btn btn-danger">
              <i class="fab fa-youtube me-2"></i>Học Bài
            </a>
            @else
            <div class="mb-3">
              <i class="fa fa-play-circle fa-3x text-primary"></i>
            </div>
            <p class="text-muted mb-3">Xem Video Để Học Bài Này</p>
            <a href="{{ $lesson->video_url }}" target="_blank" class="btn btn-primary">
              <i class="fa fa-play me-2"></i>Học Bài
            </a>
            @endif
          </div>
        </div>
        @endif

        @if($lesson->type == 'text' && $lesson->content)
        <!-- Text Content -->
        <div class="lesson-content">
          {!! nl2br(e($lesson->content)) !!}
        </div>
        @endif

        @if($lesson->type == 'pdf' && $lesson->file_path)
        <!-- PDF Download -->
        <div class="text-center py-4">
          <i class="fa fa-file-pdf fa-4x text-danger mb-3"></i>
          <h5>Tài Liệu PDF</h5>
          <p class="text-muted">Nhấn Vào Nút Bên Dưới Để Tải Xuống Tài Liệu</p>
          <a href="{{ route('user.courses.lessons.download.pdf', ['course' => $course->id, 'chapter' => $chapter->id, 'lesson' => $lesson->id]) }}" class="btn btn-danger">
            <i class="fa fa-download me-2"></i>Tải Xuống PDF
          </a>
        </div>
        @endif

        @if($lesson->exercise_files && count($lesson->exercise_files) > 0)
        <!-- Exercise Files Download -->
        <div class="mt-4">
          <div class="card border-success">
            <div class="card-header bg-success text-white">
              <h6 class="mb-0">
                <i class="fa fa-tasks me-2"></i>Bài Tập Thực Hành ({{ count($lesson->exercise_files) }} File)
              </h6>
            </div>
            <div class="card-body">
              <div class="text-center mb-4">
                <i class="fa fa-file-archive fa-3x text-success mb-3"></i>
                <h6>File Bài Tập Đính Kèm</h6>
                <p class="text-muted">Tải Xuống Các File Bài Tập Để Thực Hành Những Kiến Thức Đã Học Trong Bài Này</p>
              </div>

              <div class="row">
                @foreach($lesson->exercise_files as $index => $file)
                <div class="col-md-6 mb-3">
                  <div class="card border-light">
                    <div class="card-body text-center">
                      @if($file['type'] == 'pdf')
                      <i class="fa fa-file-pdf fa-2x text-danger mb-2"></i>
                      @elseif(in_array($file['type'], ['zip', 'rar']))
                      <i class="fa fa-file-archive fa-2x text-warning mb-2"></i>
                      @else
                      <i class="fa fa-file-alt fa-2x text-primary mb-2"></i>
                      @endif

                      <h6 class="card-title">Tài Liệu {{ $index + 1 }}</h6>
                      <p class="text-muted small">{{ number_format($file['size'] / 1024, 1) }} KB</p>

                      <a href="{{ route('user.courses.lessons.download.file', ['course' => $course->id, 'chapter' => $chapter->id, 'lesson' => $lesson->id, 'fileIndex' => $index]) }}" class="btn btn-success btn-sm">
                        <i class="fa fa-download me-1"></i>Tải Xuống
                      </a>
                    </div>
                  </div>
                </div>
                @endforeach
              </div>

              @if(count($lesson->exercise_files) > 1)
              <div class="text-center mt-3">
                <button class="btn btn-outline-success" onclick="downloadAllFiles()">
                  <i class="fa fa-download me-2"></i>Tải Tất Cả File
                </button>
              </div>
              @endif
            </div>
          </div>
        </div>
        @endif

        <!-- Navigation -->
        <div class="d-flex justify-content-between align-items-center mt-4 pt-4 border-top">
          <div>
            @if($previousLesson)
            <a href="{{ route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $previousLesson->chapter_id, 'lesson' => $previousLesson->id]) }}" class="btn btn-outline-primary">
              <i class="fa fa-arrow-left me-2"></i>Bài Trước
            </a>
            @endif
          </div>

          <div class="text-center">
            <small class="text-muted">Thời Lượng: {{ $lesson->duration_minutes }} Phút</small>
          </div>

          <div>
            @if($nextLesson)
            <a href="{{ route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $nextLesson->chapter_id, 'lesson' => $nextLesson->id]) }}" class="btn btn-primary">
              Bài Tiếp <i class="fa fa-arrow-right ms-2"></i>
            </a>
            @endif
          </div>
        </div>

        <!-- Hoàn Thành Bài Học -->
        <div class="text-center mt-4 pt-4 border-top">
          @if($userCourse->isLessonCompleted($lesson->id))
          <div class="alert alert-success">
            <i class="fa fa-check-circle me-2"></i>Bạn Đã Hoàn Thành Bài Học Này
          </div>
          @else
          <form action="{{ route('user.courses.lessons.complete', ['course' => $userCourse->course_id, 'chapter' => $lesson->chapter_id, 'lesson' => $lesson->id]) }}" method="POST" class="d-inline">
            @csrf
            <button type="submit" class="btn btn-success btn-lg">
              <i class="fa fa-check-circle me-2"></i>Báo Cáo Hoàn Thành Bài Học
            </button>
          </form>
          @endif
        </div>
      </div>
    </div>

    <!-- Ghi Chú -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Ghi Chú Của Tôi
        </h6>
      </div>
      <div class="card-body">
        <textarea class="form-control" rows="4" placeholder="Viết Ghi Chú Cho Bài Học Này..."></textarea>
        <div class="mt-3">
          <button class="btn btn-primary">
            <i class="fa fa-save me-2"></i>Lưu Ghi Chú
          </button>
        </div>
      </div>
    </div>

    <!-- Comments Section -->
    <div class="card mt-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-comments me-2"></i>Thảo Luận ({{ $lesson->comments->where('parent_id', null)->count() }})
        </h6>
      </div>
      <div class="card-body">
        <!-- Comment Form -->
        @auth
        <form id="comment-form" onsubmit="submitComment(event)">
          @csrf
          <div class="d-flex gap-3 mb-4">
            <div class="flex-shrink-0">
              @if(auth()->user()->avatar)
              <img src="{{ asset('storage/' . auth()->user()->avatar) }}" class="rounded-circle" width="40" height="40" alt="{{ auth()->user()->name }}">
              @else
              <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                <span class="text-white fw-bold">{{ strtoupper(substr(auth()->user()->name, 0, 1)) }}</span>
              </div>
              @endif
            </div>
            <div class="flex-grow-1">
              <textarea class="form-control mb-2" name="content" rows="3" placeholder="Chia Sẻ Suy Nghĩ Của Bạn Về Bài Học Này..." required></textarea>
              <button type="submit" class="btn btn-primary">
                <i class="fa fa-paper-plane me-2"></i>Gửi Bình Luận
              </button>
            </div>
          </div>
        </form>
        @else
        <div class="text-center py-4">
          <p class="text-muted mb-3">Đăng Nhập Để Tham Gia Thảo Luận</p>
          <a href="{{ route('login') }}" class="btn btn-primary">
            <i class="fa fa-sign-in-alt me-2"></i>Đăng Nhập
          </a>
        </div>
        @endauth

        <!-- Comments List -->
        <div id="comments-list">
          @forelse($lesson->comments->where('parent_id', null) as $comment)
          @include('components.comment-item', ['comment' => $comment])
          @empty
          <div class="text-center py-4 text-muted">
            <i class="fa fa-comments fa-3x mb-3"></i>
            <p>Chưa Có Bình Luận Nào. Hãy Là Người Đầu Tiên Chia Sẻ!</p>
          </div>
          @endforelse
        </div>
      </div>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="col-lg-4">
    <!-- Tiến Độ Khóa Học -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Tiến Độ Khóa Học
        </h6>
      </div>
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <small>Hoàn Thành</small>
          <small>0%</small>
        </div>
        <div class="progress mb-3" style="height: 8px;">
          <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
        </div>
        
        <div class="text-center">
          <small class="text-muted">0 / {{ $lesson->chapter->course->total_lessons }} Bài Học</small>
        </div>
      </div>
    </div>

    <!-- Danh Sách Bài Học -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>{{ $lesson->chapter->title }}
        </h6>
      </div>
      <div class="card-body p-0">
        <div class="list-group list-group-flush">
          @foreach($chapterLessons as $chapterLesson)
          <div class="list-group-item {{ $chapterLesson->id == $lesson->id ? 'active' : '' }}">
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex align-items-center">
                @if($chapterLesson->type == 'video')
                <i class="fa fa-play-circle me-2"></i>
                @elseif($chapterLesson->type == 'text')
                <i class="fa fa-file-text me-2"></i>
                @elseif($chapterLesson->type == 'pdf')
                <i class="fa fa-file-pdf me-2"></i>
                @else
                <i class="fa fa-question-circle me-2"></i>
                @endif
                
                <div>
                  @if($chapterLesson->is_preview || $chapterLesson->id == $lesson->id)
                  <a href="{{ route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $chapterLesson->chapter_id, 'lesson' => $chapterLesson->id]) }}" class="text-decoration-none {{ $chapterLesson->id == $lesson->id ? 'text-white' : '' }}">
                    {{ $chapterLesson->title }}
                  </a>
                  @else
                  <span class="text-muted">{{ $chapterLesson->title }}</span>
                  <i class="fa fa-lock ms-1"></i>
                  @endif
                  
                  @if($chapterLesson->is_preview)
                  <span class="badge bg-success ms-1">Preview</span>
                  @endif
                </div>
              </div>
              
              <small class="{{ $chapterLesson->id == $lesson->id ? 'text-white-50' : 'text-muted' }}">
                {{ $chapterLesson->duration_minutes }}p
              </small>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>

    <!-- Quay Lại Khóa Học -->
    <div class="mt-3">
      <a href="{{ route('courses.show', $lesson->chapter->course->slug) }}" class="btn btn-outline-primary w-100">
        <i class="fa fa-arrow-left me-2"></i>Quay Lại Khóa Học
      </a>
    </div>
  </div>
</div>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mt-4">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Trang Chủ</a></li>
    <li class="breadcrumb-item"><a href="{{ route('courses.index') }}">Khóa Học</a></li>
    <li class="breadcrumb-item"><a href="{{ route('courses.show', $lesson->chapter->course->slug) }}">{{ $lesson->chapter->course->title }}</a></li>
    <li class="breadcrumb-item active" aria-current="page">{{ $lesson->title }}</li>
  </ol>
</nav>
@endsection

@if($lesson->exercise_files && count($lesson->exercise_files) > 1)
<script>
function downloadAllFiles() {
  @foreach($lesson->exercise_files as $index => $file)
  setTimeout(() => {
    const link = document.createElement('a');
    link.href = '{{ asset("storage/" . $file["path"]) }}';
    link.download = '{{ $file["name"] }}';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, {{ $index * 500 }});
  @endforeach
}

// Comment Functions
function submitComment(event) {
  event.preventDefault();
  const form = event.target;
  const formData = new FormData(form);

  fetch('{{ route("user.comments.store", $lesson->id) }}', {
    method: 'POST',
    body: formData,
    headers: {
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
      'Accept': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Reset form
      form.reset();

      // Add new comment to list
      const commentsList = document.getElementById('comments-list');
      if (commentsList.querySelector('.text-muted')) {
        commentsList.innerHTML = data.html;
      } else {
        commentsList.insertAdjacentHTML('afterbegin', data.html);
      }

      // Update comment count
      updateCommentCount();
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Có Lỗi Xảy Ra Khi Gửi Bình Luận');
  });
}

function submitReply(event, parentId) {
  event.preventDefault();
  const form = event.target;
  const textarea = form.querySelector('textarea');

  if (!textarea.value.trim()) {
    alert('Vui Lòng Nhập Nội Dung Trả Lời');
    return;
  }

  const formData = new FormData();
  formData.append('content', textarea.value);
  formData.append('parent_id', parentId);
  formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

  fetch('{{ route("user.comments.store", $lesson->id) }}', {
    method: 'POST',
    body: formData,
    headers: {
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    return response.json();
  })
  .then(data => {
    if (data.success) {
      // Reset form and hide
      form.reset();
      toggleReplyForm(parentId);

      // Add reply to parent comment
      const parentComment = document.querySelector(`[data-comment-id="${parentId}"]`);
      let repliesContainer = parentComment.querySelector('.replies');
      if (!repliesContainer) {
        repliesContainer = document.createElement('div');
        repliesContainer.className = 'replies mt-3 ms-4';
        parentComment.appendChild(repliesContainer);
      }
      repliesContainer.insertAdjacentHTML('beforeend', data.html);
      alert('Trả Lời Đã Được Gửi!');
    } else {
      alert('Có Lỗi Xảy Ra: ' + (data.message || 'Unknown error'));
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Có Lỗi Xảy Ra Khi Gửi Trả Lời: ' + error.message);
  });
}

function toggleReplyForm(commentId) {
  const replyForm = document.getElementById(`reply-form-${commentId}`);
  replyForm.classList.toggle('d-none');

  if (!replyForm.classList.contains('d-none')) {
    replyForm.querySelector('textarea').focus();
  }
}

function editComment(commentId) {
  const commentItem = document.querySelector(`[data-comment-id="${commentId}"]`);
  const content = commentItem.querySelector('.comment-content');
  const editForm = commentItem.querySelector('.edit-form');

  content.classList.add('d-none');
  editForm.classList.remove('d-none');
  editForm.querySelector('textarea').focus();
}

function cancelEdit(commentId) {
  const commentItem = document.querySelector(`[data-comment-id="${commentId}"]`);
  const content = commentItem.querySelector('.comment-content');
  const editForm = commentItem.querySelector('.edit-form');

  content.classList.remove('d-none');
  editForm.classList.add('d-none');
}

function updateComment(event, commentId) {
  event.preventDefault();
  const form = event.target;
  const formData = new FormData(form);
  formData.append('_method', 'PUT');

  fetch(`{{ url('/user/comments') }}/${commentId}`, {
    method: 'POST',
    body: formData,
    headers: {
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
      'Accept': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      const commentItem = document.querySelector(`[data-comment-id="${commentId}"]`);
      const content = commentItem.querySelector('.comment-content p');
      const newContent = form.querySelector('textarea').value;

      content.textContent = newContent;
      cancelEdit(commentId);
      alert('Bình Luận Đã Được Cập Nhật!');
    } else {
      alert('Có Lỗi Xảy Ra: ' + (data.message || 'Unknown error'));
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Có Lỗi Xảy Ra Khi Cập Nhật Bình Luận');
  });
}

function deleteComment(commentId) {
  if (!confirm('Bạn Có Chắc Muốn Xóa Bình Luận Này?')) {
    return;
  }

  fetch(`{{ url('/user/comments') }}/${commentId}`, {
    method: 'DELETE',
    headers: {
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
      'Accept': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      document.querySelector(`[data-comment-id="${commentId}"]`).remove();
      updateCommentCount();
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Có Lỗi Xảy Ra Khi Xóa Bình Luận');
  });
}

function updateCommentCount() {
  const comments = document.querySelectorAll('[data-comment-id]').length;
  const countElement = document.querySelector('.card-header h6');
  countElement.innerHTML = '<i class="fa fa-comments me-2"></i>Thảo Luận (' + comments + ')';
}
</script>
@endif

@push('scripts')
<style>
.lesson-content {
  line-height: 1.8;
  font-size: 1.1rem;
}

.lesson-content h1, .lesson-content h2, .lesson-content h3 {
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.lesson-content p {
  margin-bottom: 1rem;
}

.lesson-content ul, .lesson-content ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}
</style>
@endpush
