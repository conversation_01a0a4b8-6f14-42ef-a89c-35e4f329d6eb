@extends('layouts.admin')

@section('title', 'Chỉnh Sửa Danh <PERSON>')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Chỉnh Sử<PERSON></h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.categories.index') }}">Danh <PERSON></a></li>
            <li class="breadcrumb-item active">Chỉnh Sửa</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fa fa-edit me-2"></i>Chỉnh Sửa Danh Mục: {{ $category->name }}
          </h5>
        </div>
        
        <form action="{{ route('admin.categories.update', $category->id) }}" method="POST">
          @csrf
          @method('PUT')
          
          <div class="card-body">
            <div class="row">
              <!-- Tên Danh Mục -->
              <div class="col-12 mb-3">
                <label for="name" class="form-label">Tên Danh Mục <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                       id="name" name="name" value="{{ old('name', $category->name) }}" required>
                @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <!-- Slug -->
              <div class="col-12 mb-3">
                <label for="slug" class="form-label">Slug</label>
                <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                       id="slug" name="slug" value="{{ old('slug', $category->slug) }}">
                @error('slug')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Để trống để tự động tạo từ tên danh mục</div>
              </div>

              <!-- Mô Tả -->
              <div class="col-12 mb-3">
                <label for="description" class="form-label">Mô Tả</label>
                <textarea class="form-control @error('description') is-invalid @enderror" 
                          id="description" name="description" rows="4">{{ old('description', $category->description) }}</textarea>
                @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <!-- Hình Ảnh -->
              <div class="col-12 mb-3">
                <label for="image_url" class="form-label">URL Hình Ảnh Danh Mục</label>

                @if($category->image_url)
                <div class="mb-2">
                  <img src="{{ $category->image_url }}" alt="Current Image"
                       class="img-fluid rounded" style="max-height: 200px;">
                  <p class="text-muted small mt-1">Hình Ảnh Hiện Tại</p>
                </div>
                @endif

                <input type="url" class="form-control @error('image_url') is-invalid @enderror"
                       id="image_url" name="image_url" value="{{ old('image_url', $category->image_url) }}"
                       placeholder="https://example.com/category-image.jpg">
                @error('image_url')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <div class="form-text">Nhập URL hình ảnh từ internet (tiết kiệm dung lượng server)</div>

                <!-- Preview -->
                <div id="image-preview-container" class="mt-2" style="display: none;">
                  <img id="image-preview" class="img-fluid rounded" style="max-height: 200px;" alt="Preview">
                </div>
              </div>



              <!-- Màu Sắc -->
              <div class="col-md-6 mb-3">
                <label for="color" class="form-label">Màu Sắc</label>
                <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" 
                       id="color" name="color" value="{{ old('color', $category->color ?? '#007bff') }}">
                @error('color')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <!-- Thứ Tự -->
              <div class="col-md-6 mb-3">
                <label for="sort_order" class="form-label">Thứ Tự Hiển Thị</label>
                <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                       id="sort_order" name="sort_order" value="{{ old('sort_order', $category->sort_order ?? 0) }}" min="0">
                @error('sort_order')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>

              <!-- Trạng Thái -->
              <div class="col-12 mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                         {{ old('is_active', $category->is_active) ? 'checked' : '' }}>
                  <label class="form-check-label" for="is_active">
                    <strong>Kích Hoạt Danh Mục</strong>
                  </label>
                  <div class="form-text">Danh mục sẽ hiển thị trên website khi được kích hoạt</div>
                </div>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <div class="d-flex justify-content-between">
              <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left me-2"></i>Quay Lại
              </a>
              <div>
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-save me-2"></i>Cập Nhật Danh Mục
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Sidebar Info -->
    <div class="col-lg-4">
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-info-circle me-2"></i>Thông Tin Danh Mục
          </h6>
        </div>
        <div class="card-body">
          <table class="table table-borderless table-sm">
            <tr>
              <td><strong>ID:</strong></td>
              <td>{{ $category->id }}</td>
            </tr>
            <tr>
              <td><strong>Slug:</strong></td>
              <td><code>{{ $category->slug }}</code></td>
            </tr>
            <tr>
              <td><strong>Số Khóa Học:</strong></td>
              <td>{{ $category->courses_count ?? $category->courses->count() }}</td>
            </tr>
            <tr>
              <td><strong>Trạng Thái:</strong></td>
              <td>
                @if($category->is_active)
                <span class="badge bg-success">Kích Hoạt</span>
                @else
                <span class="badge bg-secondary">Tạm Dừng</span>
                @endif
              </td>
            </tr>
            <tr>
              <td><strong>Tạo Lúc:</strong></td>
              <td>{{ $category->created_at->format('d/m/Y H:i') }}</td>
            </tr>
            <tr>
              <td><strong>Cập Nhật:</strong></td>
              <td>{{ $category->updated_at->format('d/m/Y H:i') }}</td>
            </tr>
          </table>
        </div>
      </div>

      <!-- Preview -->
      <div class="card mt-3">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-eye me-2"></i>Xem Trước
          </h6>
        </div>
        <div class="card-body text-center">
          @if($category->image_url)
          <img src="{{ $category->image_url }}" alt="{{ $category->name }}"
               class="img-fluid rounded mb-2" style="max-height: 100px;">
          @else
          <div class="bg-light rounded p-3 mb-2">
            <i class="fa fa-image fa-2x text-muted"></i>
          </div>
          @endif
          <h6 class="mb-1">{{ $category->name }}</h6>
          <p class="text-muted small">{{ Str::limit($category->description, 50) }}</p>
          @if($category->color)
          <div class="d-inline-block rounded" style="width: 20px; height: 20px; background-color: {{ $category->color }};"></div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Auto generate slug from name
document.getElementById('name').addEventListener('input', function() {
  const name = this.value;
  const slug = name.toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
  document.getElementById('slug').value = slug;
});
</script>
@endsection

@push('scripts')
<script>
// Preview URL Ảnh
document.getElementById('image_url').addEventListener('input', function(e) {
  const url = e.target.value;
  const preview = document.getElementById('image-preview');
  const container = document.getElementById('image-preview-container');

  console.log('URL entered:', url); // Debug

  if (url && url.trim() !== '') {
    preview.src = url;
    container.style.display = 'block';

    // Kiểm tra nếu ảnh load thành công
    preview.onload = function() {
      console.log('Image loaded successfully'); // Debug
      container.style.display = 'block';
    };

    preview.onerror = function() {
      console.log('Image failed to load'); // Debug
      container.style.display = 'none';
    };
  } else {
    container.style.display = 'none';
  }
});

// Auto generate slug from name
document.getElementById('name').addEventListener('input', function() {
  const name = this.value;
  const slug = name.toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
  document.getElementById('slug').value = slug;
});
</script>
@endpush
