<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use ZipArchive;

class BackupFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:files {--keep=7 : Số ngày giữ lại backup}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backup Files Quan Trọng (Storage, Uploads)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Bắt Đầu Backup Files...');

        try {
            // Tạo tên file backup
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $filename = "backup_files_{$timestamp}.zip";
            $backupPath = storage_path("app/backups/{$filename}");

            // Tạo thư mục backup nếu chưa có
            if (!file_exists(storage_path('app/backups'))) {
                mkdir(storage_path('app/backups'), 0755, true);
            }

            // Tạo ZIP archive
            $zip = new ZipArchive();
            if ($zip->open($backupPath, ZipArchive::CREATE) !== TRUE) {
                $this->error('❌ Không Thể Tạo File ZIP!');
                return Command::FAILURE;
            }

            // Backup storage/app/public (uploaded files)
            $this->addDirectoryToZip($zip, storage_path('app/public'), 'storage/public');

            // Backup .env file
            if (file_exists(base_path('.env'))) {
                $zip->addFile(base_path('.env'), 'env/.env');
            }

            // Backup composer files
            if (file_exists(base_path('composer.json'))) {
                $zip->addFile(base_path('composer.json'), 'composer/composer.json');
            }
            if (file_exists(base_path('composer.lock'))) {
                $zip->addFile(base_path('composer.lock'), 'composer/composer.lock');
            }

            $zip->close();

            if (file_exists($backupPath)) {
                $fileSize = $this->formatBytes(filesize($backupPath));
                $this->info("✅ Backup Files Thành Công: {$filename} ({$fileSize})");

                // Dọn dẹp backup cũ
                $this->cleanOldBackups($this->option('keep'));

                return Command::SUCCESS;
            } else {
                $this->error('❌ Backup Files Thất Bại!');
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error("❌ Lỗi Backup Files: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }

    /**
     * Thêm Thư Mục Vào ZIP
     */
    private function addDirectoryToZip($zip, $dir, $zipDir = '')
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($files as $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                $relativePath = $zipDir . '/' . substr($filePath, strlen($dir) + 1);
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    /**
     * Dọn Dẹp Backup Cũ
     */
    private function cleanOldBackups($keepDays)
    {
        $this->info("Dọn Dẹp File Backup Cũ Hơn {$keepDays} Ngày...");

        $backupDir = storage_path('app/backups');
        $cutoffDate = Carbon::now()->subDays($keepDays);
        $deletedCount = 0;

        if (is_dir($backupDir)) {
            $files = glob($backupDir . '/backup_files_*.zip');

            foreach ($files as $file) {
                $fileTime = Carbon::createFromTimestamp(filemtime($file));

                if ($fileTime->lt($cutoffDate)) {
                    unlink($file);
                    $deletedCount++;
                    $this->line("🗑️  Đã Xóa: " . basename($file));
                }
            }
        }

        if ($deletedCount > 0) {
            $this->info("✅ Đã Xóa {$deletedCount} File Backup Cũ");
        } else {
            $this->info("ℹ️  Không Có File Backup Cũ Cần Xóa");
        }
    }

    /**
     * Format Bytes
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
