<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Laravel\Socialite\Facades\Socialite;

class AuthController extends Controller
{
    /**
     * Hiển Thị Form Đăng Ký
     */
    public function showRegisterForm()
    {
        return view('auth.register');
    }

    /**
     * Xử Lý Đăng Ký
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
        ], [
            'name.required' => 'Họ Và Tên <PERSON> B<PERSON>ộ<PERSON>',
            'email.required' => 'Email <PERSON>',
            'email.email' => 'Email <PERSON>',
            'email.unique' => 'Email <PERSON>',
            'password.required' => 'M<PERSON><PERSON>ẩu <PERSON>à Bắt Buộc',
            'password.min' => 'Mật Khẩu Phải Có Ít Nhất 8 Ký Tự',
            'password.confirmed' => 'Xác Nhận Mật Khẩu Không Khớp',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'role' => 'user',
        ]);

        Auth::login($user);

        // Chuyển Hướng Theo Role
        if ($user->role === 'admin') {
            return redirect()->route('admin.dashboard')->with('success', 'Đăng Ký Thành Công!');
        }

        return redirect()->route('user.dashboard')->with('success', 'Đăng Ký Thành Công!');
    }

    /**
     * Hiển Thị Form Đăng Nhập
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Xử Lý Đăng Nhập
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ], [
            'email.required' => 'Email Là Bắt Buộc',
            'email.email' => 'Email Không Hợp Lệ',
            'password.required' => 'Mật Khẩu Là Bắt Buộc',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $credentials = $request->only('email', 'password');
        $remember = $request->has('remember');

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            // Chuyển Hướng Theo Role
            $user = Auth::user();
            if ($user->role === 'admin') {
                return redirect()->intended(route('admin.dashboard'))->with('success', 'Đăng Nhập Thành Công!');
            }

            return redirect()->intended(route('user.dashboard'))->with('success', 'Đăng Nhập Thành Công!');
        }

        return back()->withErrors([
            'email' => 'Thông Tin Đăng Nhập Không Chính Xác.',
        ])->withInput();
    }

    /**
     * Đăng Xuất
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login')->with('success', 'Đăng Xuất Thành Công!');
    }

    /**
     * Hiển Thị Trang Hồ Sơ Cá Nhân
     */
    public function profile()
    {
        return view('auth.profile', ['user' => Auth::user()]);
    }

    /**
     * Cập Nhật Hồ Sơ Cá Nhân
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|string|min:8|confirmed',
        ], [
            'name.required' => 'Họ Và Tên Là Bắt Buộc',
            'email.required' => 'Email Là Bắt Buộc',
            'email.email' => 'Email Không Hợp Lệ',
            'email.unique' => 'Email Đã Được Sử Dụng',
            'avatar.image' => 'File Phải Là Hình Ảnh',
            'avatar.mimes' => 'Hình Ảnh Phải Có Định Dạng: jpeg, png, jpg, gif',
            'avatar.max' => 'Kích Thước Hình Ảnh Không Được Vượt Quá 2MB',
            'current_password.required_with' => 'Vui Lòng Nhập Mật Khẩu Hiện Tại',
            'password.min' => 'Mật Khẩu Mới Phải Có Ít Nhất 8 Ký Tự',
            'password.confirmed' => 'Xác Nhận Mật Khẩu Mới Không Khớp',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Kiểm Tra Mật Khẩu Hiện Tại Nếu Muốn Đổi Mật Khẩu
        if ($request->filled('password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => 'Mật Khẩu Hiện Tại Không Chính Xác']);
            }
        }

        // Xử Lý Upload Avatar
        if ($request->hasFile('avatar')) {
            // Xóa Avatar Cũ
            if ($user->avatar && Storage::exists('public/' . $user->avatar)) {
                Storage::delete('public/' . $user->avatar);
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $avatarPath;
        }

        // Cập Nhật Thông Tin
        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;

        if ($request->filled('password')) {
            $user->password = Hash::make($request->password);
        }

        $user->save();

        return back()->with('success', 'Cập Nhật Hồ Sơ Thành Công!');
    }

    /**
     * API: Lấy Thông Tin User Hiện Tại
     */
    public function user(Request $request)
    {
        return response()->json([
            'success' => true,
            'data' => $request->user()
        ]);
    }

    /**
     * API: Đăng Ký
     */
    public function apiRegister(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'phone' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ Liệu Không Hợp Lệ',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'role' => 'user',
        ]);

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Đăng Ký Thành Công',
            'data' => [
                'user' => $user,
                'token' => $token
            ]
        ], 201);
    }

    /**
     * API: Đăng Nhập
     */
    public function apiLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ Liệu Không Hợp Lệ',
                'errors' => $validator->errors()
            ], 422);
        }

        if (!Auth::attempt($request->only('email', 'password'))) {
            return response()->json([
                'success' => false,
                'message' => 'Thông Tin Đăng Nhập Không Chính Xác'
            ], 401);
        }

        $user = User::where('email', $request->email)->firstOrFail();
        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Đăng Nhập Thành Công',
            'data' => [
                'user' => $user,
                'token' => $token
            ]
        ]);
    }

    /**
     * API: Đăng Xuất
     */
    public function apiLogout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Đăng Xuất Thành Công'
        ]);
    }

    /**
     * API: Cập Nhật Hồ Sơ
     */
    public function apiUpdateProfile(Request $request)
    {
        $user = $request->user();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ Liệu Không Hợp Lệ',
                'errors' => $validator->errors()
            ], 422);
        }

        // Xử Lý Upload Avatar
        if ($request->hasFile('avatar')) {
            if ($user->avatar && Storage::exists('public/' . $user->avatar)) {
                Storage::delete('public/' . $user->avatar);
            }
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $avatarPath;
        }

        $user->update($request->only(['name', 'email', 'phone']));

        return response()->json([
            'success' => true,
            'message' => 'Cập Nhật Hồ Sơ Thành Công',
            'data' => $user->fresh()
        ]);
    }

    /**
     * Chuyển Hướng Đến Google
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Xử Lý Callback Từ Google
     */
    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            // Tìm user theo email
            $user = User::where('email', $googleUser->getEmail())->first();

            if ($user) {
                // Cập nhật thông tin Google nếu chưa có
                if (!$user->google_id) {
                    $user->update([
                        'google_id' => $googleUser->getId(),
                        'avatar' => $googleUser->getAvatar(),
                    ]);
                }
            } else {
                // Tạo user mới
                $user = User::create([
                    'name' => $googleUser->getName(),
                    'email' => $googleUser->getEmail(),
                    'google_id' => $googleUser->getId(),
                    'avatar' => $googleUser->getAvatar(),
                    'email_verified_at' => now(),
                    'password' => Hash::make(uniqid()), // Random password
                    'role' => 'user',
                ]);
            }

            // Đăng nhập user
            Auth::login($user, true);

            // Chuyển hướng theo role
            if ($user->role === 'admin') {
                return redirect()->route('admin.dashboard')->with('success', 'Đăng Nhập Google Thành Công!');
            }

            return redirect()->route('user.dashboard')->with('success', 'Đăng Nhập Google Thành Công!');

        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'Đăng Nhập Google Thất Bại: ' . $e->getMessage());
        }
    }
}
