<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use App\Models\Category;
use App\Models\Course;
use App\Models\User;

class CacheService
{
    /**
     * Cache Duration Constants (in seconds)
     */
    const CACHE_SHORT = 300;    // 5 minutes
    const CACHE_MEDIUM = 1800;  // 30 minutes
    const CACHE_LONG = 3600;    // 1 hour
    const CACHE_DAILY = 86400;  // 24 hours

    /**
     * Cache Active Categories
     */
    public static function getActiveCategories()
    {
        return Cache::remember('categories.active', self::CACHE_LONG, function () {
            return Category::where('is_active', true)
                ->select('id', 'name', 'slug', 'description')
                ->orderBy('name')
                ->get();
        });
    }

    /**
     * Cache Published Courses Count By Category
     */
    public static function getCourseCountByCategory()
    {
        return Cache::remember('courses.count_by_category', self::CACHE_MEDIUM, function () {
            return Course::where('status', 'published')
                ->selectRaw('category_id, COUNT(*) as count')
                ->groupBy('category_id')
                ->pluck('count', 'category_id')
                ->toArray();
        });
    }

    /**
     * Cache Popular Courses
     */
    public static function getPopularCourses($limit = 6)
    {
        $cacheKey = "courses.popular.{$limit}";
        
        return Cache::remember($cacheKey, self::CACHE_MEDIUM, function () use ($limit) {
            return Course::with(['category:id,name'])
                ->where('status', 'published')
                ->withCount('approvedReviews')
                ->orderBy('approved_reviews_count', 'desc')
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Cache Free Courses
     */
    public static function getFreeCourses($limit = 6)
    {
        $cacheKey = "courses.free.{$limit}";
        
        return Cache::remember($cacheKey, self::CACHE_MEDIUM, function () use ($limit) {
            return Course::with(['category:id,name'])
                ->where('status', 'published')
                ->where('price', 0)
                ->latest()
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Cache Latest Courses
     */
    public static function getLatestCourses($limit = 6)
    {
        $cacheKey = "courses.latest.{$limit}";
        
        return Cache::remember($cacheKey, self::CACHE_SHORT, function () use ($limit) {
            return Course::with(['category:id,name'])
                ->where('status', 'published')
                ->latest()
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Cache User Statistics
     */
    public static function getUserStats()
    {
        return Cache::remember('stats.users', self::CACHE_MEDIUM, function () {
            return [
                'total' => User::count(),
                'active_today' => User::whereDate('updated_at', today())->count(),
                'new_this_week' => User::where('created_at', '>=', now()->subWeek())->count(),
                'admins' => User::where('role', 'admin')->count(),
            ];
        });
    }

    /**
     * Cache Course Statistics
     */
    public static function getCourseStats()
    {
        return Cache::remember('stats.courses', self::CACHE_MEDIUM, function () {
            return [
                'total' => Course::count(),
                'published' => Course::where('status', 'published')->count(),
                'draft' => Course::where('status', 'draft')->count(),
                'free' => Course::where('price', 0)->count(),
            ];
        });
    }

    /**
     * Cache System Health
     */
    public static function getSystemHealth()
    {
        return Cache::remember('system.health', self::CACHE_SHORT, function () {
            return [
                'database' => self::checkDatabaseHealth(),
                'cache' => self::checkCacheHealth(),
                'storage' => self::checkStorageHealth(),
                'timestamp' => now()->toISOString(),
            ];
        });
    }

    /**
     * Clear All Application Caches
     */
    public static function clearAllCaches()
    {
        $patterns = [
            'categories.*',
            'courses.*',
            'stats.*',
            'system.*',
            'user.*',
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }

        // Clear specific cache tags if using tagged cache
        if (method_exists(Cache::store(), 'tags')) {
            Cache::tags(['categories', 'courses', 'stats'])->flush();
        }
    }

    /**
     * Clear Course Related Caches
     */
    public static function clearCourseCaches()
    {
        $keys = [
            'courses.popular.*',
            'courses.latest.*',
            'courses.free.*',
            'courses.count_by_category',
            'stats.courses',
        ];

        foreach ($keys as $key) {
            if (str_contains($key, '*')) {
                // For wildcard patterns, we need to implement custom logic
                // This is a simplified version
                $baseKey = str_replace('.*', '', $key);
                for ($i = 1; $i <= 20; $i++) {
                    Cache::forget("{$baseKey}.{$i}");
                }
            } else {
                Cache::forget($key);
            }
        }
    }

    /**
     * Clear Category Related Caches
     */
    public static function clearCategoryCaches()
    {
        Cache::forget('categories.active');
        Cache::forget('courses.count_by_category');
    }

    /**
     * Clear User Related Caches
     */
    public static function clearUserCaches()
    {
        Cache::forget('stats.users');
    }

    /**
     * Check Database Health
     */
    private static function checkDatabaseHealth(): array
    {
        try {
            $start = microtime(true);
            \DB::select('SELECT 1');
            $responseTime = round((microtime(true) - $start) * 1000, 2);
            
            return [
                'status' => 'healthy',
                'response_time_ms' => $responseTime,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check Cache Health
     */
    private static function checkCacheHealth(): array
    {
        try {
            $testKey = 'health_check_' . time();
            Cache::put($testKey, 'test', 60);
            $result = Cache::get($testKey);
            Cache::forget($testKey);
            
            return [
                'status' => $result === 'test' ? 'healthy' : 'unhealthy',
                'driver' => config('cache.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check Storage Health
     */
    private static function checkStorageHealth(): array
    {
        try {
            $diskFree = disk_free_space(storage_path());
            $diskTotal = disk_total_space(storage_path());
            $usagePercent = round((($diskTotal - $diskFree) / $diskTotal) * 100, 2);
            
            return [
                'status' => $usagePercent < 90 ? 'healthy' : 'warning',
                'usage_percent' => $usagePercent,
                'free_space' => self::formatBytes($diskFree),
                'total_space' => self::formatBytes($diskTotal),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Format Bytes
     */
    private static function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
