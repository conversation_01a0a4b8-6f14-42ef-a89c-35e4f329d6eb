@extends('layouts.user')

@section('title', $lesson->title . ' - Tài <PERSON>')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{{ route('user.documents.index') }}">Tài <PERSON></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{{ route('my-courses.show', $lesson->chapter->course_id) }}">
                {{ $lesson->chapter->course->title }}
              </a>
            </li>
            <li class="breadcrumb-item active">{{ $lesson->title }}</li>
          </ol>
        </nav>
        <h4 class="mb-0">{{ $lesson->title }}</h4>
        <p class="text-muted mb-0">{{ $lesson->chapter->title }} - {{ $lesson->chapter->course->title }}</p>
      </div>
      <div>
        @if($lesson->attachment_url)
        <a href="{{ route('user.documents.download', $lesson->id) }}" class="btn btn-primary">
          <i class="fa fa-download me-2"></i>Tải Xuống
        </a>
        @endif
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-8">
    <!-- Nội Dung Tài Liệu -->
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-file-text me-2"></i>Nội Dung Tài Liệu
        </h5>
      </div>
      <div class="card-body">
        @if($lesson->type === 'pdf' && $lesson->attachment_url)
        <!-- PDF Viewer -->
        <div class="text-center mb-3">
          <iframe 
            src="{{ asset('storage/' . $lesson->attachment_url) }}" 
            width="100%" 
            height="600px"
            style="border: 1px solid #ddd; border-radius: 8px;">
            <p>Trình duyệt của bạn không hỗ trợ hiển thị PDF. 
               <a href="{{ route('user.documents.download', $lesson->id) }}">Tải xuống tại đây</a>
            </p>
          </iframe>
        </div>
        @endif

        @if($lesson->content)
        <!-- Text Content -->
        <div class="document-content">
          {!! nl2br(e($lesson->content)) !!}
        </div>
        @endif

        @if($lesson->video_url)
        <!-- Video -->
        <div class="mb-3">
          <h6>Video Hướng Dẫn:</h6>
          <div class="ratio ratio-16x9">
            <iframe src="{{ $lesson->video_url }}" allowfullscreen></iframe>
          </div>
        </div>
        @endif
      </div>
    </div>

    <!-- Hành Động -->
    <div class="card mt-4">
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            @if(!$userCourse->isLessonCompleted($lesson->id))
            <button class="btn btn-success" onclick="markAsCompleted({{ $lesson->id }})">
              <i class="fa fa-check me-2"></i>Đánh Dấu Hoàn Thành
            </button>
            @else
            <span class="badge bg-success fs-6">
              <i class="fa fa-check me-1"></i>Đã Hoàn Thành
            </span>
            @endif
          </div>
          <div class="col-md-6 text-end">
            <a href="{{ route('user.courses.lessons.show', ['course' => $lesson->chapter->course_id, 'chapter' => $lesson->chapter_id, 'lesson' => $lesson->id]) }}" class="btn btn-primary">
              <i class="fa fa-play me-2"></i>Đến Bài Học
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-lg-4">
    <!-- Thông Tin Tài Liệu -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">Thông Tin Tài Liệu</h6>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <strong>Loại:</strong>
          <span class="badge bg-info ms-2">
            @if($lesson->type === 'pdf')
              <i class="fa fa-file-pdf me-1"></i>PDF
            @else
              <i class="fa fa-file me-1"></i>{{ strtoupper($lesson->type) }}
            @endif
          </span>
        </div>

        <div class="mb-3">
          <strong>Thời Lượng:</strong>
          <span class="text-muted">{{ $lesson->duration_minutes ?? 0 }} phút</span>
        </div>

        <div class="mb-3">
          <strong>Khóa Học:</strong>
          <br><a href="{{ route('my-courses.show', $lesson->chapter->course_id) }}" class="text-decoration-none">
            {{ $lesson->chapter->course->title }}
          </a>
        </div>

        <div class="mb-3">
          <strong>Chương:</strong>
          <br><span class="text-muted">{{ $lesson->chapter->title }}</span>
        </div>

        <div class="mb-3">
          <strong>Trạng Thái:</strong>
          <br>
          @if($userCourse->isLessonCompleted($lesson->id))
          <span class="badge bg-success">Đã Hoàn Thành</span>
          @else
          <span class="badge bg-warning">Chưa Hoàn Thành</span>
          @endif
        </div>

        @if($lesson->is_preview)
        <div class="mb-3">
          <span class="badge bg-info">
            <i class="fa fa-eye me-1"></i>Xem Trước Miễn Phí
          </span>
        </div>
        @endif
      </div>
    </div>

    <!-- Tiến Độ Khóa Học -->
    <div class="card mt-4">
      <div class="card-header">
        <h6 class="mb-0">Tiến Độ Khóa Học</h6>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <div class="d-flex justify-content-between mb-1">
            <small>Hoàn Thành</small>
            <small>{{ $userCourse->progress_percentage }}%</small>
          </div>
          <div class="progress" style="height: 10px;">
            <div class="progress-bar bg-success" style="width: {{ $userCourse->progress_percentage }}%"></div>
          </div>
        </div>

        <div class="row text-center">
          <div class="col-6">
            <small class="text-muted d-block">Đã Học</small>
            <strong>{{ count($userCourse->completed_lessons ?? []) }}</strong>
          </div>
          <div class="col-6">
            <small class="text-muted d-block">Tổng Số</small>
            <strong>{{ $userCourse->course->total_lessons }}</strong>
          </div>
        </div>
      </div>
    </div>

    <!-- Tài Liệu Liên Quan -->
    <div class="card mt-4">
      <div class="card-header">
        <h6 class="mb-0">Tài Liệu Cùng Khóa Học</h6>
      </div>
      <div class="card-body">
        @php
        $relatedDocs = $lesson->chapter->course->chapters()
          ->with(['lessons' => function($query) {
            $query->where(function($q) {
              $q->where('type', 'pdf')->orWhereNotNull('attachment_url');
            });
          }])
          ->get()
          ->pluck('lessons')
          ->flatten()
          ->take(5);
        @endphp

        @if($relatedDocs->count() > 0)
        @foreach($relatedDocs as $doc)
        <div class="d-flex align-items-center mb-2 {{ $doc->id == $lesson->id ? 'bg-light p-2 rounded' : '' }}">
          <i class="fa fa-file-text text-muted me-2"></i>
          <div class="flex-grow-1">
            @if($doc->id == $lesson->id)
            <strong>{{ $doc->title }}</strong>
            @else
            <a href="{{ route('user.documents.view', $doc->id) }}" class="text-decoration-none">
              {{ $doc->title }}
            </a>
            @endif
          </div>
          @if($userCourse->isLessonCompleted($doc->id))
          <i class="fa fa-check text-success"></i>
          @endif
        </div>
        @endforeach
        @else
        <p class="text-muted mb-0">Không có tài liệu khác</p>
        @endif
      </div>
    </div>
  </div>
</div>
@endsection

@push('scripts')
<script>
function markAsCompleted(lessonId) {
    fetch(`/user/my-courses/{{ $lesson->chapter->course_id }}/lessons/${lessonId}/complete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Có lỗi xảy ra');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra');
    });
}
</script>
@endpush
