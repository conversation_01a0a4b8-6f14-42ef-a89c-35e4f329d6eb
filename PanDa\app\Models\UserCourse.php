<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserCourse extends Model
{
    protected $fillable = [
        'user_id',
        'course_id',
        'order_id',
        'enrolled_at',
        'started_at',
        'completed_at',
        'progress_percentage',
        'completed_lessons',
        'last_accessed_at',
        'current_lesson_id',
    ];

    protected $casts = [
        'enrolled_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'completed_lessons' => 'array',
        'progress_percentage' => 'integer',
    ];

    /**
     * Quan Hệ Với User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Quan Hệ Với Course
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Quan Hệ <PERSON>ới Order
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Quan Hệ Với Current Lesson
     */
    public function currentLesson(): BelongsTo
    {
        return $this->belongsTo(Lesson::class, 'current_lesson_id');
    }

    /**
     * Scope Đang Học
     */
    public function scopeInProgress($query)
    {
        return $query->whereNotNull('started_at')
                    ->whereNull('completed_at');
    }

    /**
     * Scope Đã Hoàn Thành
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    /**
     * Scope Chưa Bắt Đầu
     */
    public function scopeNotStarted($query)
    {
        return $query->whereNull('started_at');
    }

    /**
     * Bắt Đầu Học
     */
    public function startLearning()
    {
        if (!$this->started_at) {
            $this->update([
                'started_at' => now(),
                'last_accessed_at' => now(),
            ]);
        }
    }

    /**
     * Cập Nhật Tiến Độ
     */
    public function updateProgress($lessonId)
    {
        $completedLessons = $this->completed_lessons ?? [];

        if (!in_array($lessonId, $completedLessons)) {
            $completedLessons[] = $lessonId;
        }

        $totalLessons = $this->course->total_lessons;
        $progressPercentage = $totalLessons > 0 ?
            round((count($completedLessons) / $totalLessons) * 100) : 0;

        $updateData = [
            'completed_lessons' => $completedLessons,
            'progress_percentage' => $progressPercentage,
            'last_accessed_at' => now(),
            'current_lesson_id' => $lessonId,
        ];

        // Kiểm tra hoàn thành: 100% bài học VÀ admin đã đánh dấu khóa học hoàn thành
        $shouldComplete = $progressPercentage >= 100 &&
                         $this->course->is_completed &&
                         !$this->completed_at;

        if ($shouldComplete) {
            $updateData['completed_at'] = now();

            // Tự động tạo certificate
            $this->update($updateData);
            \App\Models\Certificate::createForUserCourse($this);
        } else {
            // Nếu admin thêm bài mới sau khi user đã hoàn thành, reset completed_at
            if ($this->completed_at && ($progressPercentage < 100 || !$this->course->is_completed)) {
                $updateData['completed_at'] = null;
            }

            $this->update($updateData);
        }
    }

    /**
     * Kiểm Tra Lesson Đã Hoàn Thành
     */
    public function isLessonCompleted($lessonId): bool
    {
        $completedLessons = $this->completed_lessons ?? [];
        return in_array($lessonId, $completedLessons);
    }

    /**
     * Lấy Lesson Tiếp Theo
     */
    public function getNextLesson()
    {
        $completedLessons = $this->completed_lessons ?? [];

        // Lấy tất cả lessons từ chapters đã được eager loaded
        $allLessons = collect();

        foreach ($this->course->chapters->sortBy('position') as $chapter) {
            foreach ($chapter->lessons->sortBy('position') as $lesson) {
                $allLessons->push($lesson);
            }
        }

        foreach ($allLessons as $lesson) {
            if (!in_array($lesson->id, $completedLessons)) {
                return $lesson;
            }
        }

        return null; // Đã hoàn thành tất cả
    }

    /**
     * Accessor Cho Status Text
     */
    public function getStatusTextAttribute(): string
    {
        if ($this->completed_at) {
            return 'Đã Hoàn Thành';
        } elseif ($this->started_at) {
            return 'Đang Học';
        } else {
            return 'Chưa Bắt Đầu';
        }
    }

    /**
     * Accessor Cho Status Class
     */
    public function getStatusClassAttribute(): string
    {
        if ($this->completed_at) {
            return 'success';
        } elseif ($this->started_at) {
            return 'warning';
        } else {
            return 'secondary';
        }
    }
}
