@extends('layouts.user')

@section('title', $course->title . ' - <PERSON><PERSON>p Online')

@section('content')
<div class="row">
  <div class="col-lg-8">
    <!-- Thông Tin Khóa Học -->
    <div class="card mb-4">
      @if($course->image_url)
      <img src="{{ $course->image_url }}" class="card-img-top" alt="{{ $course->title }}" style="height: 300px; object-fit: cover;">
      @else
      <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
        <i class="fa fa-book fa-4x text-muted"></i>
      </div>
      @endif
      
      <div class="card-body">
        <div class="mb-3">
          <span class="badge bg-primary">{{ $course->category->name }}</span>
          <span class="badge bg-secondary ms-1">
            @if($course->level == 'beginner') Người <PERSON>
            @elseif($course->level == 'intermediate') Trung Cấp
            @else Nâng Cao
            @endif
          </span>
        </div>
        
        <h1 class="card-title h3">{{ $course->title }}</h1>
        <p class="card-text">{{ $course->description }}</p>
        
        <div class="row text-center mb-4">
          <div class="col-3">
            <div class="border-end">
              <h5 class="text-primary mb-0">{{ $course->total_lessons }}</h5>
              <small class="text-muted">Bài Học</small>
            </div>
          </div>
          <div class="col-3">
            <div class="border-end">
              <h5 class="text-success mb-0">{{ $course->duration_hours }} Giờ</h5>
              <small class="text-muted">Thời Lượng</small>
            </div>
          </div>
          <div class="col-3">
            <div class="border-end">
              <h5 class="text-warning mb-0">{{ $course->chapters->count() }}</h5>
              <small class="text-muted">Chương</small>
            </div>
          </div>
          <div class="col-3">
            <h5 class="text-info mb-0">0</h5>
            <small class="text-muted">Học Viên</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Nội Dung Khóa Học -->
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Nội Dung Khóa Học
        </h5>
      </div>
      <div class="card-body">
        <div class="accordion" id="courseContent">
          @foreach($course->chapters as $index => $chapter)
          <div class="accordion-item">
            <h2 class="accordion-header" id="heading{{ $index }}">
              <button class="accordion-button {{ $index == 0 ? '' : 'collapsed' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $index }}" aria-expanded="{{ $index == 0 ? 'true' : 'false' }}" aria-controls="collapse{{ $index }}">
                <div class="d-flex justify-content-between w-100 me-3">
                  <span>{{ $chapter->title }}</span>
                  <small class="text-muted">{{ $chapter->lessons->count() }} Bài - {{ $chapter->total_duration }} Phút</small>
                </div>
              </button>
            </h2>
            <div id="collapse{{ $index }}" class="accordion-collapse collapse {{ $index == 0 ? 'show' : '' }}" aria-labelledby="heading{{ $index }}" data-bs-parent="#courseContent">
              <div class="accordion-body">
                @if($chapter->description)
                <p class="text-muted mb-3">{{ $chapter->description }}</p>
                @endif
                
                <div class="list-group list-group-flush">
                  @foreach($chapter->lessons as $lesson)
                  <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                      @if($lesson->type == 'video')
                      <i class="fa fa-play-circle text-primary me-2"></i>
                      @elseif($lesson->type == 'text')
                      <i class="fa fa-file-text text-success me-2"></i>
                      @elseif($lesson->type == 'pdf')
                      <i class="fa fa-file-pdf text-danger me-2"></i>
                      @else
                      <i class="fa fa-question-circle text-warning me-2"></i>
                      @endif
                      
                      <div>
                        @if($lesson->is_preview)
                        <a href="{{ route('lessons.show', $lesson->id) }}" class="text-decoration-none">
                          {{ $lesson->title }}
                        </a>
                        <span class="badge bg-success ms-2">Preview</span>
                        @else
                        <span class="text-muted">{{ $lesson->title }}</span>
                        <i class="fa fa-lock ms-2 text-warning"></i>
                        @endif
                      </div>
                    </div>
                    
                    <small class="text-muted">{{ $lesson->duration_minutes }} Phút</small>
                  </div>
                  @endforeach
                </div>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="col-lg-4">
    <!-- Giá & Mua Khóa Học -->
    <div class="card mb-4 sticky-top" style="top: 20px;">
      <div class="card-body text-center">
        <div class="mb-3">
          @if($course->price > 0)
          <h3 class="text-primary mb-0">{{ number_format($course->price, 0, ',', '.') }}đ</h3>
          @else
          <h3 class="text-success mb-0">Miễn Phí</h3>
          @endif
        </div>
        
        <div class="d-grid gap-2">
          @auth
          @if($course->price > 0)
          <form method="POST" action="{{ route('cart.store') }}">
            @csrf
            <input type="hidden" name="course_id" value="{{ $course->id }}">
            <button type="submit" class="btn btn-primary btn-lg w-100">
              <i class="fa fa-shopping-cart me-2"></i>Thêm Vào Giỏ Hàng
            </button>
          </form>
          <button class="btn btn-outline-primary">
            <i class="fa fa-credit-card me-2"></i>Mua Ngay
          </button>
          @else
          <button class="btn btn-success btn-lg">
            <i class="fa fa-play me-2"></i>Học Miễn Phí
          </button>
          @endif
          @else
          <a href="{{ route('login') }}" class="btn btn-primary btn-lg">
            <i class="fa fa-sign-in-alt me-2"></i>Đăng Nhập Để Mua
          </a>
          @endauth

          <button class="btn btn-outline-secondary">
            <i class="fa fa-heart me-2"></i>Yêu Thích
          </button>
        </div>
        
        <hr>
        
        <div class="text-start">
          <h6>Khóa Học Bao Gồm:</h6>
          <ul class="list-unstyled">
            <li><i class="fa fa-check text-success me-2"></i>{{ $course->total_lessons }} Bài Học</li>
            <li><i class="fa fa-check text-success me-2"></i>{{ $course->duration_hours }} Giờ Video</li>
            <li><i class="fa fa-check text-success me-2"></i>Truy Cập Trọn Đời</li>
            <li><i class="fa fa-check text-success me-2"></i>Chứng Chỉ Hoàn Thành</li>
            <li><i class="fa fa-check text-success me-2"></i>Hỗ Trợ Q&A</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Yêu Cầu -->
    @if($course->requirements)
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Yêu Cầu
        </h6>
      </div>
      <div class="card-body">
        <div class="text-muted">{{ $course->requirements }}</div>
      </div>
    </div>
    @endif

    <!-- Bạn Sẽ Học Được -->
    @if($course->what_you_learn)
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Bạn Sẽ Học Được
        </h6>
      </div>
      <div class="card-body">
        <div class="text-muted">{{ $course->what_you_learn }}</div>
      </div>
    </div>
    @endif

    <!-- Khóa Học Liên Quan -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Khóa Học Liên Quan
        </h6>
      </div>
      <div class="card-body">
        <div class="text-center text-muted">
          <i class="fa fa-book fa-2x mb-2"></i>
          <p class="mb-0">Chưa Có Khóa Học Liên Quan</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mt-4">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Trang Chủ</a></li>
    <li class="breadcrumb-item"><a href="{{ route('courses.index') }}">Khóa Học</a></li>
    <li class="breadcrumb-item active" aria-current="page">{{ $course->title }}</li>
  </ol>
</nav>
@endsection
