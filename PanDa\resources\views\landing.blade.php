<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>PanDa - Nền <PERSON>p <PERSON><PERSON> 3</title>

    <!-- Core CSS -->
    <link rel="stylesheet" href="{{ asset('assets/css/styles.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/custom.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #495057;
        }

        .navbar {
            background: white !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: #6c757d !important;
        }

        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
            margin: 0 0.5rem;
        }

        .navbar-nav .nav-link:hover {
            color: #6c757d !important;
        }

        .hero-section {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            color: white;
            padding-top: 80px;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn-primary {
            background-color: #6c757d;
            border-color: #6c757d;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 25px;
        }

        .btn-primary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }

        .btn-outline-light {
            border-color: white;
            color: white;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 25px;
        }

        .btn-outline-light:hover {
            background-color: white;
            color: #6c757d;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #495057;
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            border: 1px solid #dee2e6;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .pricing-card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            border: 1px solid #dee2e6;
            height: 100%;
        }

        .pricing-card.featured {
            border-color: #6c757d;
            transform: scale(1.02);
        }

        .pricing-badge {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #6c757d;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #495057;
        }

        .currency {
            font-size: 1.2rem;
            vertical-align: top;
        }

        .period {
            font-size: 1rem;
            color: #6c757d;
        }

        .footer {
            background: #495057;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer a {
            color: #dee2e6;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: white;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .pricing-card.featured {
                transform: none;
                margin-top: 1rem;
            }
        }
    </style>
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg fixed-top">
    <div class="container">
        <a class="navbar-brand" href="/">
            <i class="fa fa-graduation-cap me-2"></i>PanDa
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link" href="#features">Tính Năng</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#pricing">Giá Cả</a>
                </li>
                @guest
                <li class="nav-item">
                    <a class="nav-link" href="{{ route('login') }}">Đăng Nhập</a>
                </li>
                <li class="nav-item">
                    <a class="btn btn-primary ms-2" href="{{ route('register') }}">Đăng Ký</a>
                </li>
                @else
                <li class="nav-item">
                    <a class="btn btn-primary ms-2" href="{{ route('user.dashboard') }}">Dashboard</a>
                </li>
                @endguest
            </ul>
        </div>
    </div>
</nav>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="hero-title">
                    Nền Tảng Học Tập Trực Tuyến
                    <span class="text-warning">Hàng Đầu</span>
                    Cho Học Sinh Cấp 3
                </h1>
                <p class="hero-subtitle">
                    Khám Phá Hàng Ngàn Khóa Học Chất Lượng Cao Với Chi Phí Cực Kỳ Hợp Lý.
                    Học Mọi Lúc, Mọi Nơi Cùng Đội Ngũ Giảng Viên Chuyên Nghiệp.
                </p>
                <div class="hero-stats mb-4">
                    <div class="row text-center">
                        <div class="col-6 col-md-3">
                            <h3 class="text-warning">{{ $stats['total_courses'] }}+</h3>
                            <p class="text-white-50">Khóa Học</p>
                        </div>
                        <div class="col-6 col-md-3">
                            <h3 class="text-warning">{{ $stats['total_students'] }}+</h3>
                            <p class="text-white-50">Học Viên</p>
                        </div>
                        <div class="col-6 col-md-3">
                            <h3 class="text-warning">{{ $stats['total_categories'] }}+</h3>
                            <p class="text-white-50">Danh Mục</p>
                        </div>
                        <div class="col-6 col-md-3">
                            <h3 class="text-warning">{{ $stats['total_reviews'] }}+</h3>
                            <p class="text-white-50">Đánh Giá</p>
                        </div>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="{{ route('courses.index') }}" class="btn btn-warning me-3">
                        <i class="fa fa-search me-2"></i>Khám Phá Khóa Học
                    </a>
                    @guest
                    <a href="{{ route('register') }}" class="btn btn-outline-light">
                        <i class="fa fa-user-plus me-2"></i>Đăng Ký Ngay
                    </a>
                    @endguest
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <div class="bg-white bg-opacity-10 rounded-4 p-4">
                        <i class="fa fa-graduation-cap fa-5x text-warning mb-3"></i>
                        <h4>Học Tập Thông Minh</h4>
                        <p>Cùng PanDa - Nơi Tri Thức Không Giới Hạn</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-5 bg-light">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="section-title">Tại Sao Chọn PanDa?</h2>
                <p class="section-subtitle">Những Ưu Điểm Vượt Trội Của Chúng Tôi</p>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon bg-success mb-3">
                        <i class="fa fa-money"></i>
                    </div>
                    <h4>Chi Phí Siêu Rẻ</h4>
                    <p class="text-muted">Học Phí Chỉ Từ 50.000đ/Khóa - Phù Hợp Với Mọi Gia Đình Việt Nam</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon bg-primary mb-3">
                        <i class="fa fa-clock-o"></i>
                    </div>
                    <h4>Học Mọi Lúc Mọi Nơi</h4>
                    <p class="text-muted">24/7 Truy Cập Không Giới Hạn - Học Theo Tốc Độ Của Riêng Bạn</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon bg-warning mb-3">
                        <i class="fa fa-user"></i>
                    </div>
                    <h4>Giảng Viên Top Đầu</h4>
                    <p class="text-muted">Đội Ngũ Thầy Cô Giàu Kinh Nghiệm Từ Các Trường THPT Chuyên</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon bg-info mb-3">
                        <i class="fa fa-certificate"></i>
                    </div>
                    <h4>Chứng Chỉ Uy Tín</h4>
                    <p class="text-muted">Nhận Chứng Chỉ Hoàn Thành Được Các Trường Đại Học Công Nhận</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon bg-danger mb-3">
                        <i class="fa fa-mobile"></i>
                    </div>
                    <h4>Đa Nền Tảng</h4>
                    <p class="text-muted">Học Trên Điện Thoại, Máy Tính Bảng Hay Laptop Đều Mượt Mà</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon bg-secondary mb-3">
                        <i class="fa fa-headphones"></i>
                    </div>
                    <h4>Hỗ Trợ Tận Tình</h4>
                    <p class="text-muted">Đội Ngũ Hỗ Trợ Học Tập Luôn Sẵn Sàng Giải Đáp 24/7</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section id="pricing" class="py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="section-title">Đánh Giá Của Học Sinh</h2>
                <p class="section-subtitle">Những Chia Sẻ Chân Thật Từ Các Bạn Học Sinh Đã Học Tại PanDa</p>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="pricing-card">
                    <div class="text-center mb-3">
                        <div class="d-inline-block bg-primary rounded-circle p-3 mb-3">
                            <i class="fa fa-user text-white fa-2x"></i>
                        </div>
                        <h5>Nguyễn Minh Anh</h5>
                        <p class="text-muted">Học Sinh Lớp 12A1 - THPT Chu Văn An</p>
                    </div>
                    <div class="mb-3 text-center">
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                    </div>
                    <p class="text-muted">"Nhờ PanDa Mà Em Đã Nâng Điểm Toán Từ 5 Lên 8.5. Các Thầy Cô Giảng Rất Dễ Hiểu Và Luôn Nhiệt Tình Hỗ Trợ."</p>
                    <div class="text-center">
                        <small class="text-success"><i class="fa fa-check-circle me-1"></i>Đã Học 6 Tháng</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="pricing-card featured">
                    <div class="pricing-badge">Học Sinh Xuất Sắc</div>
                    <div class="text-center mb-3">
                        <div class="d-inline-block bg-success rounded-circle p-3 mb-3">
                            <i class="fa fa-user text-white fa-2x"></i>
                        </div>
                        <h5>Trần Văn Hùng</h5>
                        <p class="text-muted">Học Sinh Lớp 11A2 - THPT Lê Quý Đôn</p>
                    </div>
                    <div class="mb-3 text-center">
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                    </div>
                    <p class="text-muted">"PanDa Giúp Em Tiết Kiệm Rất Nhiều Tiền Học Thêm. Chỉ 99k/Tháng Mà Học Được Tất Cả Môn. Quá Tuyệt Vời!"</p>
                    <div class="text-center">
                        <small class="text-success"><i class="fa fa-trophy me-1"></i>Top 1 Lớp Liên Tục</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="pricing-card">
                    <div class="text-center mb-3">
                        <div class="d-inline-block bg-warning rounded-circle p-3 mb-3">
                            <i class="fa fa-user text-white fa-2x"></i>
                        </div>
                        <h5>Lê Thị Mai</h5>
                        <p class="text-muted">Học Sinh Lớp 10A3 - THPT Nguyễn Trãi</p>
                    </div>
                    <div class="mb-3 text-center">
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                    </div>
                    <p class="text-muted">"Em Rất Thích Cách Giảng Của Các Thầy Cô. Không Khô Khan Mà Rất Sinh Động. Em Đã Yêu Thích Môn Hóa Hơn Rồi!"</p>
                    <div class="text-center">
                        <small class="text-success"><i class="fa fa-heart me-1"></i>Yêu Thích Nhất</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="pricing-card">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-info rounded-circle p-2 me-3">
                            <i class="fa fa-user text-white"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">Phạm Quốc Việt</h6>
                            <small class="text-muted">Học Sinh Lớp 12B1 - THPT Trần Phú</small>
                        </div>
                    </div>
                    <div class="mb-2">
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                    </div>
                    <p class="text-muted mb-2">"Chuẩn Bị Thi Đại Học Với PanDa Thật Sự Hiệu Quả. Em Tự Tin Sẽ Đậu Được Trường Mơ Ước!"</p>
                </div>
            </div>
            <div class="col-lg-6 col-md-6 mb-4">
                <div class="pricing-card">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-danger rounded-circle p-2 me-3">
                            <i class="fa fa-user text-white"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">Hoàng Thị Lan</h6>
                            <small class="text-muted">Học Sinh Lớp 11C2 - THPT Lý Thái Tổ</small>
                        </div>
                    </div>
                    <div class="mb-2">
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                        <i class="fa fa-star text-warning"></i>
                    </div>
                    <p class="text-muted mb-2">"Học Trên Điện Thoại Rất Tiện. Em Có Thể Học Mọi Lúc Mọi Nơi, Ngay Cả Khi Đi Xe Bus!"</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 text-white" style="background: #6c757d;">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="mb-4">Sẵn Sàng Bắt Đầu Hành Trình Học Tập?</h2>
                <p class="lead mb-4">Tham Gia Cùng Hàng Ngàn Học Sinh Đã Thành Công Với PanDa</p>
                <div class="cta-buttons">
                    @guest
                    <a href="{{ route('register') }}" class="btn btn-warning me-3">
                        <i class="fa fa-user-plus me-2"></i>Đăng Ký Miễn Phí
                    </a>
                    @endguest
                    <a href="{{ route('courses.index') }}" class="btn btn-outline-light">
                        <i class="fa fa-search me-2"></i>Xem Khóa Học
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="footer">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <h5><i class="fa fa-graduation-cap me-2"></i>PanDa</h5>
                <p class="text-muted">Nền Tảng Học Tập Trực Tuyến Hàng Đầu Cho Học Sinh Cấp 3 Việt Nam</p>
            </div>
            <div class="col-lg-2 col-md-6 mb-4">
                <h6>Sản Phẩm</h6>
                <ul class="list-unstyled">
                    <li><a href="{{ route('courses.index') }}">Khóa Học</a></li>
                    <li><a href="#">Ứng Dụng Mobile</a></li>
                    <li><a href="#">Chứng Chỉ</a></li>
                </ul>
            </div>
            <div class="col-lg-2 col-md-6 mb-4">
                <h6>Hỗ Trợ</h6>
                <ul class="list-unstyled">
                    <li><a href="#">Trung Tâm Trợ Giúp</a></li>
                    <li><a href="#">Liên Hệ</a></li>
                    <li><a href="#">FAQ</a></li>
                </ul>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <h6>Liên Hệ</h6>
                <p class="mb-2"><i class="fa fa-envelope me-2"></i><EMAIL></p>
                <p class="mb-2"><i class="fa fa-phone me-2"></i>1900 1234</p>
                <p><i class="fa fa-map-marker me-2"></i>Hà Nội, Việt Nam</p>
            </div>
        </div>
        <hr class="my-4">
        <div class="row">
            <div class="col-12 text-center">
                <p class="mb-0">&copy; 2024 PanDa. Tất Cả Quyền Được Bảo Lưu.</p>
            </div>
        </div>
    </div>
</footer>

<!-- Core JS -->
<script src="{{ asset('assets/js/vendor.min.js') }}"></script>
<script src="{{ asset('assets/js/theme/app.init.js') }}"></script>
<script src="{{ asset('assets/js/theme/theme.js') }}"></script>
<script src="{{ asset('assets/js/theme/app.min.js') }}"></script>

<script>
// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add navbar background on scroll
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.classList.add('bg-white', 'shadow-sm');
        navbar.classList.remove('bg-transparent');
    } else {
        navbar.classList.remove('bg-white', 'shadow-sm');
        navbar.classList.add('bg-transparent');
    }
});
</script>

</body>
</html>