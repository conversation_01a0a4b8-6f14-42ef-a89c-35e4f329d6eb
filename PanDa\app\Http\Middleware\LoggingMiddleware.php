<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Carbon\Carbon;

class LoggingMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);

        // Log request
        $this->logRequest($request);

        $response = $next($request);

        // Log response
        $this->logResponse($request, $response, $startTime);

        return $response;
    }

    /**
     * Log Request
     */
    private function logRequest(Request $request)
    {
        $logData = [
            'timestamp' => Carbon::now()->toISOString(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => auth()->id(),
            'session_id' => $request->session()->getId(),
        ];

        // Log sensitive data carefully
        if (!$this->isSensitiveRoute($request)) {
            $logData['parameters'] = $request->except(['password', 'password_confirmation', '_token']);
        }

        Log::channel('requests')->info('HTTP Request', $logData);
    }

    /**
     * Log Response
     */
    private function logResponse(Request $request, Response $response, $startTime)
    {
        $duration = round((microtime(true) - $startTime) * 1000, 2);

        $logData = [
            'timestamp' => Carbon::now()->toISOString(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'status_code' => $response->getStatusCode(),
            'duration_ms' => $duration,
            'memory_usage' => $this->formatBytes(memory_get_peak_usage(true)),
            'user_id' => auth()->id(),
        ];

        $logLevel = $this->getLogLevel($response->getStatusCode());
        Log::channel('requests')->{$logLevel}('HTTP Response', $logData);

        // Log slow requests
        if ($duration > 1000) { // > 1 second
            Log::channel('performance')->warning('Slow Request Detected', [
                'url' => $request->fullUrl(),
                'duration_ms' => $duration,
                'memory_usage' => $logData['memory_usage'],
            ]);
        }
    }

    /**
     * Check If Route Is Sensitive
     */
    private function isSensitiveRoute(Request $request): bool
    {
        $sensitiveRoutes = [
            'login',
            'register',
            'password',
            'admin/login',
        ];

        foreach ($sensitiveRoutes as $route) {
            if (str_contains($request->path(), $route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get Log Level Based On Status Code
     */
    private function getLogLevel(int $statusCode): string
    {
        if ($statusCode >= 500) {
            return 'error';
        } elseif ($statusCode >= 400) {
            return 'warning';
        } else {
            return 'info';
        }
    }

    /**
     * Format Bytes
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
