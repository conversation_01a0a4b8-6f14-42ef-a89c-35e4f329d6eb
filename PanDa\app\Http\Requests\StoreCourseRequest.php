<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->role === 'admin';
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'category_id' => 'required|exists:categories,id',
            'title' => 'required|string|max:255|unique:courses,title',
            'description' => 'required|string|min:50',
            'requirements' => 'nullable|string|max:1000',
            'what_you_learn' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0|max:99999999',
            'duration_hours' => 'required|integer|min:1|max:1000',
            'level' => 'required|in:beginner,intermediate,advanced',
            'status' => 'required|in:draft,published,archived',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'category_id.required' => 'Vui lòng chọn danh mục',
            'category_id.exists' => 'Danh mục không tồn tại',
            'title.required' => 'Tiêu đề khóa học là bắt buộc',
            'title.unique' => 'Tiêu đề khóa học đã tồn tại',
            'title.max' => 'Tiêu đề không được quá 255 ký tự',
            'description.required' => 'Mô tả khóa học là bắt buộc',
            'description.min' => 'Mô tả phải có ít nhất 50 ký tự',
            'price.required' => 'Giá khóa học là bắt buộc',
            'price.numeric' => 'Giá phải là số',
            'price.min' => 'Giá không được âm',
            'duration_hours.required' => 'Thời lượng là bắt buộc',
            'duration_hours.integer' => 'Thời lượng phải là số nguyên',
            'duration_hours.min' => 'Thời lượng ít nhất 1 giờ',
            'level.required' => 'Cấp độ là bắt buộc',
            'level.in' => 'Cấp độ không hợp lệ',
            'status.required' => 'Trạng thái là bắt buộc',
            'status.in' => 'Trạng thái không hợp lệ',
            'image.image' => 'File phải là hình ảnh',
            'image.mimes' => 'Hình ảnh phải có định dạng: jpeg, png, jpg, gif',
            'image.max' => 'Hình ảnh không được quá 2MB',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'category_id' => 'Danh Mục',
            'title' => 'Tiêu Đề',
            'description' => 'Mô Tả',
            'requirements' => 'Yêu Cầu',
            'what_you_learn' => 'Bạn Sẽ Học Được',
            'price' => 'Giá',
            'duration_hours' => 'Thời Lượng',
            'level' => 'Cấp Độ',
            'status' => 'Trạng Thái',
            'image' => 'Hình Ảnh',
        ];
    }
}