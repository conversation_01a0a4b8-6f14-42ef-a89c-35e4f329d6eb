@extends('layouts.admin')

@section('title', 'Quản Lý Bình Luận')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Quản Lý Bình Luận</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active">Bình Luận</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="total-comments">0</h4>
              <p class="mb-0">Tổng Bình Luận</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-comments fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="approved-comments">0</h4>
              <p class="mb-0">Đã Duyệt</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-check-circle fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-warning text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="pending-comments">0</h4>
              <p class="mb-0">Chờ Duyệt</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-clock fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3">
      <div class="card bg-info text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-1" id="today-comments">0</h4>
              <p class="mb-0">Hôm Nay</p>
            </div>
            <div class="align-self-center">
              <i class="fa fa-calendar fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="GET" class="row g-3">
            <div class="col-md-3">
              <label class="form-label">Tìm Kiếm</label>
              <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                     placeholder="Nội dung, tên user, bài học...">
            </div>
            <div class="col-md-2">
              <label class="form-label">Trạng Thái</label>
              <select class="form-select" name="status">
                <option value="">Tất Cả</option>
                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Đã Duyệt</option>
                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Chờ Duyệt</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">Loại</label>
              <select class="form-select" name="type">
                <option value="">Tất Cả</option>
                <option value="parent" {{ request('type') == 'parent' ? 'selected' : '' }}>Bình Luận Chính</option>
                <option value="reply" {{ request('type') == 'reply' ? 'selected' : '' }}>Trả Lời</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Người Dùng</label>
              <select class="form-select" name="user_id">
                <option value="">Tất Cả User</option>
                @foreach($users as $user)
                <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                  {{ $user->name }}
                </option>
                @endforeach
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-search me-1"></i>Tìm
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Bulk Actions -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form id="bulk-form" method="POST">
            @csrf
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <input type="checkbox" id="select-all" class="form-check-input me-2">
                <label for="select-all" class="form-check-label">Chọn Tất Cả</label>
              </div>
              <div>
                <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('approve')">
                  <i class="fa fa-check me-1"></i>Duyệt Đã Chọn
                </button>
                <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                  <i class="fa fa-trash me-1"></i>Xóa Đã Chọn
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Comments List -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-comments me-2"></i>Danh Sách Bình Luận ({{ $comments->total() }})
          </h5>
        </div>
        <div class="card-body p-0">
          @if($comments->count() > 0)
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th width="50">
                    <input type="checkbox" class="form-check-input">
                  </th>
                  <th>Người Dùng</th>
                  <th>Nội Dung</th>
                  <th>Bài Học</th>
                  <th>Loại</th>
                  <th>Trạng Thái</th>
                  <th>Ngày Tạo</th>
                  <th>Thao Tác</th>
                </tr>
              </thead>
              <tbody>
                @foreach($comments as $comment)
                <tr>
                  <td>
                    <input type="checkbox" class="form-check-input comment-checkbox" value="{{ $comment->id }}">
                  </td>
                  
                  <td>
                    <div class="d-flex align-items-center">
                      @if($comment->user->avatar)
                      <img src="{{ asset('storage/' . $comment->user->avatar) }}" alt="{{ $comment->user->name }}" 
                           class="rounded-circle me-2" width="32" height="32">
                      @else
                      <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                           style="width: 32px; height: 32px;">
                        <span class="text-white fw-bold">{{ strtoupper(substr($comment->user->name, 0, 1)) }}</span>
                      </div>
                      @endif
                      <div>
                        <h6 class="mb-0">{{ $comment->user->name }}</h6>
                        <small class="text-muted">{{ $comment->user->email }}</small>
                      </div>
                    </div>
                  </td>
                  
                  <td>
                    <div>
                      <p class="mb-1">{{ Str::limit($comment->content, 100) }}</p>
                      @if($comment->parent_id)
                      <small class="text-muted">
                        <i class="fa fa-reply me-1"></i>Trả lời: {{ Str::limit($comment->parent->content, 50) }}
                      </small>
                      @endif
                    </div>
                  </td>
                  
                  <td>
                    <div>
                      <h6 class="mb-1">{{ $comment->lesson->title }}</h6>
                      <small class="text-muted">{{ $comment->lesson->chapter->course->title }}</small>
                    </div>
                  </td>
                  
                  <td>
                    @if($comment->parent_id)
                    <span class="badge bg-info">Trả Lời</span>
                    @else
                    <span class="badge bg-primary">Bình Luận</span>
                    @endif
                  </td>
                  
                  <td>
                    @if($comment->is_approved)
                    <span class="badge bg-success">Đã Duyệt</span>
                    @else
                    <span class="badge bg-warning">Chờ Duyệt</span>
                    @endif
                  </td>
                  
                  <td>{{ $comment->created_at->format('d/m/Y H:i') }}</td>
                  
                  <td>
                    @include('admin.partials.action-buttons', [
                      'showRoute' => route('admin.comments.show', $comment->id),
                      'deleteRoute' => route('admin.comments.destroy', $comment->id),
                      'deleteMessage' => 'Bạn có chắc muốn xóa bình luận này?'
                    ])

                    @if(!$comment->is_approved)
                    <form action="{{ route('admin.comments.approve', $comment->id) }}" method="POST" class="d-inline" style="margin-left: 2px;">
                      @csrf
                      @method('PATCH')
                      <button type="submit" class="btn btn-outline-success d-flex align-items-center justify-content-center"
                              data-bs-toggle="tooltip"
                              style="width: 32px; height: 32px;"
                              aria-label="Duyệt"
                              data-bs-original-title="Duyệt">
                        <i class="fa fa-check" style="font-size: 14px;"></i>
                      </button>
                    </form>
                    @else
                    <form action="{{ route('admin.comments.reject', $comment->id) }}" method="POST" class="d-inline" style="margin-left: 2px;">
                      @csrf
                      @method('PATCH')
                      <button type="submit" class="btn btn-outline-warning d-flex align-items-center justify-content-center"
                              data-bs-toggle="tooltip"
                              style="width: 32px; height: 32px;"
                              aria-label="Từ Chối"
                              data-bs-original-title="Từ Chối">
                        <i class="fa fa-times" style="font-size: 14px;"></i>
                      </button>
                    </form>
                    @endif
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
          
          <!-- Pagination -->
          <div class="card-footer">
            {{ $comments->links() }}
          </div>
          @else
          <div class="text-center py-5">
            <i class="fa fa-comments fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Không Tìm Thấy Bình Luận</h5>
            <p class="text-muted">Thử thay đổi bộ lọc để tìm kiếm</p>
          </div>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Load stats
fetch('{{ route("admin.comments.stats") }}')
  .then(response => response.json())
  .then(data => {
    document.getElementById('total-comments').textContent = data.total_comments;
    document.getElementById('approved-comments').textContent = data.approved_comments;
    document.getElementById('pending-comments').textContent = data.pending_comments;
    document.getElementById('today-comments').textContent = data.today_comments;
  })
  .catch(error => console.error('Error loading stats:', error));

// Select all checkbox
document.getElementById('select-all').addEventListener('change', function() {
  const checkboxes = document.querySelectorAll('.comment-checkbox');
  checkboxes.forEach(checkbox => {
    checkbox.checked = this.checked;
  });
});

// Bulk actions
function bulkAction(action) {
  const checkedBoxes = document.querySelectorAll('.comment-checkbox:checked');
  if (checkedBoxes.length === 0) {
    alert('Vui lòng chọn ít nhất một bình luận!');
    return;
  }
  
  const commentIds = Array.from(checkedBoxes).map(cb => cb.value);
  const form = document.getElementById('bulk-form');
  
  // Clear existing hidden inputs
  form.querySelectorAll('input[name="comment_ids[]"]').forEach(input => input.remove());
  
  // Add comment IDs
  commentIds.forEach(id => {
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'comment_ids[]';
    input.value = id;
    form.appendChild(input);
  });
  
  if (action === 'approve') {
    form.action = '{{ route("admin.comments.bulk-approve") }}';
  } else if (action === 'delete') {
    if (!confirm('Bạn có chắc muốn xóa các bình luận đã chọn?')) {
      return;
    }
    form.action = '{{ route("admin.comments.bulk-delete") }}';
  }
  
  form.submit();
}
</script>
@endsection
