@extends('layouts.admin')

@section('title', '<PERSON> Tiết Bài H<PERSON> - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Chi Tiết Bài Học</h4>
        <p class="text-muted mb-0">{{ $lesson->title }}</p>
      </div>
      <div>
        <a href="{{ route('admin.lessons.edit', $lesson->id) }}" class="btn btn-warning me-2">
          <i class="fa fa-edit me-2"></i>Chỉnh Sửa
        </a>
        <a href="{{ route('admin.lessons.index', ['chapter_id' => $lesson->chapter_id]) }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-8">
    <!-- Thông Tin Bài Học -->
    <div class="card mb-4">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>{{ $lesson->title }}
          </h5>
          <div>
            @if($lesson->type == 'video')
            <span class="badge bg-primary"><i class="fa fa-play-circle me-1"></i>Video</span>
            @elseif($lesson->type == 'text')
            <span class="badge bg-success"><i class="fa fa-file-text me-1"></i>Văn Bản</span>
            @elseif($lesson->type == 'pdf')
            <span class="badge bg-danger"><i class="fa fa-file-pdf me-1"></i>PDF</span>
            @else
            <span class="badge bg-warning"><i class="fa fa-question-circle me-1"></i>Quiz</span>
            @endif
            
            @if($lesson->is_preview)
            <span class="badge bg-success ms-1">Preview</span>
            @endif
            
            @if($lesson->exercise_files && count($lesson->exercise_files) > 0)
            <span class="badge bg-warning ms-1"><i class="fa fa-tasks me-1"></i>{{ count($lesson->exercise_files) }} Bài Tập</span>
            @endif
            
            @if($lesson->is_active)
            <span class="badge bg-success ms-1">Hoạt Động</span>
            @else
            <span class="badge bg-secondary ms-1">Ẩn</span>
            @endif
          </div>
        </div>
      </div>
      
      <div class="card-body">
        <!-- Video Content -->
        @if($lesson->type == 'video' && $lesson->video_url)
        <div class="mb-4">
          <h6>Video URL:</h6>
          <a href="{{ $lesson->video_url }}" target="_blank" class="btn btn-outline-primary">
            <i class="fa fa-external-link-alt me-2"></i>Xem Video
          </a>
          <div class="mt-2">
            <small class="text-muted">{{ $lesson->video_url }}</small>
          </div>
        </div>
        @endif

        <!-- Text Content -->
        @if($lesson->type == 'text' && $lesson->content)
        <div class="mb-4">
          <h6>Nội Dung:</h6>
          <div class="border rounded p-3 bg-light">
            {!! nl2br(e($lesson->content)) !!}
          </div>
        </div>
        @endif

        <!-- Quiz Content -->
        @if($lesson->type == 'quiz' && $lesson->content)
        <div class="mb-4">
          <h6>Nội Dung Quiz:</h6>
          <div class="border rounded p-3 bg-light">
            {!! nl2br(e($lesson->content)) !!}
          </div>
        </div>
        @endif

        <!-- PDF File -->
        @if($lesson->type == 'pdf' && $lesson->file_path)
        <div class="mb-4">
          <h6>File PDF:</h6>
          <a href="{{ asset('storage/' . $lesson->file_path) }}" target="_blank" class="btn btn-outline-danger">
            <i class="fa fa-file-pdf me-2"></i>Xem PDF
          </a>
          <div class="mt-2">
            <small class="text-muted">{{ basename($lesson->file_path) }}</small>
          </div>
        </div>
        @endif

        <!-- Exercise Files -->
        @if($lesson->exercise_files && count($lesson->exercise_files) > 0)
        <div class="mb-4">
          <h6>File Bài Tập ({{ count($lesson->exercise_files) }} File):</h6>
          <div class="row">
            @foreach($lesson->exercise_files as $file)
            <div class="col-md-6 mb-2">
              <div class="card border-light">
                <div class="card-body p-3">
                  <div class="d-flex align-items-center">
                    @if($file['type'] == 'pdf')
                    <i class="fa fa-file-pdf fa-2x text-danger me-3"></i>
                    @elseif(in_array($file['type'], ['zip', 'rar']))
                    <i class="fa fa-file-archive fa-2x text-warning me-3"></i>
                    @else
                    <i class="fa fa-file-alt fa-2x text-primary me-3"></i>
                    @endif

                    <div class="flex-grow-1">
                      <h6 class="mb-1">{{ $file['name'] }}</h6>
                      <small class="text-muted">{{ number_format($file['size'] / 1024, 1) }} KB</small>
                    </div>

                    <a href="{{ asset('storage/' . $file['path']) }}" target="_blank" class="btn btn-outline-success btn-sm">
                      <i class="fa fa-download"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            @endforeach
          </div>
        </div>
        @endif

        <!-- Thống Kê -->
        <div class="row text-center">
          <div class="col-3">
            <div class="border-end">
              <h5 class="text-primary mb-0">{{ $lesson->position }}</h5>
              <small class="text-muted">Vị Trí</small>
            </div>
          </div>
          <div class="col-3">
            <div class="border-end">
              <h5 class="text-success mb-0">{{ $lesson->duration_minutes }}</h5>
              <small class="text-muted">Phút</small>
            </div>
          </div>
          <div class="col-3">
            <div class="border-end">
              <h5 class="text-warning mb-0">0</h5>
              <small class="text-muted">Lượt Xem</small>
            </div>
          </div>
          <div class="col-3">
            <h5 class="text-info mb-0">0</h5>
            <small class="text-muted">Hoàn Thành</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="col-lg-4">
    <!-- Thông Tin Chương -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Thông Tin Chương
        </h6>
      </div>
      <div class="card-body">
        <div class="mb-2">
          <small class="text-muted">Khóa Học:</small>
          <div class="fw-bold">{{ $lesson->chapter->course->title }}</div>
        </div>
        <div class="mb-2">
          <small class="text-muted">Chương:</small>
          <div class="fw-bold">{{ $lesson->chapter->title }}</div>
        </div>
        <div class="mb-2">
          <small class="text-muted">Vị Trí Chương:</small>
          <div class="fw-bold">{{ $lesson->chapter->position }}</div>
        </div>
        
        <div class="mt-3">
          <a href="{{ route('admin.chapters.show', $lesson->chapter->id) }}" class="btn btn-outline-primary btn-sm w-100">
            <i class="fa fa-eye me-2"></i>Xem Chi Tiết Chương
          </a>
        </div>
      </div>
    </div>

    <!-- Hành Động -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Hành Động
        </h6>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a href="{{ route('lessons.show', $lesson->id) }}" class="btn btn-outline-primary" target="_blank">
            <i class="fa fa-eye me-2"></i>Xem Trang Công Khai
          </a>
          <a href="{{ route('admin.lessons.edit', $lesson->id) }}" class="btn btn-warning">
            <i class="fa fa-edit me-2"></i>Chỉnh Sửa
          </a>
          <form method="POST" action="{{ route('admin.lessons.destroy', $lesson->id) }}" class="d-inline">
            @csrf
            @method('DELETE')
            <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Bạn Có Chắc Muốn Xóa?')">
              <i class="fa fa-trash me-2"></i>Xóa Bài Học
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Thông Tin Kỹ Thuật -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Thông Tin Kỹ Thuật
        </h6>
      </div>
      <div class="card-body">
        <div class="mb-2">
          <small class="text-muted">ID:</small>
          <span class="fw-bold">{{ $lesson->id }}</span>
        </div>
        <div class="mb-2">
          <small class="text-muted">Loại:</small>
          <span class="fw-bold">{{ ucfirst($lesson->type) }}</span>
        </div>
        <div class="mb-2">
          <small class="text-muted">Ngày Tạo:</small>
          <span class="fw-bold">{{ $lesson->created_at->format('d/m/Y H:i') }}</span>
        </div>
        <div class="mb-2">
          <small class="text-muted">Cập Nhật Cuối:</small>
          <span class="fw-bold">{{ $lesson->updated_at->format('d/m/Y H:i') }}</span>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
