@extends('layouts.user')

@section('title', '<PERSON> Tiết Đơn Hàng - ' . $order->order_number)

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Chi Tiết Đơn Hàng</h4>
        <p class="text-muted mb-0">{{ $order->order_number }}</p>
      </div>
      <div>
        <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-8">
    <!-- Thông Tin Đơn Hàng -->
    <div class="card mb-4">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>{{ $order->order_number }}
          </h5>
          <div>
            @if($order->status == 'pending')
            <span class="badge bg-warning fs-6">{{ $order->status_text }}</span>
            @elseif($order->status == 'approved')
            <span class="badge bg-success fs-6">{{ $order->status_text }}</span>
            @elseif($order->status == 'rejected')
            <span class="badge bg-danger fs-6">{{ $order->status_text }}</span>
            @else
            <span class="badge bg-info fs-6">{{ $order->status_text }}</span>
            @endif
          </div>
        </div>
      </div>
      
      <div class="card-body">
        <div class="row mb-4">
          <div class="col-md-6">
            <h6>Thông Tin Đơn Hàng</h6>
            <div class="mb-2">
              <small class="text-muted">Mã Đơn:</small>
              <span class="fw-bold">{{ $order->order_number }}</span>
            </div>
            <div class="mb-2">
              <small class="text-muted">Ngày Tạo:</small>
              <span class="fw-bold">{{ $order->created_at->format('d/m/Y H:i') }}</span>
            </div>
            <div class="mb-2">
              <small class="text-muted">Phương Thức:</small>
              <span class="fw-bold">{{ $order->payment_method_text }}</span>
            </div>
          </div>
          
          <div class="col-md-6">
            <h6>Trạng Thái</h6>
            @if($order->approved_at)
            <div class="mb-2">
              <small class="text-muted">Ngày Duyệt:</small>
              <span class="fw-bold">{{ $order->approved_at->format('d/m/Y H:i') }}</span>
            </div>
            @endif
            
            @if($order->approvedBy)
            <div class="mb-2">
              <small class="text-muted">Người Duyệt:</small>
              <span class="fw-bold">{{ $order->approvedBy->name }}</span>
            </div>
            @endif
          </div>
        </div>
        
        @if($order->note)
        <div class="mb-4">
          <h6>Ghi Chú Của Bạn</h6>
          <div class="alert alert-light">
            {{ $order->note }}
          </div>
        </div>
        @endif
        
        @if($order->admin_note)
        <div class="mb-4">
          <h6>Phản Hồi Từ Admin</h6>
          <div class="alert alert-info">
            <i class="fa fa-info-circle me-2"></i>
            {{ $order->admin_note }}
          </div>
        </div>
        @endif
      </div>
    </div>

    <!-- Danh Sách Khóa Học -->
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Khóa Học ({{ $order->items->count() }})
        </h5>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Khóa Học</th>
                <th>Ghi Chú</th>
                <th>Giá</th>
              </tr>
            </thead>
            <tbody>
              @foreach($order->items as $item)
              <tr>
                <td>
                  <div>
                    <h6 class="mb-1">{{ $item->course_title }}</h6>
                    @if($item->course)
                    <small class="text-muted">{{ $item->course->category->name ?? 'N/A' }}</small>
                    @endif
                  </div>
                </td>
                <td>
                  @if($item->note)
                  <span class="text-muted">{{ $item->note }}</span>
                  @else
                  <span class="text-muted">-</span>
                  @endif
                </td>
                <td>
                  <span class="fw-bold text-success">
                    @if($item->course_price > 0)
                    {{ number_format($item->course_price, 0, ',', '.') }}đ
                    @else
                    Miễn Phí
                    @endif
                  </span>
                </td>
              </tr>
              @endforeach
            </tbody>
            <tfoot class="table-primary">
              <tr>
                <th colspan="2">Tổng Cộng</th>
                <th class="text-success">{{ number_format($order->total_amount, 0, ',', '.') }}đ</th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="col-lg-4">
    <!-- Tóm Tắt -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Tóm Tắt Đơn Hàng
        </h6>
      </div>
      <div class="card-body">
        <div class="d-flex justify-content-between mb-2">
          <span>Số Khóa Học:</span>
          <span class="fw-bold">{{ $order->items->count() }}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
          <span>Phương Thức:</span>
          <span class="fw-bold">{{ $order->payment_method_text }}</span>
        </div>
        <hr>
        <div class="d-flex justify-content-between">
          <span class="h6">Tổng Tiền:</span>
          <span class="h6 text-success">{{ number_format($order->total_amount, 0, ',', '.') }}đ</span>
        </div>
      </div>
    </div>

    <!-- Trạng Thái -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Trạng Thái Đơn Hàng
        </h6>
      </div>
      <div class="card-body">
        @if($order->status == 'pending')
        <div class="alert alert-warning">
          <i class="fa fa-clock me-2"></i>
          <strong>Chờ Duyệt</strong><br>
          Đơn hàng đang chờ admin xem xét và phê duyệt.
        </div>
        @elseif($order->status == 'approved')
        <div class="alert alert-success">
          <i class="fa fa-check-circle me-2"></i>
          <strong>Đã Duyệt</strong><br>
          Đơn hàng đã được admin phê duyệt.
        </div>
        @elseif($order->status == 'rejected')
        <div class="alert alert-danger">
          <i class="fa fa-times-circle me-2"></i>
          <strong>Bị Từ Chối</strong><br>
          Đơn hàng đã bị admin từ chối.
        </div>
        @else
        <div class="alert alert-info">
          <i class="fa fa-check-double me-2"></i>
          <strong>Hoàn Thành</strong><br>
          Đơn hàng đã được hoàn thành.
        </div>
        @endif
      </div>
    </div>

    <!-- Hành Động -->
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-code me-2"></i>Hành Động
        </h6>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a href="{{ route('orders.index') }}" class="btn btn-outline-primary">
            <i class="fa fa-list me-2"></i>Tất Cả Đơn Hàng
          </a>
          
          @if($order->status == 'approved' || $order->status == 'completed')
          <a href="{{ route('courses.index') }}" class="btn btn-success">
            <i class="fa fa-play me-2"></i>Bắt Đầu Học
          </a>
          @endif
          
          <a href="{{ route('cart.index') }}" class="btn btn-outline-secondary">
            <i class="fa fa-shopping-cart me-2"></i>Giỏ Hàng
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
