<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Hiển Thị Trang Cài Đặt Chính
     */
    public function index()
    {
        $settings = $this->getAllSettings();
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Cài Đặt Tổng Quan
     */
    public function general()
    {
        $settings = $this->getAllSettings();
        return view('admin.settings.general', compact('settings'));
    }

    /**
     * Cập Nhật Cài Đặt Tổng Quan
     */
    public function updateGeneral(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'site_name' => 'required|string|max:255',
            'site_description' => 'required|string|max:500',
            'site_keywords' => 'nullable|string|max:500',
            'admin_email' => 'required|email',
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:20',
            'contact_address' => 'nullable|string|max:500',
            'timezone' => 'required|string',
            'language' => 'required|string',
            'site_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'site_favicon' => 'nullable|image|mimes:ico,png|max:512',
        ], [
            'site_name.required' => 'Tên Website Là Bắt Buộc',
            'site_description.required' => 'Mô Tả Website Là Bắt Buộc',
            'admin_email.required' => 'Email Admin Là Bắt Buộc',
            'contact_email.required' => 'Email Liên Hệ Là Bắt Buộc',
            'timezone.required' => 'Múi Giờ Là Bắt Buộc',
            'language.required' => 'Ngôn Ngữ Là Bắt Buộc',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Upload logo
        if ($request->hasFile('site_logo')) {
            $logoPath = $request->file('site_logo')->store('settings', 'public');
            $this->updateSetting('site_logo', $logoPath);
        }

        // Upload favicon
        if ($request->hasFile('site_favicon')) {
            $faviconPath = $request->file('site_favicon')->store('settings', 'public');
            $this->updateSetting('site_favicon', $faviconPath);
        }

        // Cập nhật các setting khác
        $settings = [
            'site_name', 'site_description', 'site_keywords',
            'admin_email', 'contact_email', 'contact_phone', 'contact_address',
            'timezone', 'language'
        ];

        foreach ($settings as $setting) {
            if ($request->has($setting)) {
                $this->updateSetting($setting, $request->$setting);
            }
        }

        return back()->with('success', 'Cập Nhật Cài Đặt Tổng Quan Thành Công!');
    }

    /**
     * Cài Đặt Email
     */
    public function email()
    {
        $settings = $this->getAllSettings();
        return view('admin.settings.email', compact('settings'));
    }

    /**
     * Cập Nhật Cài Đặt Email
     */
    public function updateEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mail_driver' => 'required|in:smtp,sendmail,mailgun,ses,postmark',
            'mail_host' => 'required_if:mail_driver,smtp|string',
            'mail_port' => 'required_if:mail_driver,smtp|integer',
            'mail_username' => 'required_if:mail_driver,smtp|string',
            'mail_password' => 'required_if:mail_driver,smtp|string',
            'mail_encryption' => 'nullable|in:tls,ssl',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $emailSettings = [
            'mail_driver', 'mail_host', 'mail_port', 'mail_username',
            'mail_password', 'mail_encryption', 'mail_from_address', 'mail_from_name'
        ];

        foreach ($emailSettings as $setting) {
            if ($request->has($setting)) {
                $this->updateSetting($setting, $request->$setting);
            }
        }

        return back()->with('success', 'Cập Nhật Cài Đặt Email Thành Công!');
    }

    /**
     * Cài Đặt SEO
     */
    public function seo()
    {
        $settings = $this->getAllSettings();
        return view('admin.settings.seo', compact('settings'));
    }

    /**
     * Cập Nhật Cài Đặt SEO
     */
    public function updateSeo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'seo_title' => 'nullable|string|max:60',
            'seo_description' => 'nullable|string|max:160',
            'seo_keywords' => 'nullable|string|max:500',
            'google_analytics' => 'nullable|string',
            'google_tag_manager' => 'nullable|string',
            'facebook_pixel' => 'nullable|string',
            'robots_txt' => 'nullable|string',
            'sitemap_enabled' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $seoSettings = [
            'seo_title', 'seo_description', 'seo_keywords',
            'google_analytics', 'google_tag_manager', 'facebook_pixel',
            'robots_txt', 'sitemap_enabled'
        ];

        foreach ($seoSettings as $setting) {
            if ($request->has($setting)) {
                $this->updateSetting($setting, $request->$setting);
            }
        }

        return back()->with('success', 'Cập Nhật Cài Đặt SEO Thành Công!');
    }

    /**
     * Cài Đặt Bảo Mật
     */
    public function security()
    {
        $settings = $this->getAllSettings();
        return view('admin.settings.security', compact('settings'));
    }

    /**
     * Cập Nhật Cài Đặt Bảo Mật
     */
    public function updateSecurity(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'login_attempts' => 'required|integer|min:3|max:10',
            'session_timeout' => 'required|integer|min:15|max:1440',
            'password_min_length' => 'required|integer|min:6|max:20',
            'require_email_verification' => 'boolean',
            'enable_2fa' => 'boolean',
            'enable_captcha' => 'boolean',
            'captcha_site_key' => 'nullable|string',
            'captcha_secret_key' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $securitySettings = [
            'login_attempts', 'session_timeout', 'password_min_length',
            'require_email_verification', 'enable_2fa', 'enable_captcha',
            'captcha_site_key', 'captcha_secret_key'
        ];

        foreach ($securitySettings as $setting) {
            if ($request->has($setting)) {
                $this->updateSetting($setting, $request->$setting);
            }
        }

        return back()->with('success', 'Cập Nhật Cài Đặt Bảo Mật Thành Công!');
    }

    /**
     * Cài Đặt Mạng Xã Hội
     */
    public function social()
    {
        $settings = $this->getAllSettings();
        return view('admin.settings.social', compact('settings'));
    }

    /**
     * Cập Nhật Cài Đặt Mạng Xã Hội
     */
    public function updateSocial(Request $request)
    {
        $socialSettings = [
            'facebook_url', 'twitter_url', 'instagram_url', 'youtube_url',
            'linkedin_url', 'tiktok_url', 'discord_url', 'telegram_url',
            'facebook_app_id', 'facebook_app_secret',
            'google_client_id', 'google_client_secret',
            'github_client_id', 'github_client_secret'
        ];

        foreach ($socialSettings as $setting) {
            if ($request->has($setting)) {
                $this->updateSetting($setting, $request->$setting);
            }
        }

        return back()->with('success', 'Cập Nhật Cài Đặt Mạng Xã Hội Thành Công!');
    }

    /**
     * Cài Đặt Hệ Thống
     */
    public function system()
    {
        $settings = $this->getAllSettings();
        $systemInfo = $this->getSystemInfo();
        return view('admin.settings.system', compact('settings', 'systemInfo'));
    }

    /**
     * Cập Nhật Cài Đặt Hệ Thống
     */
    public function updateSystem(Request $request)
    {
        $systemSettings = [
            'maintenance_mode', 'debug_mode', 'cache_enabled',
            'log_level', 'max_upload_size', 'allowed_file_types',
            'backup_enabled', 'backup_frequency'
        ];

        foreach ($systemSettings as $setting) {
            if ($request->has($setting)) {
                $this->updateSetting($setting, $request->$setting);
            }
        }

        return back()->with('success', 'Cập Nhật Cài Đặt Hệ Thống Thành Công!');
    }

    /**
     * Xóa Cache
     */
    public function clearCache()
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');

            return back()->with('success', 'Đã Xóa Cache Thành Công!');
        } catch (\Exception $e) {
            return back()->with('error', 'Lỗi Khi Xóa Cache: ' . $e->getMessage());
        }
    }

    /**
     * Tối Ưu Hệ Thống
     */
    public function optimize()
    {
        try {
            Artisan::call('config:cache');
            Artisan::call('route:cache');
            Artisan::call('view:cache');

            return back()->with('success', 'Đã Tối Ưu Hệ Thống Thành Công!');
        } catch (\Exception $e) {
            return back()->with('error', 'Lỗi Khi Tối Ưu: ' . $e->getMessage());
        }
    }

    /**
     * Backup Database
     */
    public function backup()
    {
        try {
            $filename = 'backup_' . date('Y_m_d_H_i_s') . '.sql';
            $path = storage_path('app/backups/' . $filename);

            // Tạo thư mục backup nếu chưa có
            if (!File::exists(storage_path('app/backups'))) {
                File::makeDirectory(storage_path('app/backups'), 0755, true);
            }

            // Thực hiện backup (simplified version)
            $tables = DB::select('SHOW TABLES');
            $sql = '';

            foreach ($tables as $table) {
                $tableName = array_values((array) $table)[0];
                $sql .= "DROP TABLE IF EXISTS `{$tableName}`;\n";

                $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`")[0];
                $sql .= $createTable->{'Create Table'} . ";\n\n";

                $rows = DB::select("SELECT * FROM `{$tableName}`");
                foreach ($rows as $row) {
                    $values = array_map(function($value) {
                        return is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }, (array) $row);

                    $sql .= "INSERT INTO `{$tableName}` VALUES (" . implode(', ', $values) . ");\n";
                }
                $sql .= "\n";
            }

            File::put($path, $sql);

            return response()->download($path)->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return back()->with('error', 'Lỗi Khi Backup: ' . $e->getMessage());
        }
    }

    /**
     * Lấy Tất Cả Settings
     */
    private function getAllSettings()
    {
        return [
            // General Settings
            'site_name' => $this->getSetting('site_name', 'PanDa Learning'),
            'site_description' => $this->getSetting('site_description', 'Nền tảng học trực tuyến hàng đầu'),
            'site_keywords' => $this->getSetting('site_keywords', 'học trực tuyến, khóa học, giáo dục'),
            'admin_email' => $this->getSetting('admin_email', '<EMAIL>'),
            'contact_email' => $this->getSetting('contact_email', '<EMAIL>'),
            'contact_phone' => $this->getSetting('contact_phone', ''),
            'contact_address' => $this->getSetting('contact_address', ''),
            'timezone' => $this->getSetting('timezone', 'Asia/Ho_Chi_Minh'),
            'language' => $this->getSetting('language', 'vi'),
            'site_logo' => $this->getSetting('site_logo', ''),
            'site_favicon' => $this->getSetting('site_favicon', ''),

            // Email Settings
            'mail_driver' => $this->getSetting('mail_driver', 'smtp'),
            'mail_host' => $this->getSetting('mail_host', 'smtp.gmail.com'),
            'mail_port' => $this->getSetting('mail_port', '587'),
            'mail_username' => $this->getSetting('mail_username', ''),
            'mail_password' => $this->getSetting('mail_password', ''),
            'mail_encryption' => $this->getSetting('mail_encryption', 'tls'),
            'mail_from_address' => $this->getSetting('mail_from_address', '<EMAIL>'),
            'mail_from_name' => $this->getSetting('mail_from_name', 'PanDa Learning'),

            // SEO Settings
            'seo_title' => $this->getSetting('seo_title', ''),
            'seo_description' => $this->getSetting('seo_description', ''),
            'seo_keywords' => $this->getSetting('seo_keywords', ''),
            'google_analytics' => $this->getSetting('google_analytics', ''),
            'google_tag_manager' => $this->getSetting('google_tag_manager', ''),
            'facebook_pixel' => $this->getSetting('facebook_pixel', ''),
            'robots_txt' => $this->getSetting('robots_txt', ''),
            'sitemap_enabled' => $this->getSetting('sitemap_enabled', true),

            // Security Settings
            'login_attempts' => $this->getSetting('login_attempts', 5),
            'session_timeout' => $this->getSetting('session_timeout', 120),
            'password_min_length' => $this->getSetting('password_min_length', 8),
            'require_email_verification' => $this->getSetting('require_email_verification', true),
            'enable_2fa' => $this->getSetting('enable_2fa', false),
            'enable_captcha' => $this->getSetting('enable_captcha', false),
            'captcha_site_key' => $this->getSetting('captcha_site_key', ''),
            'captcha_secret_key' => $this->getSetting('captcha_secret_key', ''),

            // Social Settings
            'facebook_url' => $this->getSetting('facebook_url', ''),
            'twitter_url' => $this->getSetting('twitter_url', ''),
            'instagram_url' => $this->getSetting('instagram_url', ''),
            'youtube_url' => $this->getSetting('youtube_url', ''),
            'linkedin_url' => $this->getSetting('linkedin_url', ''),
            'tiktok_url' => $this->getSetting('tiktok_url', ''),
            'discord_url' => $this->getSetting('discord_url', ''),
            'telegram_url' => $this->getSetting('telegram_url', ''),
            'facebook_app_id' => $this->getSetting('facebook_app_id', ''),
            'facebook_app_secret' => $this->getSetting('facebook_app_secret', ''),
            'google_client_id' => $this->getSetting('google_client_id', ''),
            'google_client_secret' => $this->getSetting('google_client_secret', ''),
            'github_client_id' => $this->getSetting('github_client_id', ''),
            'github_client_secret' => $this->getSetting('github_client_secret', ''),

            // System Settings
            'maintenance_mode' => $this->getSetting('maintenance_mode', false),
            'debug_mode' => $this->getSetting('debug_mode', false),
            'cache_enabled' => $this->getSetting('cache_enabled', true),
            'log_level' => $this->getSetting('log_level', 'error'),
            'max_upload_size' => $this->getSetting('max_upload_size', '10'),
            'allowed_file_types' => $this->getSetting('allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx,zip'),
            'backup_enabled' => $this->getSetting('backup_enabled', true),
            'backup_frequency' => $this->getSetting('backup_frequency', 'weekly'),
        ];
    }

    /**
     * Lấy Setting
     */
    private function getSetting($key, $default = null)
    {
        return Cache::remember("setting_{$key}", 3600, function() use ($key, $default) {
            $setting = DB::table('settings')->where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Cập Nhật Setting
     */
    private function updateSetting($key, $value)
    {
        DB::table('settings')->updateOrInsert(
            ['key' => $key],
            ['value' => $value, 'updated_at' => now()]
        );

        Cache::forget("setting_{$key}");
    }

    /**
     * Lấy Thông Tin Hệ Thống
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => $this->getDatabaseVersion(),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'disk_free_space' => $this->formatBytes(disk_free_space('/')),
            'disk_total_space' => $this->formatBytes(disk_total_space('/')),
        ];
    }

    /**
     * Format Bytes
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Lấy Database Version
     */
    private function getDatabaseVersion()
    {
        try {
            $driver = config('database.default');
            $connection = config("database.connections.{$driver}.driver");

            switch ($connection) {
                case 'mysql':
                    return DB::select('SELECT VERSION() as version')[0]->version;
                case 'pgsql':
                    return DB::select('SELECT version()')[0]->version;
                case 'sqlite':
                    return DB::select('SELECT sqlite_version() as version')[0]->version;
                case 'sqlsrv':
                    return DB::select('SELECT @@VERSION as version')[0]->version;
                default:
                    return 'Unknown';
            }
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
}
