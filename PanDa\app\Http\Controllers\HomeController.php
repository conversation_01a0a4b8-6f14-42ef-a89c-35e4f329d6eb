<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Category;
use App\Models\Review;
use App\Models\User;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Hiển thị trang chủ landing page
     */
    public function index()
    {
        // Lấy khóa học nổi bật (có nhiều đánh giá tốt nhất)
        $featuredCourses = Course::with(['category', 'reviews'])
            ->where('status', 'published')
            ->withCount('reviews')
            ->withAvg('reviews', 'rating')
            ->orderByDesc('reviews_avg_rating')
            ->orderByDesc('reviews_count')
            ->take(6)
            ->get();

        // Lấy khóa học mới nhất
        $latestCourses = Course::with(['category'])
            ->where('status', 'published')
            ->orderByDesc('created_at')
            ->take(4)
            ->get();

        // L<PERSON>y danh mục phổ biến
        $popularCategories = Category::withCount('courses')
            ->orderByDesc('courses_count')
            ->take(8)
            ->get()
            ->filter(function($category) {
                return $category->courses_count > 0;
            });

        // Lấy đánh giá tích cực
        $testimonials = Review::with(['user', 'course'])
            ->where('rating', '>=', 4)
            ->where('is_approved', true)
            ->orderByDesc('created_at')
            ->take(6)
            ->get();

        // Thống kê tổng quan
        $stats = [
            'total_courses' => Course::where('status', 'published')->count(),
            'total_students' => User::where('role', 'user')->count(),
            'total_categories' => Category::count(),
            'total_reviews' => Review::where('is_approved', true)->count(),
        ];

        return view('landing', compact(
            'featuredCourses',
            'latestCourses', 
            'popularCategories',
            'testimonials',
            'stats'
        ));
    }
}
