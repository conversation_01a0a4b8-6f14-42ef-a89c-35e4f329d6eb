@extends('layouts.user')

@section('title', 'Chứng Chỉ Của Tôi')

@section('content')
<!-- <PERSON> Header -->
<div class="row mb-4">
  <div class="col-12">
    <div class="page-header bg-gradient-warning text-white rounded-4 p-4">
      <div class="row align-items-center">
        <div class="col-md-8">
          <h2 class="mb-2">
            <i class="fa fa-certificate me-3"></i>Chứng Chỉ Của Tôi
          </h2>
          <p class="mb-0 opacity-75">Tất cả chứng chỉ bạn đã đạt được từ các khóa học đã hoàn thành</p>
        </div>
        <div class="col-md-4 text-end">
          <div class="certificate-stats">
            <h3 class="mb-0">{{ auth()->user()->userCourses->where('completed', true)->count() }}</h3>
            <small>Chứng chỉ đã đạt được</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Certificates Grid -->
<div class="row">
  @php
    $completedCourses = auth()->user()->userCourses->where('completed', true);
  @endphp
  
  @if($completedCourses->count() > 0)
    @foreach($completedCourses as $userCourse)
    <div class="col-lg-6 col-xl-4 mb-4">
      <div class="certificate-card">
        <div class="certificate-header">
          <div class="certificate-badge">
            <i class="fa fa-award fa-2x"></i>
          </div>
          <div class="certificate-ribbon">
            <span>Chứng Chỉ</span>
          </div>
        </div>
        
        <div class="certificate-body">
          <div class="certificate-logo mb-3">
            @if($userCourse->course->thumbnail)
            <img src="{{ asset('storage/' . $userCourse->course->thumbnail) }}" alt="{{ $userCourse->course->title }}" class="course-thumbnail">
            @else
            <div class="course-placeholder">
              <i class="fa fa-graduation-cap fa-2x"></i>
            </div>
            @endif
          </div>
          
          <h5 class="certificate-title">{{ $userCourse->course->title }}</h5>
          <p class="certificate-category">{{ $userCourse->course->category->name }}</p>
          
          <div class="certificate-details">
            <div class="detail-item">
              <i class="fa fa-user text-primary me-2"></i>
              <span>{{ auth()->user()->name }}</span>
            </div>
            <div class="detail-item">
              <i class="fa fa-calendar text-success me-2"></i>
              <span>{{ $userCourse->completed_at ? $userCourse->completed_at->format('d/m/Y') : $userCourse->updated_at->format('d/m/Y') }}</span>
            </div>
            <div class="detail-item">
              <i class="fa fa-clock text-info me-2"></i>
              <span>{{ $userCourse->course->chapters->sum(function($chapter) { return $chapter->lessons->count(); }) }} bài học</span>
            </div>
          </div>
        </div>
        
        <div class="certificate-footer">
          <div class="row">
            <div class="col-6">
              <button class="btn btn-outline-primary btn-sm w-100" onclick="viewCertificate({{ $userCourse->id }})">
                <i class="fa fa-eye me-1"></i>Xem
              </button>
            </div>
            <div class="col-6">
              <button class="btn btn-primary btn-sm w-100" onclick="downloadCertificate({{ $userCourse->id }})">
                <i class="fa fa-download me-1"></i>Tải Xuống
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    @endforeach
  @else
    <!-- No Certificates -->
    <div class="col-12">
      <div class="no-certificates text-center py-5">
        <div class="no-cert-icon mb-4">
          <i class="fa fa-certificate fa-5x text-muted"></i>
        </div>
        <h3 class="text-muted mb-3">Chưa Có Chứng Chỉ Nào</h3>
        <p class="text-muted mb-4">Bạn chưa hoàn thành khóa học nào. Hãy hoàn thành khóa học để nhận chứng chỉ!</p>
        <div class="row justify-content-center">
          <div class="col-md-6">
            <div class="d-grid gap-2">
              <a href="{{ route('courses.index') }}" class="btn btn-primary btn-lg">
                <i class="fa fa-search me-2"></i>Khám Phá Khóa Học
              </a>
              <a href="{{ route('user.courses.index') }}" class="btn btn-outline-primary">
                <i class="fa fa-book me-2"></i>Khóa Học Của Tôi
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  @endif
</div>

<!-- Certificate Stats -->
@if($completedCourses->count() > 0)
<div class="row mt-5">
  <div class="col-12">
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-transparent border-0 pb-0">
        <h5 class="card-title mb-0">
          <i class="fa fa-chart-bar text-primary me-2"></i>Thống Kê Chứng Chỉ
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3 text-center mb-3">
            <div class="stat-item">
              <h3 class="text-primary">{{ $completedCourses->count() }}</h3>
              <p class="text-muted mb-0">Tổng Chứng Chỉ</p>
            </div>
          </div>
          <div class="col-md-3 text-center mb-3">
            <div class="stat-item">
              <h3 class="text-success">{{ $completedCourses->where('created_at', '>=', now()->startOfYear())->count() }}</h3>
              <p class="text-muted mb-0">Năm Nay</p>
            </div>
          </div>
          <div class="col-md-3 text-center mb-3">
            <div class="stat-item">
              <h3 class="text-warning">{{ $completedCourses->groupBy('course.category.name')->count() }}</h3>
              <p class="text-muted mb-0">Danh Mục</p>
            </div>
          </div>
          <div class="col-md-3 text-center mb-3">
            <div class="stat-item">
              <h3 class="text-info">{{ $completedCourses->where('created_at', '>=', now()->startOfMonth())->count() }}</h3>
              <p class="text-muted mb-0">Tháng Này</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Categories Breakdown -->
<div class="row mt-4">
  <div class="col-12">
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-transparent border-0 pb-0">
        <h5 class="card-title mb-0">
          <i class="fa fa-tags text-success me-2"></i>Chứng Chỉ Theo Danh Mục
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          @foreach($completedCourses->groupBy('course.category.name') as $categoryName => $courses)
          <div class="col-md-6 col-lg-4 mb-3">
            <div class="category-item">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h6 class="mb-1">{{ $categoryName }}</h6>
                  <small class="text-muted">{{ $courses->count() }} chứng chỉ</small>
                </div>
                <div class="category-badge">
                  {{ $courses->count() }}
                </div>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
</div>
@endif

<!-- Certificate Modal -->
<div class="modal fade" id="certificateModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header border-0">
        <h5 class="modal-title">Chứng Chỉ Hoàn Thành Khóa Học</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div id="certificatePreview" class="text-center">
          <!-- Certificate preview will be loaded here -->
        </div>
      </div>
      <div class="modal-footer border-0">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="button" class="btn btn-primary" id="downloadCertBtn">
          <i class="fa fa-download me-2"></i>Tải Xuống
        </button>
      </div>
    </div>
  </div>
</div>

<style>
.page-header {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  border-radius: 20px !important;
}

.certificate-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  position: relative;
  border: 3px solid #f8f9fa;
}

.certificate-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0,0,0,0.15);
  border-color: #ffc107;
}

.certificate-header {
  background: linear-gradient(135deg, #ffc107, #ff9800);
  padding: 20px;
  text-align: center;
  position: relative;
}

.certificate-badge {
  color: white;
  margin-bottom: 10px;
}

.certificate-ribbon {
  position: absolute;
  top: 15px;
  right: -10px;
  background: #dc3545;
  color: white;
  padding: 5px 15px;
  font-size: 0.8rem;
  font-weight: bold;
  transform: rotate(15deg);
  border-radius: 5px;
}

.certificate-body {
  padding: 25px;
  text-align: center;
}

.course-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #ffc107;
}

.course-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  border: 4px solid #ffc107;
}

.certificate-title {
  color: #2c3e50;
  font-weight: 700;
  margin: 15px 0 5px;
}

.certificate-category {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.certificate-details {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.certificate-footer {
  padding: 20px;
  background: #f8f9fa;
}

.no-certificates {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 20px;
  margin: 50px 0;
}

.no-cert-icon {
  opacity: 0.3;
}

.stat-item {
  padding: 20px;
  border-radius: 15px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.category-item {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 15px;
  transition: all 0.3s ease;
}

.category-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.category-badge {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.btn {
  border-radius: 10px;
  font-weight: 600;
}

.card {
  border-radius: 15px;
}
</style>

<script>
function viewCertificate(userCourseId) {
  // Show certificate preview in modal
  $('#certificateModal').modal('show');
  
  // Load certificate preview
  $('#certificatePreview').html(`
    <div class="certificate-preview">
      <div class="cert-border">
        <div class="cert-content">
          <h2 class="cert-title">CHỨNG CHỈ HOÀN THÀNH</h2>
          <p class="cert-subtitle">Chứng nhận rằng</p>
          <h3 class="cert-name">{{ auth()->user()->name }}</h3>
          <p class="cert-text">đã hoàn thành xuất sắc khóa học</p>
          <h4 class="cert-course">Tên Khóa Học</h4>
          <div class="cert-footer">
            <div class="cert-date">Ngày: ${new Date().toLocaleDateString('vi-VN')}</div>
            <div class="cert-signature">PanDa Learning</div>
          </div>
        </div>
      </div>
    </div>
  `);
  
  // Set download button action
  $('#downloadCertBtn').attr('onclick', `downloadCertificate(${userCourseId})`);
}

function downloadCertificate(userCourseId) {
  // Simulate certificate download
  const link = document.createElement('a');
  link.href = `/user/certificates/${userCourseId}/download`;
  link.download = `certificate-${userCourseId}.pdf`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Show success message
  alert('Chứng chỉ đang được tải xuống!');
}
</script>
@endsection
