@extends('layouts.user')

@section('title', 'Đơn Hàng Của Tôi')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Đơn Hàng Của Tôi</h4>
        <p class="text-muted mb-0">Theo Dõi Trạng Thái Các Đơn Hàng</p>
      </div>
      <div>
        <a href="{{ route('cart.index') }}" class="btn btn-primary">
          <i class="fa fa-shopping-cart me-2"></i>Giỏ Hàng
        </a>
      </div>
    </div>
  </div>
</div>

@if($orders->count() > 0)
<div class="row">
  @foreach($orders as $order)
  <div class="col-12 mb-4">
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h6 class="mb-0">
              <i class="fa fa-code me-2"></i>{{ $order->order_number }}
            </h6>
            <small class="text-muted">{{ $order->created_at->format('d/m/Y H:i') }}</small>
          </div>
          <div>
            @if($order->status == 'pending')
            <span class="badge bg-warning">{{ $order->status_text }}</span>
            @elseif($order->status == 'approved')
            <span class="badge bg-success">{{ $order->status_text }}</span>
            @elseif($order->status == 'rejected')
            <span class="badge bg-danger">{{ $order->status_text }}</span>
            @else
            <span class="badge bg-info">{{ $order->status_text }}</span>
            @endif
          </div>
        </div>
      </div>
      
      <div class="card-body">
        <div class="row">
          <div class="col-md-8">
            <h6>Khóa Học ({{ $order->items->count() }})</h6>
            <div class="list-group list-group-flush">
              @foreach($order->items as $item)
              <div class="list-group-item px-0">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h6 class="mb-1">{{ $item->course_title }}</h6>
                    @if($item->note)
                    <small class="text-muted">Ghi chú: {{ $item->note }}</small>
                    @endif
                  </div>
                  <div class="text-end">
                    <span class="fw-bold text-success">
                      @if($item->course_price > 0)
                      {{ number_format($item->course_price, 0, ',', '.') }}đ
                      @else
                      Miễn Phí
                      @endif
                    </span>
                  </div>
                </div>
              </div>
              @endforeach
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="card bg-light">
              <div class="card-body">
                <div class="mb-2">
                  <small class="text-muted">Tổng Tiền:</small>
                  <div class="h5 text-success mb-0">{{ number_format($order->total_amount, 0, ',', '.') }}đ</div>
                </div>
                
                <div class="mb-2">
                  <small class="text-muted">Thanh Toán:</small>
                  <div class="fw-bold">{{ $order->payment_method_text }}</div>
                </div>
                
                @if($order->note)
                <div class="mb-2">
                  <small class="text-muted">Ghi Chú:</small>
                  <div class="small">{{ $order->note }}</div>
                </div>
                @endif
                
                @if($order->admin_note)
                <div class="mb-2">
                  <small class="text-muted">Phản Hồi Admin:</small>
                  <div class="small text-primary">{{ $order->admin_note }}</div>
                </div>
                @endif
                
                @if($order->approved_at)
                <div class="mb-2">
                  <small class="text-muted">Ngày Duyệt:</small>
                  <div class="small">{{ $order->approved_at->format('d/m/Y H:i') }}</div>
                </div>
                @endif
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            @if($order->status == 'pending')
            <small class="text-warning">
              <i class="fa fa-clock me-1"></i>Đang chờ admin xem xét
            </small>
            @elseif($order->status == 'approved')
            <small class="text-success">
              <i class="fa fa-check-circle me-1"></i>Đơn hàng đã được duyệt
            </small>
            @elseif($order->status == 'rejected')
            <small class="text-danger">
              <i class="fa fa-times-circle me-1"></i>Đơn hàng bị từ chối
            </small>
            @else
            <small class="text-info">
              <i class="fa fa-check-double me-1"></i>Đơn hàng đã hoàn thành
            </small>
            @endif
          </div>
          
          <div>
            <a href="{{ route('orders.show', $order->id) }}" class="btn btn-outline-primary btn-sm">
              <i class="fa fa-eye me-1"></i>Chi Tiết
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  @endforeach
</div>

<!-- Phân Trang -->
@if($orders->hasPages())
<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      {{ $orders->links() }}
    </div>
  </div>
</div>
@endif

@else
<!-- Trống -->
<div class="row">
  <div class="col-12">
    <div class="text-center py-5">
      <i class="fa fa-shopping-bag fa-4x text-muted mb-4"></i>
      <h4 class="text-muted mb-3">Chưa Có Đơn Hàng Nào</h4>
      <p class="text-muted mb-4">Bạn chưa có đơn hàng nào. Hãy thêm khóa học vào giỏ hàng và gửi yêu cầu mua.</p>
      <div class="d-flex gap-2 justify-content-center">
        <a href="{{ route('courses.index') }}" class="btn btn-primary">
          <i class="fa fa-book me-2"></i>Xem Khóa Học
        </a>
        <a href="{{ route('cart.index') }}" class="btn btn-outline-primary">
          <i class="fa fa-shopping-cart me-2"></i>Giỏ Hàng
        </a>
      </div>
    </div>
  </div>
</div>
@endif

<!-- Thống Kê -->
<div class="row mt-4">
  <div class="col-md-3">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ auth()->user()->orders()->count() }}</h4>
        <small>Tổng Đơn Hàng</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-warning text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ auth()->user()->orders()->pending()->count() }}</h4>
        <small>Chờ Duyệt</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ auth()->user()->orders()->approved()->count() }}</h4>
        <small>Đã Duyệt</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ number_format(auth()->user()->orders()->sum('total_amount'), 0, ',', '.') }}đ</h4>
        <small>Tổng Giá Trị</small>
      </div>
    </div>
  </div>
</div>
@endsection
