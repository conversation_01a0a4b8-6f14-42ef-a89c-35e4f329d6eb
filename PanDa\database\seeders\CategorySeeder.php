<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Lập Trình Web',
                'slug' => 'lap-trinh-web',
                'description' => '<PERSON>ọ<PERSON> lập trình web từ cơ bản đến nâng cao với HTML, CSS, JavaScript, PHP, Laravel',
                'is_active' => true,
            ],
            [
                'name' => 'Mobile Development',
                'slug' => 'mobile-development',
                'description' => 'Phát triển ứng dụng di động với React Native, Flutter, Android, iOS',
                'is_active' => true,
            ],
            [
                'name' => 'Data Science',
                'slug' => 'data-science',
                'description' => 'K<PERSON>a học dữ li<PERSON>u, Machine Learning, AI với Python, R, TensorFlow',
                'is_active' => true,
            ],
            [
                'name' => 'DevOps',
                'slug' => 'devops',
                'description' => 'DevOps, CI/CD, Docker, Kubernetes, AWS, Azure',
                'is_active' => true,
            ],
            [
                'name' => 'UI/UX Design',
                'slug' => 'ui-ux-design',
                'description' => 'Thiết kế giao diện người dùng và trải nghiệm người dùng',
                'is_active' => true,
            ],
            [
                'name' => 'Digital Marketing',
                'slug' => 'digital-marketing',
                'description' => 'Marketing số, SEO, SEM, Social Media Marketing',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }
    }
}
