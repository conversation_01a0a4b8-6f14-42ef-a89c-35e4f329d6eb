@extends('layouts.admin')

@section('title', 'Cài Đặt Hệ Thống')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Cài Đặt Hệ T<PERSON>ống</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Cài Đặt</a></li>
            <li class="breadcrumb-item active">Hệ Thống</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- System Settings -->
    <div class="col-lg-8">
      <form action="{{ route('admin.settings.system.update') }}" method="POST">
        @csrf
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-cogs me-2"></i>Cài Đặt Hệ Thống
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="maintenance_mode" value="1" 
                         {{ old('maintenance_mode', $settings['maintenance_mode']) ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Chế Độ Bảo Trì</strong>
                    <small class="d-block text-muted">Tạm thời đóng website cho người dùng</small>
                  </label>
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="debug_mode" value="1" 
                         {{ old('debug_mode', $settings['debug_mode']) ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Debug Mode</strong>
                    <small class="d-block text-muted">Hiển thị lỗi chi tiết (chỉ dùng khi phát triển)</small>
                  </label>
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="cache_enabled" value="1" 
                         {{ old('cache_enabled', $settings['cache_enabled']) ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Bật Cache</strong>
                    <small class="d-block text-muted">Tăng tốc độ website</small>
                  </label>
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" name="backup_enabled" value="1" 
                         {{ old('backup_enabled', $settings['backup_enabled']) ? 'checked' : '' }}>
                  <label class="form-check-label">
                    <strong>Tự Động Backup</strong>
                    <small class="d-block text-muted">Backup database định kỳ</small>
                  </label>
                </div>
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Log Level</label>
                <select class="form-select" name="log_level">
                  <option value="emergency" {{ old('log_level', $settings['log_level']) == 'emergency' ? 'selected' : '' }}>Emergency</option>
                  <option value="alert" {{ old('log_level', $settings['log_level']) == 'alert' ? 'selected' : '' }}>Alert</option>
                  <option value="critical" {{ old('log_level', $settings['log_level']) == 'critical' ? 'selected' : '' }}>Critical</option>
                  <option value="error" {{ old('log_level', $settings['log_level']) == 'error' ? 'selected' : '' }}>Error</option>
                  <option value="warning" {{ old('log_level', $settings['log_level']) == 'warning' ? 'selected' : '' }}>Warning</option>
                  <option value="notice" {{ old('log_level', $settings['log_level']) == 'notice' ? 'selected' : '' }}>Notice</option>
                  <option value="info" {{ old('log_level', $settings['log_level']) == 'info' ? 'selected' : '' }}>Info</option>
                  <option value="debug" {{ old('log_level', $settings['log_level']) == 'debug' ? 'selected' : '' }}>Debug</option>
                </select>
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Tần Suất Backup</label>
                <select class="form-select" name="backup_frequency">
                  <option value="daily" {{ old('backup_frequency', $settings['backup_frequency']) == 'daily' ? 'selected' : '' }}>Hàng Ngày</option>
                  <option value="weekly" {{ old('backup_frequency', $settings['backup_frequency']) == 'weekly' ? 'selected' : '' }}>Hàng Tuần</option>
                  <option value="monthly" {{ old('backup_frequency', $settings['backup_frequency']) == 'monthly' ? 'selected' : '' }}>Hàng Tháng</option>
                </select>
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Kích Thước Upload Tối Đa (MB)</label>
                <input type="number" class="form-control" name="max_upload_size" 
                       value="{{ old('max_upload_size', $settings['max_upload_size']) }}" min="1" max="100">
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Định Dạng File Cho Phép</label>
                <input type="text" class="form-control" name="allowed_file_types" 
                       value="{{ old('allowed_file_types', $settings['allowed_file_types']) }}" 
                       placeholder="jpg,png,pdf,doc,zip">
                <small class="text-muted">Cách nhau bằng dấu phẩy</small>
              </div>
            </div>
          </div>
          
          <div class="card-footer">
            <button type="submit" class="btn btn-primary">
              <i class="fa fa-save me-2"></i>Lưu Cài Đặt
            </button>
          </div>
        </div>
      </form>

      <!-- System Actions -->
      <div class="card mt-4">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fa fa-tools me-2"></i>Thao Tác Hệ Thống
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3 mb-3">
              <form action="{{ route('admin.settings.clear-cache') }}" method="POST">
                @csrf
                <button type="submit" class="btn btn-outline-primary w-100" onclick="return confirm('Bạn có chắc muốn xóa cache?')">
                  <i class="fa fa-trash me-2"></i>Xóa Cache
                </button>
              </form>
            </div>
            <div class="col-md-3 mb-3">
              <form action="{{ route('admin.settings.optimize') }}" method="POST">
                @csrf
                <button type="submit" class="btn btn-outline-success w-100">
                  <i class="fa fa-rocket me-2"></i>Tối Ưu
                </button>
              </form>
            </div>
            <div class="col-md-3 mb-3">
              <form action="{{ route('admin.settings.backup') }}" method="POST">
                @csrf
                <button type="submit" class="btn btn-outline-warning w-100">
                  <i class="fa fa-download me-2"></i>Backup DB
                </button>
              </form>
            </div>
            <div class="col-md-3 mb-3">
              <button type="button" class="btn btn-outline-info w-100" onclick="checkSystemHealth()">
                <i class="fa fa-heartbeat me-2"></i>Kiểm Tra
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- System Information -->
    <div class="col-lg-4">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fa fa-info-circle me-2"></i>Thông Tin Hệ Thống
          </h5>
        </div>
        <div class="card-body">
          <table class="table table-borderless table-sm">
            <tr>
              <td><strong>PHP Version:</strong></td>
              <td>{{ $systemInfo['php_version'] }}</td>
            </tr>
            <tr>
              <td><strong>Laravel Version:</strong></td>
              <td>{{ $systemInfo['laravel_version'] }}</td>
            </tr>
            <tr>
              <td><strong>Database:</strong></td>
              <td>{{ $systemInfo['database_version'] }}</td>
            </tr>
            <tr>
              <td><strong>Server:</strong></td>
              <td>{{ $systemInfo['server_software'] }}</td>
            </tr>
            <tr>
              <td><strong>Memory Limit:</strong></td>
              <td>{{ $systemInfo['memory_limit'] }}</td>
            </tr>
            <tr>
              <td><strong>Max Execution:</strong></td>
              <td>{{ $systemInfo['max_execution_time'] }}s</td>
            </tr>
            <tr>
              <td><strong>Upload Max:</strong></td>
              <td>{{ $systemInfo['upload_max_filesize'] }}</td>
            </tr>
            <tr>
              <td><strong>Post Max:</strong></td>
              <td>{{ $systemInfo['post_max_size'] }}</td>
            </tr>
            <tr>
              <td><strong>Disk Free:</strong></td>
              <td>{{ $systemInfo['disk_free_space'] }}</td>
            </tr>
            <tr>
              <td><strong>Disk Total:</strong></td>
              <td>{{ $systemInfo['disk_total_space'] }}</td>
            </tr>
          </table>
        </div>
      </div>

      <!-- System Health -->
      <div class="card mt-4">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fa fa-heartbeat me-2"></i>Tình Trạng Hệ Thống
          </h5>
        </div>
        <div class="card-body">
          <div class="health-item d-flex justify-content-between align-items-center mb-3">
            <span>Database Connection</span>
            <span class="badge bg-success">OK</span>
          </div>
          <div class="health-item d-flex justify-content-between align-items-center mb-3">
            <span>Storage Writable</span>
            <span class="badge bg-success">OK</span>
          </div>
          <div class="health-item d-flex justify-content-between align-items-center mb-3">
            <span>Cache Working</span>
            <span class="badge bg-{{ $settings['cache_enabled'] ? 'success' : 'warning' }}">
              {{ $settings['cache_enabled'] ? 'OK' : 'Disabled' }}
            </span>
          </div>
          <div class="health-item d-flex justify-content-between align-items-center mb-3">
            <span>Debug Mode</span>
            <span class="badge bg-{{ $settings['debug_mode'] ? 'warning' : 'success' }}">
              {{ $settings['debug_mode'] ? 'ON' : 'OFF' }}
            </span>
          </div>
          <div class="health-item d-flex justify-content-between align-items-center">
            <span>Maintenance Mode</span>
            <span class="badge bg-{{ $settings['maintenance_mode'] ? 'danger' : 'success' }}">
              {{ $settings['maintenance_mode'] ? 'ON' : 'OFF' }}
            </span>
          </div>
        </div>
      </div>

      <!-- Recent Logs -->
      <div class="card mt-4">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fa fa-file-alt me-2"></i>Logs Gần Đây
          </h5>
        </div>
        <div class="card-body">
          <div class="log-item mb-2">
            <div class="d-flex justify-content-between">
              <span class="badge bg-success">INFO</span>
              <small class="text-muted">2 phút trước</small>
            </div>
            <p class="small mb-0">User logged in successfully</p>
          </div>
          <div class="log-item mb-2">
            <div class="d-flex justify-content-between">
              <span class="badge bg-warning">WARNING</span>
              <small class="text-muted">1 giờ trước</small>
            </div>
            <p class="small mb-0">High memory usage detected</p>
          </div>
          <div class="log-item mb-2">
            <div class="d-flex justify-content-between">
              <span class="badge bg-info">INFO</span>
              <small class="text-muted">3 giờ trước</small>
            </div>
            <p class="small mb-0">Cache cleared successfully</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Back Button -->
  <div class="row mt-4">
    <div class="col-12">
      <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
        <i class="fa fa-arrow-left me-2"></i>Quay Lại Cài Đặt
      </a>
    </div>
  </div>
</div>

<script>
function checkSystemHealth() {
  const btn = event.target;
  const originalText = btn.innerHTML;
  btn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Đang kiểm tra...';
  btn.disabled = true;
  
  // Simulate health check
  setTimeout(() => {
    btn.innerHTML = originalText;
    btn.disabled = false;
    alert('Hệ thống hoạt động bình thường!');
  }, 2000);
}
</script>
@endsection
