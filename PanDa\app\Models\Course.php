<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Course extends Model
{
    protected $fillable = [
        'title',
        'slug',
        'description',
        'image_url',
        'price',
        'level',
        'category_id',
        'status',
        'is_completed',
        'duration_hours',
        'requirements',
        'what_you_learn',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'duration_hours' => 'integer',
        'is_completed' => 'boolean',
    ];

    /**
     * Quan Hệ Với Category
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Quan Hệ Với Chapters
     */
    public function chapters(): HasMany
    {
        return $this->hasMany(Chapter::class)->orderBy('position');
    }

    /**
     * Quan Hệ Với Lessons (Thông Qua Chapters)
     */
    public function lessons()
    {
        return $this->hasManyThrough(
            Lesson::class,
            Chapter::class,
            'course_id', // Foreign key on chapters table
            'chapter_id', // Foreign key on lessons table
            'id', // Local key on courses table
            'id' // Local key on chapters table
        );
    }



    /**
     * Scope Chỉ Lấy Khóa Học Đã Xuất Bản
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope Theo Level
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope Theo Category
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Lấy Tổng Số Bài Học
     */
    public function getTotalLessonsAttribute()
    {
        return $this->chapters->sum(function ($chapter) {
            return $chapter->lessons->count();
        });
    }

    /**
     * Lấy Tổng Thời Lượng
     */
    public function getTotalDurationAttribute()
    {
        return $this->chapters->sum(function ($chapter) {
            return $chapter->lessons->sum('duration_minutes');
        });
    }

    /**
     * Quan Hệ Với Reviews
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Quan Hệ Với Reviews Đã Duyệt
     */
    public function approvedReviews()
    {
        return $this->hasMany(Review::class)->approved();
    }

    /**
     * Accessor Cho Average Rating
     */
    public function getAverageRatingAttribute()
    {
        return $this->approvedReviews()->avg('rating') ?: 0;
    }

    /**
     * Accessor Cho Total Reviews
     */
    public function getTotalReviewsAttribute()
    {
        return $this->approvedReviews()->count();
    }

    /**
     * Accessor Cho Stars Display
     */
    public function getStarsAttribute()
    {
        $rating = round($this->average_rating);
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $rating) {
                $stars .= '<i class="fa fa-star text-warning"></i>';
            } else {
                $stars .= '<i class="fa fa-star text-muted"></i>';
            }
        }
        return $stars;
    }

    /**
     * Quan Hệ Với User Courses
     */
    public function userCourses()
    {
        return $this->hasMany(UserCourse::class);
    }


}
