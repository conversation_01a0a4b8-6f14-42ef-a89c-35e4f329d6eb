<div class="comment-item mb-3" data-comment-id="{{ $comment->id }}">
  <div class="d-flex">
    <!-- Avatar -->
    <div class="flex-shrink-0 me-3">
      @if($comment->user->avatar)
      <img src="{{ asset('storage/' . $comment->user->avatar) }}" class="rounded-circle" width="40" height="40" alt="{{ $comment->user->name }}">
      @else
      <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
        <span class="text-white fw-bold">{{ strtoupper(substr($comment->user->name, 0, 1)) }}</span>
      </div>
      @endif
    </div>

    <!-- Comment Content -->
    <div class="flex-grow-1">
      <div class="bg-light rounded p-3">
        <!-- User Info -->
        <div class="d-flex justify-content-between align-items-start mb-2">
          <div>
            <strong class="text-primary">{{ $comment->user->name }}</strong>
            <small class="text-muted ms-2">{{ $comment->time_ago }}</small>
          </div>
          
          <!-- Actions -->
          @if(auth()->check() && auth()->id() === $comment->user_id)
          <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="editComment({{ $comment->id }})">
                <i class="fa fa-edit me-2"></i>Chỉnh Sửa
              </a></li>
              <li><a class="dropdown-item text-danger" href="#" onclick="deleteComment({{ $comment->id }})">
                <i class="fa fa-trash me-2"></i>Xóa
              </a></li>
            </ul>
          </div>
          @endif
        </div>

        <!-- Comment Text -->
        <div class="comment-content">
          <p class="mb-0">{{ $comment->content }}</p>
        </div>

        <!-- Edit Form (Hidden) -->
        <div class="edit-form d-none">
          <form onsubmit="updateComment(event, {{ $comment->id }})">
            <div class="mb-2">
              <textarea class="form-control" name="content" rows="3" required>{{ $comment->content }}</textarea>
            </div>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary btn-sm">
                <i class="fa fa-save me-1"></i>Lưu
              </button>
              <button type="button" class="btn btn-secondary btn-sm" onclick="cancelEdit({{ $comment->id }})">
                <i class="fa fa-times me-1"></i>Hủy
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Reply Button -->
      @auth
      <div class="mt-2">
        <button class="btn btn-link btn-sm text-muted p-0" onclick="toggleReplyForm({{ $comment->id }})">
          <i class="fa fa-reply me-1"></i>Trả Lời
        </button>
      </div>
      @endauth

      <!-- Reply Form (Hidden) -->
      @auth
      <div class="reply-form mt-2 d-none" id="reply-form-{{ $comment->id }}">
        <form onsubmit="submitReply(event, {{ $comment->id }})">
          @csrf
          <div class="d-flex gap-2">
            <div class="flex-shrink-0">
              @if(auth()->user()->avatar)
              <img src="{{ asset('storage/' . auth()->user()->avatar) }}" class="rounded-circle" width="32" height="32" alt="{{ auth()->user()->name }}">
              @else
              <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                <span class="text-white fw-bold">{{ strtoupper(substr(auth()->user()->name, 0, 1)) }}</span>
              </div>
              @endif
            </div>
            <div class="flex-grow-1">
              <textarea class="form-control mb-2" name="content" rows="2" placeholder="Viết Trả Lời..." required></textarea>
              <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary btn-sm">
                  <i class="fa fa-paper-plane me-1"></i>Gửi
                </button>
                <button type="button" class="btn btn-secondary btn-sm" onclick="toggleReplyForm({{ $comment->id }})">
                  <i class="fa fa-times me-1"></i>Hủy
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
      @endauth

      <!-- Replies -->
      @if($comment->replies->count() > 0)
      <div class="replies mt-3 ms-4">
        @foreach($comment->replies as $reply)
        @include('components.comment-item', ['comment' => $reply])
        @endforeach
      </div>
      @endif
    </div>
  </div>
</div>
