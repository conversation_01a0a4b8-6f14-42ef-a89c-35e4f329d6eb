<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .success-icon {
            font-size: 48px;
            color: #28a745;
            margin-bottom: 15px;
        }
        .title {
            color: #28a745;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .order-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .order-number {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .course-list {
            margin: 20px 0;
        }
        .course-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            background-color: #ffffff;
        }
        .course-title {
            font-weight: bold;
            color: #333;
        }
        .course-price {
            color: #28a745;
            font-weight: bold;
        }
        .total-section {
            background-color: #007bff;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .total-amount {
            font-size: 24px;
            font-weight: bold;
        }
        .next-steps {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .contact-info {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .email-container {
                padding: 20px;
            }
            .course-item {
                flex-direction: column;
                align-items: flex-start;
            }
            .course-price {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">🐼 PanDa Learning</div>
            <div class="success-icon"></div>
            <h1 class="title">Đơn Hàng Được Duyệt!</h1>
        </div>

        <p>Xin Chào <strong>{{ $user->name }}</strong>,</p>

        <p>Chúng Tôi Vui Mừng Thông Báo Rằng Đơn Hàng Của Bạn Đã Được Duyệt Thành Công!</p>

        <div class="order-info">
            <div class="order-number">📋 Mã Đơn Hàng: {{ $order->order_number }}</div>
            <p><strong> Ngày Đặt:</strong> {{ $order->created_at->format('d/m/Y H:i') }}</p>
            <p><strong> Ngày Duyệt:</strong> {{ $order->approved_at->format('d/m/Y H:i') }}</p>
            @if($order->admin_note)
            <p><strong> Ghi Chú:</strong> {{ $order->admin_note }}</p>
            @endif
        </div>

        <div class="course-list">
            <h3> Khóa Học Đã Mua:</h3>
            @foreach($items as $item)
            <div class="course-item">
                <div>
                    <div class="course-title">{{ $item->course->title }}</div>
                    <small>{{ $item->course->category->name }}</small>
                </div>
                <div class="course-price">
                    @if($item->price > 0)
                        {{ number_format($item->price) }}đ
                    @else
                        Miễn Phí
                    @endif
                </div>
            </div>
            @endforeach
        </div>

        <div class="total-section">
            <div>Tổng Thanh Toán</div>
            <div class="total-amount">{{ number_format($order->total_amount) }}đ</div>
        </div>

        <div class="next-steps">
            <h3> Bước Tiếp Theo:</h3>
            <ul>
                <li>Bạn Có Thể Bắt Đầu Học Ngay Bây Giờ</li>
                <li>Truy Cập Vào Trang Khóa Học Của Tôi</li>
                <li>Xem Tất Cả Bài Học Và Tài Liệu</li>
                <li>Tham Gia Cộng Đồng Học Tập</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ url('/user/dashboard') }}" class="btn btn-success">
                 Bắt Đầu Học Ngay
            </a>
            <a href="{{ url('/courses') }}" class="btn">
                 Xem Thêm Khóa Học
            </a>
        </div>

        <div class="contact-info">
            <h4> Cần Hỗ Trợ?</h4>
            <p>Nếu Bạn Có Bất Kỳ Câu Hỏi Nào, Đừng Ngần Ngại Liên Hệ Với Chúng Tôi:</p>
            <p>
                📧 Email: <EMAIL><br>
                📱 Hotline: 1900-xxxx<br>
                🌐 Website: {{ url('/') }}
            </p>
        </div>

        <div class="footer">
            <p>Cảm Ơn Bạn Đã Tin Tương Và Lựa Chọn PanDa Learning!</p>
            <p>Chúc Bạn Học Tập Hiệu Quả Và Đạt Được Mục Tiêu Đề Ra! </p>
            <hr>
            <p>
                <small>
                    Email Này Được Gửi Tự Động. Vui Lòng Không Trả Lời Email Này.<br>
                    © {{ date('Y') }} PanDa Learning. All Rights Reserved.
                </small>
            </p>
        </div>
    </div>
</body>
</html>
