@extends('layouts.admin')

@section('title', 'Cài Đặt Email')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Cài Đặt Email</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.settings.index') }}">Cài Đặt</a></li>
            <li class="breadcrumb-item active">Email</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <form action="{{ route('admin.settings.email.update') }}" method="POST">
    @csrf
    <div class="row">
      <!-- <PERSON><PERSON>u Hình SMTP -->
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-server me-2"></i>Cấu Hình Máy Chủ Email
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">Driver Email <span class="text-danger">*</span></label>
                <select class="form-select @error('mail_driver') is-invalid @enderror" name="mail_driver" required>
                  <option value="smtp" {{ old('mail_driver', $settings['mail_driver']) == 'smtp' ? 'selected' : '' }}>SMTP</option>
                  <option value="sendmail" {{ old('mail_driver', $settings['mail_driver']) == 'sendmail' ? 'selected' : '' }}>Sendmail</option>
                  <option value="mailgun" {{ old('mail_driver', $settings['mail_driver']) == 'mailgun' ? 'selected' : '' }}>Mailgun</option>
                  <option value="ses" {{ old('mail_driver', $settings['mail_driver']) == 'ses' ? 'selected' : '' }}>Amazon SES</option>
                  <option value="postmark" {{ old('mail_driver', $settings['mail_driver']) == 'postmark' ? 'selected' : '' }}>Postmark</option>
                </select>
                @error('mail_driver')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Máy Chủ SMTP <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('mail_host') is-invalid @enderror" 
                       name="mail_host" value="{{ old('mail_host', $settings['mail_host']) }}" 
                       placeholder="smtp.gmail.com" required>
                @error('mail_host')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Cổng SMTP <span class="text-danger">*</span></label>
                <input type="number" class="form-control @error('mail_port') is-invalid @enderror" 
                       name="mail_port" value="{{ old('mail_port', $settings['mail_port']) }}" 
                       placeholder="587" required>
                @error('mail_port')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Mã Hóa</label>
                <select class="form-select @error('mail_encryption') is-invalid @enderror" name="mail_encryption">
                  <option value="">Không Mã Hóa</option>
                  <option value="tls" {{ old('mail_encryption', $settings['mail_encryption']) == 'tls' ? 'selected' : '' }}>TLS</option>
                  <option value="ssl" {{ old('mail_encryption', $settings['mail_encryption']) == 'ssl' ? 'selected' : '' }}>SSL</option>
                </select>
                @error('mail_encryption')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Tên Đăng Nhập <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('mail_username') is-invalid @enderror" 
                       name="mail_username" value="{{ old('mail_username', $settings['mail_username']) }}" required>
                @error('mail_username')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Mật Khẩu <span class="text-danger">*</span></label>
                <input type="password" class="form-control @error('mail_password') is-invalid @enderror" 
                       name="mail_password" value="{{ old('mail_password', $settings['mail_password']) }}" required>
                @error('mail_password')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
          </div>
        </div>

        <!-- Thông Tin Người Gửi -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-user me-2"></i>Thông Tin Người Gửi
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">Email Gửi <span class="text-danger">*</span></label>
                <input type="email" class="form-control @error('mail_from_address') is-invalid @enderror" 
                       name="mail_from_address" value="{{ old('mail_from_address', $settings['mail_from_address']) }}" required>
                @error('mail_from_address')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
              
              <div class="col-md-6 mb-3">
                <label class="form-label">Tên Người Gửi <span class="text-danger">*</span></label>
                <input type="text" class="form-control @error('mail_from_name') is-invalid @enderror" 
                       name="mail_from_name" value="{{ old('mail_from_name', $settings['mail_from_name']) }}" required>
                @error('mail_from_name')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Hướng Dẫn & Test -->
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-info-circle me-2"></i>Hướng Dẫn Cấu Hình
            </h5>
          </div>
          <div class="card-body">
            <div class="accordion" id="emailGuide">
              <!-- Gmail -->
              <div class="accordion-item">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gmail">
                    <i class="fab fa-google me-2"></i>Gmail
                  </button>
                </h2>
                <div id="gmail" class="accordion-collapse collapse" data-bs-parent="#emailGuide">
                  <div class="accordion-body">
                    <strong>Cấu hình Gmail:</strong>
                    <ul class="small">
                      <li>Host: smtp.gmail.com</li>
                      <li>Port: 587</li>
                      <li>Encryption: TLS</li>
                      <li>Username: <EMAIL></li>
                      <li>Password: App Password</li>
                    </ul>
                    <div class="alert alert-warning small">
                      <strong>Lưu ý:</strong> Cần bật 2FA và tạo App Password
                    </div>
                  </div>
                </div>
              </div>

              <!-- Outlook -->
              <div class="accordion-item">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#outlook">
                    <i class="fab fa-microsoft me-2"></i>Outlook
                  </button>
                </h2>
                <div id="outlook" class="accordion-collapse collapse" data-bs-parent="#emailGuide">
                  <div class="accordion-body">
                    <strong>Cấu hình Outlook:</strong>
                    <ul class="small">
                      <li>Host: smtp-mail.outlook.com</li>
                      <li>Port: 587</li>
                      <li>Encryption: TLS</li>
                      <li>Username: <EMAIL></li>
                      <li>Password: Mật khẩu tài khoản</li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Yahoo -->
              <div class="accordion-item">
                <h2 class="accordion-header">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#yahoo">
                    <i class="fab fa-yahoo me-2"></i>Yahoo
                  </button>
                </h2>
                <div id="yahoo" class="accordion-collapse collapse" data-bs-parent="#emailGuide">
                  <div class="accordion-body">
                    <strong>Cấu hình Yahoo:</strong>
                    <ul class="small">
                      <li>Host: smtp.mail.yahoo.com</li>
                      <li>Port: 587</li>
                      <li>Encryption: TLS</li>
                      <li>Username: <EMAIL></li>
                      <li>Password: App Password</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Test Email -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-paper-plane me-2"></i>Test Email
            </h5>
          </div>
          <div class="card-body">
            <p class="text-muted small">Gửi email test để kiểm tra cấu hình</p>
            <div class="mb-3">
              <input type="email" class="form-control" id="test-email" placeholder="Email nhận test">
            </div>
            <button type="button" class="btn btn-outline-primary w-100" onclick="sendTestEmail()">
              <i class="fa fa-paper-plane me-2"></i>Gửi Email Test
            </button>
          </div>
        </div>

        <!-- Email Templates -->
        <div class="card mt-4">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="fa fa-file-alt me-2"></i>Email Templates
            </h5>
          </div>
          <div class="card-body">
            <div class="list-group list-group-flush">
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span>Welcome Email</span>
                <span class="badge bg-success">Hoạt động</span>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span>Reset Password</span>
                <span class="badge bg-success">Hoạt động</span>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span>Order Confirmation</span>
                <span class="badge bg-success">Hoạt động</span>
              </div>
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span>Course Completion</span>
                <span class="badge bg-success">Hoạt động</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Submit Buttons -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <a href="{{ route('admin.settings.index') }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left me-2"></i>Quay Lại
              </a>
              <div>
                <button type="reset" class="btn btn-outline-secondary me-2">
                  <i class="fa fa-undo me-2"></i>Đặt Lại
                </button>
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-save me-2"></i>Lưu Cài Đặt
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<script>
function sendTestEmail() {
  const email = document.getElementById('test-email').value;
  if (!email) {
    alert('Vui lòng nhập email để test!');
    return;
  }
  
  // Simulate test email
  const btn = event.target;
  const originalText = btn.innerHTML;
  btn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Đang gửi...';
  btn.disabled = true;
  
  setTimeout(() => {
    btn.innerHTML = originalText;
    btn.disabled = false;
    alert('Email test đã được gửi! Vui lòng kiểm tra hộp thư.');
  }, 2000);
}

// Auto-fill common SMTP settings
document.querySelector('select[name="mail_driver"]').addEventListener('change', function() {
  const driver = this.value;
  const hostInput = document.querySelector('input[name="mail_host"]');
  const portInput = document.querySelector('input[name="mail_port"]');
  const encryptionSelect = document.querySelector('select[name="mail_encryption"]');
  
  if (driver === 'smtp') {
    // Keep current values or show placeholders
    hostInput.placeholder = 'smtp.gmail.com';
    portInput.placeholder = '587';
  }
});
</script>
@endsection
