<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    protected $fillable = [
        'user_id',
        'title',
        'message',
        'type',
        'data',
        'read_at',
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
    ];

    /**
     * Quan Hệ Với User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope Chưa Đọc
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope Đã Đọc
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Đánh Dấu Đã Đọc
     */
    public function markAsRead()
    {
        $this->update(['read_at' => now()]);
    }

    /**
     * <PERSON><PERSON><PERSON> Tra Đã Đọc
     */
    public function isRead()
    {
        return !is_null($this->read_at);
    }

    /**
     * Accessor Cho Icon
     */
    public function getIconAttribute()
    {
        return match($this->type) {
            'success' => 'fa-check-circle',
            'warning' => 'fa-exclamation-triangle',
            'error' => 'fa-times-circle',
            default => 'fa-info-circle'
        };
    }

    /**
     * Accessor Cho CSS Class
     */
    public function getCssClassAttribute()
    {
        return match($this->type) {
            'success' => 'text-success',
            'warning' => 'text-warning',
            'error' => 'text-danger',
            default => 'text-info'
        };
    }

    /**
     * Tạo Thông Báo Mới
     */
    public static function createForUser($userId, $title, $message, $type = 'info', $data = null)
    {
        return static::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'data' => $data,
        ]);
    }
}
