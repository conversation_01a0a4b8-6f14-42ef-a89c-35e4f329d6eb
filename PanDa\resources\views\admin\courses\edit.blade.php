@extends('layouts.admin')

@section('title', 'Chỉnh Sửa <PERSON> H<PERSON> - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Chỉnh Sửa K<PERSON>ó<PERSON> H<PERSON></h4>
        <p class="text-muted mb-0">{{ $course->title }}</p>
      </div>
      <div>
        <a href="{{ route('admin.courses.index') }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<form method="POST" action="{{ route('admin.courses.update', $course->id) }}" enctype="multipart/form-data">
  @csrf
  @method('PUT')
  <div class="row">
    <div class="col-lg-8">
      <!-- Thông Tin Cơ Bản -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Thông Tin Cơ Bản
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-12 mb-3">
              <label for="title" class="form-label">Tiêu Đề Khóa Học <span class="text-danger">*</span></label>
              <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title', $course->title) }}" required>
              @error('title')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-md-6 mb-3">
              <label for="category_id" class="form-label">Danh Mục <span class="text-danger">*</span></label>
              <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                <option value="">Chọn Danh Mục</option>
                @foreach($categories as $category)
                <option value="{{ $category->id }}" {{ old('category_id', $course->category_id) == $category->id ? 'selected' : '' }}>
                  {{ $category->name }}
                </option>
                @endforeach
              </select>
              @error('category_id')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-md-6 mb-3">
              <label for="level" class="form-label">Cấp Độ <span class="text-danger">*</span></label>
              <select class="form-select @error('level') is-invalid @enderror" id="level" name="level" required>
                <option value="">Chọn Cấp Độ</option>
                <option value="beginner" {{ old('level', $course->level) == 'beginner' ? 'selected' : '' }}>Người Mới Bắt Đầu</option>
                <option value="intermediate" {{ old('level', $course->level) == 'intermediate' ? 'selected' : '' }}>Trung Cấp</option>
                <option value="advanced" {{ old('level', $course->level) == 'advanced' ? 'selected' : '' }}>Nâng Cao</option>
              </select>
              @error('level')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-12 mb-3">
              <label for="description" class="form-label">Mô Tả <span class="text-danger">*</span></label>
              <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="4" required>{{ old('description', $course->description) }}</textarea>
              @error('description')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-12 mb-3">
              <label for="image_url" class="form-label">URL Hình Ảnh Khóa Học</label>

              @if($course->image_url)
              <div class="mb-2">
                <img src="{{ $course->image_url }}" alt="Current Image" class="img-fluid rounded" style="max-height: 200px;">
                <p class="text-muted small mt-1">Hình Ảnh Hiện Tại</p>
              </div>
              @endif

              <input type="url" class="form-control @error('image_url') is-invalid @enderror"
                     id="image_url" name="image_url" value="{{ old('image_url', $course->image_url) }}"
                     placeholder="https://example.com/course-image.jpg">
              @error('image_url')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
              <div class="form-text">Nhập URL hình ảnh từ internet (tiết kiệm dung lượng server)</div>

              <!-- Preview -->
              <div id="image-preview-container" class="mt-2" style="display: none;">
                <img id="image-preview" class="img-fluid rounded" style="max-height: 200px;" alt="Preview">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chi Tiết Khóa Học -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Chi Tiết Khóa Học
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-12 mb-3">
              <label for="requirements" class="form-label">Yêu Cầu</label>
              <textarea class="form-control @error('requirements') is-invalid @enderror" id="requirements" name="requirements" rows="3">{{ old('requirements', $course->requirements) }}</textarea>
              @error('requirements')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-12 mb-3">
              <label for="what_you_learn" class="form-label">Bạn Sẽ Học Được</label>
              <textarea class="form-control @error('what_you_learn') is-invalid @enderror" id="what_you_learn" name="what_you_learn" rows="3">{{ old('what_you_learn', $course->what_you_learn) }}</textarea>
              @error('what_you_learn')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <!-- Cài Đặt -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Cài Đặt
          </h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label for="price" class="form-label">Giá <span class="text-danger">*</span></label>
            <div class="input-group">
              <input type="number" class="form-control @error('price') is-invalid @enderror" id="price" name="price" value="{{ old('price', $course->price) }}" min="0" step="1000" required>
              <span class="input-group-text">VNĐ</span>
            </div>
            @error('price')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          
          <div class="mb-3">
            <label for="duration_hours" class="form-label">Thời Lượng <span class="text-danger">*</span></label>
            <div class="input-group">
              <input type="number" class="form-control @error('duration_hours') is-invalid @enderror" id="duration_hours" name="duration_hours" value="{{ old('duration_hours', $course->duration_hours) }}" min="1" required>
              <span class="input-group-text">Giờ</span>
            </div>
            @error('duration_hours')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
          </div>
          
          <div class="mb-3">
            <label for="status" class="form-label">Trạng Thái</label>
            <select class="form-select" id="status" name="status">
              <option value="draft" {{ old('status', $course->status) == 'draft' ? 'selected' : '' }}>Nháp</option>
              <option value="published" {{ old('status', $course->status) == 'published' ? 'selected' : '' }}>Xuất Bản</option>
              <option value="archived" {{ old('status', $course->status) == 'archived' ? 'selected' : '' }}>Lưu Trữ</option>
            </select>
          </div>

          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="is_completed" name="is_completed" value="1" {{ old('is_completed', $course->is_completed) ? 'checked' : '' }}>
              <label class="form-check-label" for="is_completed">
                <strong>Đánh Dấu Khóa Học Hoàn Thành</strong>
              </label>
              <div class="form-text">
                <i class="fa fa-info-circle me-1"></i>
                Khi Tích Chọn: User Chỉ Hoàn Thành Khóa Học Khi Học Xong 100% Bài Học VÀ Admin Đã Đánh Dấu.<br>
                <i class="fa fa-warning me-1 text-warning"></i>
                Nếu Bỏ Tích Sau Khi User Đã Hoàn Thành: User Sẽ Phải Học Lại Để Hoàn Thành.
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Thông Tin Thêm -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Thông Tin
          </h5>
        </div>
        <div class="card-body">
          <div class="mb-2">
            <small class="text-muted">Slug:</small>
            <div class="fw-bold">{{ $course->slug }}</div>
          </div>
          <div class="mb-2">
            <small class="text-muted">Ngày Tạo:</small>
            <div class="fw-bold">{{ $course->created_at->format('d/m/Y H:i') }}</div>
          </div>
          <div class="mb-2">
            <small class="text-muted">Cập Nhật Cuối:</small>
            <div class="fw-bold">{{ $course->updated_at->format('d/m/Y H:i') }}</div>
          </div>
        </div>
      </div>

      <!-- Hành Động -->
      <div class="card">
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary">
              <i class="fa fa-save me-2"></i>Cập Nhật Khóa Học
            </button>
            <a href="{{ route('admin.courses.show', $course->id) }}" class="btn btn-outline-info">
              <i class="fa fa-eye me-2"></i>Xem Chi Tiết
            </a>
            <a href="{{ route('admin.courses.index') }}" class="btn btn-outline-secondary">
              <i class="fa fa-times me-2"></i>Hủy
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
@endsection

@push('scripts')
<script>
// Preview URL Ảnh
document.getElementById('image_url').addEventListener('input', function(e) {
  const url = e.target.value;
  const preview = document.getElementById('image-preview');
  const container = document.getElementById('image-preview-container');

  if (url && isValidImageUrl(url)) {
    preview.src = url;
    container.style.display = 'block';

    // Kiểm tra nếu ảnh load thành công
    preview.onload = function() {
      container.style.display = 'block';
    };

    preview.onerror = function() {
      container.style.display = 'none';
    };
  } else {
    container.style.display = 'none';
  }
});

// Bỏ validation strict, chấp nhận mọi URL
function isValidImageUrl(url) {
  return url && url.trim() !== '';
}
</script>
@endpush
