@extends('layouts.admin')

@section('title', 'Chi Tiết Bình Lu<PERSON>n')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Chi Tiết Bình Luận</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.comments.index') }}">Bình Luận</a></li>
            <li class="breadcrumb-item active">Chi Tiết</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- <PERSON><PERSON><PERSON> Chính -->
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="fa fa-comment me-2"></i>
              @if($comment->parent_id)
              Trả Lời Bình Luận
              @else
              Bình Luận Chính
              @endif
            </h5>
            <div>
              @if($comment->is_approved)
              <span class="badge bg-success">Đã Duyệt</span>
              @else
              <span class="badge bg-warning">Chờ Duyệt</span>
              @endif
            </div>
          </div>
        </div>
        
        <div class="card-body">
          <!-- Thông Tin Người Dùng -->
          <div class="d-flex align-items-center mb-3">
            @if($comment->user->avatar)
            <img src="{{ asset('storage/' . $comment->user->avatar) }}" alt="{{ $comment->user->name }}" 
                 class="rounded-circle me-3" width="50" height="50">
            @else
            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" 
                 style="width: 50px; height: 50px;">
              <span class="text-white fw-bold">{{ strtoupper(substr($comment->user->name, 0, 1)) }}</span>
            </div>
            @endif
            <div>
              <h6 class="mb-1">{{ $comment->user->name }}</h6>
              <small class="text-muted">{{ $comment->user->email }}</small>
              <br>
              <small class="text-muted">{{ $comment->created_at->format('d/m/Y H:i:s') }}</small>
            </div>
          </div>

          <!-- Nội Dung Bình Luận -->
          <div class="border rounded p-3 bg-light mb-3">
            <p class="mb-0">{{ $comment->content }}</p>
          </div>

          <!-- Bình Luận Gốc (Nếu Là Reply) -->
          @if($comment->parent_id && $comment->parent)
          <div class="alert alert-info">
            <h6><i class="fa fa-reply me-2"></i>Trả Lời Cho Bình Luận:</h6>
            <div class="d-flex align-items-center mb-2">
              @if($comment->parent->user->avatar)
              <img src="{{ asset('storage/' . $comment->parent->user->avatar) }}" alt="{{ $comment->parent->user->name }}" 
                   class="rounded-circle me-2" width="32" height="32">
              @else
              <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" 
                   style="width: 32px; height: 32px;">
                <span class="text-white fw-bold">{{ strtoupper(substr($comment->parent->user->name, 0, 1)) }}</span>
              </div>
              @endif
              <div>
                <strong>{{ $comment->parent->user->name }}</strong>
                <small class="text-muted ms-2">{{ $comment->parent->created_at->format('d/m/Y H:i') }}</small>
              </div>
            </div>
            <p class="mb-0 ms-4">{{ Str::limit($comment->parent->content, 200) }}</p>
          </div>
          @endif

          <!-- Thông Tin Bài Học -->
          <div class="row">
            <div class="col-md-6">
              <h6><i class="fa fa-book me-2"></i>Thông Tin Bài Học</h6>
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Bài Học:</strong></td>
                  <td>{{ $comment->lesson->title }}</td>
                </tr>
                <tr>
                  <td><strong>Chương:</strong></td>
                  <td>{{ $comment->lesson->chapter->title }}</td>
                </tr>
                <tr>
                  <td><strong>Khóa Học:</strong></td>
                  <td>{{ $comment->lesson->chapter->course->title }}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <h6><i class="fa fa-info-circle me-2"></i>Thông Tin Bình Luận</h6>
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>ID:</strong></td>
                  <td>{{ $comment->id }}</td>
                </tr>
                <tr>
                  <td><strong>Loại:</strong></td>
                  <td>
                    @if($comment->parent_id)
                    <span class="badge bg-info">Trả Lời</span>
                    @else
                    <span class="badge bg-primary">Bình Luận Chính</span>
                    @endif
                  </td>
                </tr>
                <tr>
                  <td><strong>Số Trả Lời:</strong></td>
                  <td>{{ $comment->replies->count() }}</td>
                </tr>
              </table>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <div class="d-flex justify-content-between">
            <a href="{{ route('admin.comments.index') }}" class="btn btn-secondary">
              <i class="fa fa-arrow-left me-2"></i>Quay Lại Danh Sách
            </a>
            <div>
              @if(!$comment->is_approved)
              <form action="{{ route('admin.comments.approve', $comment->id) }}" method="POST" class="d-inline">
                @csrf
                @method('PATCH')
                <button type="submit" class="btn btn-success">
                  <i class="fa fa-check me-2"></i>Duyệt Bình Luận
                </button>
              </form>
              @else
              <form action="{{ route('admin.comments.reject', $comment->id) }}" method="POST" class="d-inline">
                @csrf
                @method('PATCH')
                <button type="submit" class="btn btn-warning">
                  <i class="fa fa-times me-2"></i>Từ Chối
                </button>
              </form>
              @endif
              
              <form action="{{ route('admin.comments.destroy', $comment->id) }}" method="POST" class="d-inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger" 
                        onclick="return confirm('Bạn có chắc muốn xóa bình luận này?')">
                  <i class="fa fa-trash me-2"></i>Xóa
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>

      <!-- Các Trả Lời -->
      @if($comment->replies->count() > 0)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="fa fa-comments me-2"></i>Các Trả Lời ({{ $comment->replies->count() }})
          </h6>
        </div>
        <div class="card-body">
          @foreach($comment->replies as $reply)
          <div class="border rounded p-3 mb-3">
            <div class="d-flex justify-content-between align-items-start">
              <div class="d-flex align-items-center">
                @if($reply->user->avatar)
                <img src="{{ asset('storage/' . $reply->user->avatar) }}" alt="{{ $reply->user->name }}" 
                     class="rounded-circle me-2" width="32" height="32">
                @else
                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                     style="width: 32px; height: 32px;">
                  <span class="text-white fw-bold">{{ strtoupper(substr($reply->user->name, 0, 1)) }}</span>
                </div>
                @endif
                <div>
                  <h6 class="mb-1">{{ $reply->user->name }}</h6>
                  <small class="text-muted">{{ $reply->created_at->format('d/m/Y H:i:s') }}</small>
                </div>
              </div>
              <div>
                @if($reply->is_approved)
                <span class="badge bg-success">Đã Duyệt</span>
                @else
                <span class="badge bg-warning">Chờ Duyệt</span>
                @endif
              </div>
            </div>
            
            <div class="mt-2 ms-4">
              <p class="mb-2">{{ $reply->content }}</p>
              
              <div class="btn-group btn-group-sm">
                @if(!$reply->is_approved)
                <form action="{{ route('admin.comments.approve', $reply->id) }}" method="POST" class="d-inline">
                  @csrf
                  @method('PATCH')
                  <button type="submit" class="btn btn-outline-success">
                    <i class="fa fa-check"></i>
                  </button>
                </form>
                @else
                <form action="{{ route('admin.comments.reject', $reply->id) }}" method="POST" class="d-inline">
                  @csrf
                  @method('PATCH')
                  <button type="submit" class="btn btn-outline-warning">
                    <i class="fa fa-times"></i>
                  </button>
                </form>
                @endif
                
                <form action="{{ route('admin.comments.destroy', $reply->id) }}" method="POST" class="d-inline">
                  @csrf
                  @method('DELETE')
                  <button type="submit" class="btn btn-outline-danger" 
                          onclick="return confirm('Bạn có chắc muốn xóa trả lời này?')">
                    <i class="fa fa-trash"></i>
                  </button>
                </form>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
      @endif
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
      <!-- Thống Kê User -->
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-user me-2"></i>Thông Tin Người Dùng
          </h6>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            @if($comment->user->avatar)
            <img src="{{ asset('storage/' . $comment->user->avatar) }}" alt="{{ $comment->user->name }}" 
                 class="rounded-circle" width="80" height="80">
            @else
            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                 style="width: 80px; height: 80px;">
              <span class="text-white fw-bold h4">{{ strtoupper(substr($comment->user->name, 0, 1)) }}</span>
            </div>
            @endif
            <h6 class="mt-2 mb-1">{{ $comment->user->name }}</h6>
            <small class="text-muted">{{ $comment->user->email }}</small>
          </div>
          
          <table class="table table-borderless table-sm">
            <tr>
              <td><strong>Role:</strong></td>
              <td>
                @if($comment->user->role == 'admin')
                <span class="badge bg-danger">Admin</span>
                @else
                <span class="badge bg-primary">User</span>
                @endif
              </td>
            </tr>
            <tr>
              <td><strong>Tổng Bình Luận:</strong></td>
              <td>{{ $comment->user->comments->count() }}</td>
            </tr>
            <tr>
              <td><strong>Đã Duyệt:</strong></td>
              <td>{{ $comment->user->comments->where('is_approved', true)->count() }}</td>
            </tr>
            <tr>
              <td><strong>Ngày Tham Gia:</strong></td>
              <td>{{ $comment->user->created_at->format('d/m/Y') }}</td>
            </tr>
          </table>
          
          <div class="d-grid">
            <a href="{{ route('admin.users.show', $comment->user->id) }}" class="btn btn-primary btn-sm">
              <i class="fa fa-eye me-1"></i>Xem Chi Tiết User
            </a>
          </div>
        </div>
      </div>

      <!-- Thao Tác Nhanh -->
      <div class="card mt-3">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-bolt me-2"></i>Thao Tác Nhanh
          </h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('user.courses.lessons.show', ['course' => $comment->lesson->chapter->course->id, 'chapter' => $comment->lesson->chapter->id, 'lesson' => $comment->lesson->id]) }}" 
               class="btn btn-info btn-sm" target="_blank">
              <i class="fa fa-external-link-alt me-1"></i>Xem Bài Học
            </a>
            <a href="{{ route('admin.lessons.show', $comment->lesson->id) }}" class="btn btn-success btn-sm">
              <i class="fa fa-book me-1"></i>Quản Lý Bài Học
            </a>
            <a href="{{ route('admin.courses.show', $comment->lesson->chapter->course->id) }}" class="btn btn-warning btn-sm">
              <i class="fa fa-graduation-cap me-1"></i>Xem Khóa Học
            </a>
          </div>
        </div>
      </div>

      <!-- Bình Luận Gần Đây -->
      <div class="card mt-3">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-clock me-2"></i>Bình Luận Gần Đây Của User
          </h6>
        </div>
        <div class="card-body">
          @php
            $recentComments = $comment->user->comments()->with('lesson.chapter.course')->latest()->take(3)->get();
          @endphp
          
          @if($recentComments->count() > 0)
          @foreach($recentComments as $recent)
          <div class="border-bottom pb-2 mb-2">
            <h6 class="mb-1">{{ $recent->lesson->title }}</h6>
            <p class="mb-1 small">{{ Str::limit($recent->content, 80) }}</p>
            <small class="text-muted">{{ $recent->created_at->diffForHumans() }}</small>
          </div>
          @endforeach
          @else
          <p class="text-muted small">Không có bình luận gần đây</p>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
