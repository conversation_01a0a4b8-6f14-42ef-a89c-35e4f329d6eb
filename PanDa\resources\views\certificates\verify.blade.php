<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> Chỉ - PanDa Learning</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .verify-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .verify-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
        }
        
        .verify-header {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .verify-body {
            padding: 40px;
        }
        
        .certificate-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border-left: 5px solid #6c757d;
        }
        
        .status-badge {
            font-size: 1.2rem;
            padding: 10px 20px;
            border-radius: 50px;
        }
        
        .status-valid {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status-invalid {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .verification-details {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        
        .detail-value {
            color: #6c757d;
            text-align: right;
        }
        
        .verify-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 24px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <div class="verify-card">
            <div class="verify-header">
                <div class="logo">
                    <i class="fa fa-graduation-cap"></i>
                </div>
                <h1 class="mb-2">Xác Thực Chứng Chỉ</h1>
                <p class="mb-0 opacity-75">PanDa Learning Platform</p>
            </div>
            
            <div class="verify-body">
                @if($certificate)
                    <!-- Valid Certificate -->
                    <div class="text-center mb-4">
                        <div class="status-badge status-valid">
                            <i class="fa fa-check-circle me-2"></i>Chứng Chỉ Hợp Lệ
                        </div>
                    </div>
                    
                    <div class="certificate-info">
                        <div class="text-center mb-4">
                            <h3 class="text-primary mb-2">{{ $certificate->certificate_data['course_title'] }}</h3>
                            <h4 class="text-success">{{ $certificate->certificate_data['user_name'] }}</h4>
                        </div>
                        
                        <div class="verification-details">
                            <div class="detail-row">
                                <span class="detail-label">Mã Chứng Chỉ:</span>
                                <span class="detail-value">{{ $certificate->certificate_code }}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Người Nhận:</span>
                                <span class="detail-value">{{ $certificate->certificate_data['user_name'] }}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Khóa Học:</span>
                                <span class="detail-value">{{ $certificate->certificate_data['course_title'] }}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Ngày Cấp:</span>
                                <span class="detail-value">{{ $certificate->formatted_issue_date }}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Trạng Thái:</span>
                                <span class="detail-value">
                                    <span class="badge bg-success">Đã Xác Thực</span>
                                </span>
                            </div>
                            @if(isset($certificate->certificate_data['score']))
                            <div class="detail-row">
                                <span class="detail-label">Điểm Số:</span>
                                <span class="detail-value">{{ $certificate->certificate_data['score'] }}</span>
                            </div>
                            @endif
                            @if(isset($certificate->certificate_data['completion_time']))
                            <div class="detail-row">
                                <span class="detail-label">Thời Gian Hoàn Thành:</span>
                                <span class="detail-value">{{ $certificate->certificate_data['completion_time'] }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                    
                    <div class="verify-actions">
                        <a href="{{ route('certificates.verify', $certificate->certificate_code) }}" class="btn btn-outline-primary me-2">
                            <i class="fa fa-refresh me-2"></i>Xác Thực Lại
                        </a>
                        <a href="{{ route('home') }}" class="btn btn-secondary">
                            <i class="fa fa-home me-2"></i>Về Trang Chủ
                        </a>
                    </div>
                    
                @else
                    <!-- Invalid Certificate -->
                    <div class="text-center mb-4">
                        <div class="status-badge status-invalid">
                            <i class="fa fa-times-circle me-2"></i>Chứng Chỉ Không Hợp Lệ
                        </div>
                    </div>
                    
                    <div class="certificate-info">
                        <div class="text-center">
                            <i class="fa fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h4 class="text-danger mb-3">Không Tìm Thấy Chứng Chỉ</h4>
                            <p class="text-muted mb-4">
                                Mã chứng chỉ <strong>{{ request()->route('code') }}</strong> không tồn tại trong hệ thống 
                                hoặc đã bị thu hồi.
                            </p>
                            
                            <div class="alert alert-warning" role="alert">
                                <strong>Lưu ý:</strong> Vui lòng kiểm tra lại mã chứng chỉ hoặc liên hệ với người cấp chứng chỉ để được hỗ trợ.
                            </div>
                        </div>
                    </div>
                    
                    <div class="verify-actions">
                        <button class="btn btn-primary me-2" onclick="window.location.reload()">
                            <i class="fa fa-refresh me-2"></i>Thử Lại
                        </button>
                        <a href="{{ route('home') }}" class="btn btn-outline-secondary">
                            <i class="fa fa-home me-2"></i>Về Trang Chủ
                        </a>
                    </div>
                @endif
                
                <!-- Footer Info -->
                <div class="text-center mt-5 pt-4 border-top">
                    <small class="text-muted">
                        <i class="fa fa-shield-alt me-1"></i>
                        Hệ thống xác thực chứng chỉ được bảo mật bởi PanDa Learning Platform
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
