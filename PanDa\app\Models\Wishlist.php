<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Wishlist extends Model
{
    protected $fillable = [
        'user_id',
        'course_id',
    ];

    /**
     * Quan Hệ Với User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Quan Hệ Với Course
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Thêm Khóa Học Vào Wishlist
     */
    public static function toggle($userId, $courseId): array
    {
        $wishlist = static::where('user_id', $userId)
            ->where('course_id', $courseId)
            ->first();

        if ($wishlist) {
            // Nếu đã có thì xóa
            $wishlist->delete();
            return [
                'action' => 'removed',
                'message' => 'Đã Xóa Khỏi Danh Sách Yêu Thích',
                'in_wishlist' => false
            ];
        } else {
            // Nếu chưa có thì thêm
            static::create([
                'user_id' => $userId,
                'course_id' => $courseId,
            ]);
            return [
                'action' => 'added',
                'message' => 'Đã Thêm Vào Danh Sách Yêu Thích',
                'in_wishlist' => true
            ];
        }
    }

    /**
     * Kiểm Tra Course Có Trong Wishlist
     */
    public static function isInWishlist($userId, $courseId): bool
    {
        return static::where('user_id', $userId)
            ->where('course_id', $courseId)
            ->exists();
    }

    /**
     * Lấy Số Lượng Wishlist Của User
     */
    public static function getCountForUser($userId): int
    {
        return static::where('user_id', $userId)->count();
    }
}
