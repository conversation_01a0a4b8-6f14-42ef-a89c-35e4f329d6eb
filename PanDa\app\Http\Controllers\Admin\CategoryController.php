<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    /**
     * Hiển <PERSON>ch <PERSON>
     */
    public function index(Request $request)
    {
        $query = Category::withCount('courses');

        // Tìm <PERSON>
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $categories = $query->latest()->paginate(10);

        return view('admin.categories.index', compact('categories'));
    }

    /**
     * <PERSON><PERSON>n <PERSON>hị Form Tạo <PERSON>
     */
    public function create()
    {
        return view('admin.categories.create');
    }

    /**
     * <PERSON><PERSON><PERSON>
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:categories',
            'description' => 'nullable|string',
            'image_url' => 'nullable|url',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
        ], [
            'name.required' => 'Tên Danh Mục Là Bắt Buộc',
            'name.unique' => 'Tên Danh Mục Đã Tồn Tại',
            'image_url.url' => 'URL Hình Ảnh Không Hợp Lệ',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);
        $data['is_active'] = $request->has('is_active');

        Category::create($data);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Tạo Danh Mục Thành Công!');
    }

    /**
     * Hiển Thị Chi Tiết Danh Mục
     */
    public function show($id)
    {
        $category = Category::with('courses')->findOrFail($id);
        return view('admin.categories.show', compact('category'));
    }

    /**
     * Hiển Thị Form Chỉnh Sửa
     */
    public function edit($id)
    {
        $category = Category::findOrFail($id);
        return view('admin.categories.edit', compact('category'));
    }

    /**
     * Cập Nhật Danh Mục
     */
    public function update(Request $request, $id)
    {
        $category = Category::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:categories,name,' . $id,
            'description' => 'nullable|string',
            'image_url' => 'nullable|url',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
        ], [
            'name.required' => 'Tên Danh Mục Là Bắt Buộc',
            'name.unique' => 'Tên Danh Mục Đã Tồn Tại',
            'image_url.url' => 'URL Hình Ảnh Không Hợp Lệ',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);
        $data['is_active'] = $request->has('is_active');

        // Debug
        \Log::info('Category update data:', $data);

        $category->update($data);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Cập Nhật Danh Mục Thành Công!');
    }

    /**
     * Xóa Danh Mục
     */
    public function destroy($id)
    {
        $category = Category::findOrFail($id);

        // Kiểm Tra Có Khóa Học Không
        if ($category->courses()->count() > 0) {
            return redirect()->route('admin.categories.index')
                ->with('error', 'Không Thể Xóa Danh Mục Có Khóa Học!');
        }

        // Không cần xóa hình ảnh vì dùng URL

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', 'Xóa Danh Mục Thành Công!');
    }
}
