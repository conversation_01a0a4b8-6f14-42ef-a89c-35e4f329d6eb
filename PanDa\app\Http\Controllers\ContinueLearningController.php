<?php

namespace App\Http\Controllers;

use App\Models\UserCourse;
use Illuminate\Http\Request;

class ContinueLearningController extends Controller
{
    /**
     * Hiển Thị <PERSON>rang Tiếp Tục <PERSON>
     */
    public function index()
    {
        // L<PERSON>y khóa học đang học (có started_at nhưng chưa completed_at)
        $inProgressCourses = auth()->user()->userCourses()
            ->with(['course.category', 'course.chapters.lessons', 'currentLesson.chapter'])
            ->inProgress()
            ->orderBy('last_accessed_at', 'desc')
            ->limit(6)
            ->get();

        // Lấy khóa học gần đây nhất được truy cập
        $recentCourses = auth()->user()->userCourses()
            ->with(['course.category', 'course.chapters.lessons', 'currentLesson.chapter'])
            ->whereNotNull('last_accessed_at')
            ->orderBy('last_accessed_at', 'desc')
            ->limit(8)
            ->get();

        // Lấy khóa học chưa bắt đầu
        $notStartedCourses = auth()->user()->userCourses()
            ->with(['course.category', 'course.chapters.lessons'])
            ->notStarted()
            ->orderBy('enrolled_at', 'desc')
            ->limit(4)
            ->get();

        // Thống kê tổng quan
        $stats = [
            'total_courses' => auth()->user()->userCourses()->count(),
            'in_progress' => auth()->user()->userCourses()->inProgress()->count(),
            'completed' => auth()->user()->userCourses()->completed()->count(),
            'not_started' => auth()->user()->userCourses()->notStarted()->count(),
            'total_progress' => auth()->user()->userCourses()->avg('progress_percentage') ?? 0,
        ];

        return view('user.continue-learning.index', compact(
            'inProgressCourses',
            'recentCourses',
            'notStartedCourses',
            'stats'
        ));
    }

    /**
     * Tiếp Tục Học Khóa Học Gần Đây Nhất
     */
    public function continueRecent()
    {
        $recentCourse = auth()->user()->userCourses()
            ->whereNotNull('last_accessed_at')
            ->orderBy('last_accessed_at', 'desc')
            ->first();

        if (!$recentCourse) {
            return redirect()->route('my-courses.index')
                ->with('info', 'Bạn Chưa Có Khóa Học Nào Để Tiếp Tục');
        }

        return redirect()->route('my-courses.continue', $recentCourse->course_id);
    }

    /**
     * Lấy Gợi Ý Học Tập
     */
    public function getSuggestions()
    {
        $suggestions = [];

        // Gợi ý dựa trên khóa học đang học
        $inProgressCourses = auth()->user()->userCourses()
            ->with(['course'])
            ->inProgress()
            ->get();

        foreach ($inProgressCourses as $userCourse) {
            $nextLesson = $userCourse->getNextLesson();
            if ($nextLesson) {
                $suggestions[] = [
                    'type' => 'continue',
                    'title' => 'Tiếp Tục: ' . $userCourse->course->title,
                    'description' => 'Bài Học Tiếp Theo: ' . $nextLesson->title,
                    'progress' => $userCourse->progress_percentage,
                    'url' => route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $nextLesson->chapter_id, 'lesson' => $nextLesson->id]),
                    'course' => $userCourse->course,
                ];
            }
        }

        // Gợi ý khóa học chưa bắt đầu
        $notStartedCourses = auth()->user()->userCourses()
            ->with(['course'])
            ->notStarted()
            ->limit(3)
            ->get();

        foreach ($notStartedCourses as $userCourse) {
            $suggestions[] = [
                'type' => 'start',
                'title' => 'Bắt Đầu: ' . $userCourse->course->title,
                'description' => 'Khóa Học Mới Chờ Bạn Khám Phá',
                'progress' => 0,
                'url' => route('my-courses.start', $userCourse->course_id),
                'course' => $userCourse->course,
            ];
        }

        return response()->json(['suggestions' => $suggestions]);
    }

    /**
     * Cập Nhật Mục Tiêu Học Tập
     */
    public function updateGoal(Request $request)
    {
        $request->validate([
            'daily_minutes' => 'required|integer|min:5|max:480', // 5 phút đến 8 tiếng
            'weekly_lessons' => 'required|integer|min:1|max:50',
        ]);

        $user = auth()->user();

        // Lưu vào session hoặc user preferences (có thể tạo bảng user_preferences sau)
        session([
            'learning_goal_daily_minutes' => $request->daily_minutes,
            'learning_goal_weekly_lessons' => $request->weekly_lessons,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Cập Nhật Mục Tiêu Thành Công!'
        ]);
    }

    /**
     * Lấy Thống Kê Học Tập
     */
    public function getStats()
    {
        $user = auth()->user();

        // Thống kê tuần này
        $thisWeekStats = [
            'lessons_completed' => $user->userCourses()
                ->whereHas('course.chapters.lessons', function($query) {
                    // Đếm lessons completed trong tuần này - cần implement logic phức tạp hơn
                })
                ->count(),
            'time_spent' => 0, // Cần implement tracking time
            'courses_started' => $user->userCourses()
                ->whereBetween('started_at', [now()->startOfWeek(), now()->endOfWeek()])
                ->count(),
        ];

        // Streak (chuỗi ngày học liên tiếp)
        $streak = $this->calculateLearningStreak($user);

        return response()->json([
            'this_week' => $thisWeekStats,
            'streak' => $streak,
            'total_progress' => $user->userCourses()->avg('progress_percentage') ?? 0,
        ]);
    }

    /**
     * Tính Chuỗi Ngày Học Liên Tiếp
     */
    private function calculateLearningStreak($user)
    {
        // Đây là logic đơn giản, có thể cải thiện bằng cách track daily activity
        $recentActivity = $user->userCourses()
            ->whereNotNull('last_accessed_at')
            ->where('last_accessed_at', '>=', now()->subDays(30))
            ->orderBy('last_accessed_at', 'desc')
            ->get();

        $streak = 0;
        $currentDate = now()->startOfDay();

        foreach ($recentActivity as $activity) {
            $activityDate = $activity->last_accessed_at->startOfDay();

            if ($activityDate->eq($currentDate) || $activityDate->eq($currentDate->subDay())) {
                $streak++;
                $currentDate = $activityDate->subDay();
            } else {
                break;
            }
        }

        return $streak;
    }
}
