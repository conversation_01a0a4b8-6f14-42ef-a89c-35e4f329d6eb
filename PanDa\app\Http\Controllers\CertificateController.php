<?php

namespace App\Http\Controllers;

use App\Models\Certificate;
use Illuminate\Http\Request;

class CertificateController extends Controller
{
    /**
     * Hiển Thị Danh Sách Chứng Chỉ
     */
    public function index(Request $request)
    {
        $query = auth()->user()->certificates()->with(['course.category']);

        // Lọc theo năm
        if ($request->filled('year')) {
            $query->byYear($request->year);
        }

        // Sắp xếp
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'course':
                $query->join('courses', 'certificates.course_id', '=', 'courses.id')
                     ->orderBy('courses.title');
                break;
            case 'oldest':
                $query->orderBy('issued_at', 'asc');
                break;
            default:
                $query->orderBy('issued_at', 'desc');
        }

        $certificates = $query->paginate(12);

        // L<PERSON>y danh sách năm để filter (SQLite compatible)
        $years = auth()->user()->certificates()
            ->selectRaw("strftime('%Y', issued_at) as year")
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year');

        return view('user.certificates.index', compact('certificates', 'years'));
    }

    /**
     * Hiển Thị Chi Tiết Chứng Chỉ
     */
    public function show($id)
    {
        $certificate = auth()->user()->certificates()
            ->with(['course.category', 'userCourse'])
            ->findOrFail($id);

        return view('user.certificates.show', compact('certificate'));
    }

    /**
     * Tải Chứng Chỉ PDF
     */
    public function download($id)
    {
        $certificate = auth()->user()->certificates()->findOrFail($id);

        // Nếu chưa có file PDF, tạo mới
        if (!$certificate->file_path || !file_exists(storage_path('app/' . $certificate->file_path))) {
            $this->generateCertificatePDF($certificate);
        }

        $filePath = storage_path('app/' . $certificate->file_path);

        if (!file_exists($filePath)) {
            abort(404, 'File Chứng Chỉ Không Tồn Tại');
        }

        return response()->download($filePath, "Certificate_{$certificate->certificate_code}.pdf");
    }

    /**
     * Verify Chứng Chỉ (Public)
     */
    public function verify($code)
    {
        $certificate = Certificate::verifyByCode($code);

        if (!$certificate) {
            return view('certificates.verify', [
                'certificate' => null,
                'message' => 'Mã Chứng Chỉ Không Hợp Lệ Hoặc Đã Hết Hạn'
            ]);
        }

        return view('certificates.verify', compact('certificate'));
    }

    /**
     * Tạo Certificate PDF
     */
    private function generateCertificatePDF(Certificate $certificate)
    {
        // Đây là implementation đơn giản, có thể dùng thư viện như TCPDF, DomPDF
        $html = view('certificates.template', compact('certificate'))->render();

        // Tạo thư mục nếu chưa có
        $certificateDir = storage_path('app/certificates');
        if (!file_exists($certificateDir)) {
            mkdir($certificateDir, 0755, true);
        }

        $fileName = "certificate_{$certificate->certificate_code}.pdf";
        $filePath = "certificates/{$fileName}";

        // Đây là mock - trong thực tế cần dùng PDF library
        file_put_contents(storage_path('app/' . $filePath), $html);

        $certificate->update(['file_path' => $filePath]);
    }

    /**
     * Chia Sẻ Chứng Chỉ
     */
    public function share($id)
    {
        $certificate = auth()->user()->certificates()->findOrFail($id);

        $shareData = [
            'title' => "Chứng Chỉ Hoàn Thành Khóa Học: {$certificate->course->title}",
            'description' => "Tôi đã hoàn thành khóa học {$certificate->course->title} tại PanDa Learning",
            'url' => $certificate->verify_url,
            'image' => asset('images/certificate-share.png'), // Có thể tạo ảnh share
        ];

        return view('user.certificates.share', compact('certificate', 'shareData'));
    }

    /**
     * Tạo Lại Chứng Chỉ
     */
    public function regenerate($id)
    {
        $certificate = auth()->user()->certificates()->findOrFail($id);

        // Xóa file cũ nếu có
        if ($certificate->file_path && file_exists(storage_path('app/' . $certificate->file_path))) {
            unlink(storage_path('app/' . $certificate->file_path));
        }

        // Tạo lại PDF
        $this->generateCertificatePDF($certificate);

        return back()->with('success', 'Tạo Lại Chứng Chỉ Thành Công!');
    }
}
