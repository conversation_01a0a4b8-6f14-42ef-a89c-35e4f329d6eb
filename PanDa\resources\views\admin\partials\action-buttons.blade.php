{{--
  Action Buttons Component cho Admin
  
  Props:
  - $item: Model instance
  - $type: Loại resource (categories, courses, chapters, lessons, users, comments, reviews)
  - $showRoute: Route xem chi tiết (optional)
  - $editRoute: Route chỉnh sửa (optional)
  - $deleteRoute: Route xóa (optional)
  - $extraButtons: <PERSON><PERSON><PERSON> các button bổ sung (optional)
  - $canDelete: <PERSON><PERSON> thể xóa không (default: true)
  - $deleteMessage: Thông báo xác nhận xóa (optional)
--}}

@php
  $canDelete = $canDelete ?? true;
  $deleteMessage = $deleteMessage ?? 'Bạn Có Chắc Muốn Xóa?';
  $extraButtons = $extraButtons ?? [];
@endphp

<div class="d-flex" style="gap: 2px;">
  {{-- Nút Xem Chi Tiết --}}
  @if(isset($showRoute))
  <a href="{{ $showRoute }}"
     class="btn btn-outline-info d-flex align-items-center justify-content-center"
     data-bs-toggle="tooltip"
     style="width: 32px; height: 32px;"
     aria-label="Xem Chi Tiết"
     data-bs-original-title="Xem Chi Tiết">
    <i class="fa fa-eye" style="font-size: 14px;"></i>
  </a>
  @endif

  {{-- Nút Bổ Sung (trước Edit) --}}
  @foreach($extraButtons as $button)
    @if($button['position'] === 'before-edit')
    <a href="{{ $button['route'] }}"
       class="btn btn-outline-{{ $button['color'] ?? 'primary' }} d-flex align-items-center justify-content-center"
       data-bs-toggle="tooltip"
       style="width: 32px; height: 32px;"
       aria-label="{{ $button['title'] }}"
       data-bs-original-title="{{ $button['title'] }}"
       @if(isset($button['target'])) target="{{ $button['target'] }}" @endif
       @if(isset($button['onclick'])) onclick="{{ $button['onclick'] }}" @endif>
      <i class="fa fa-{{ $button['icon'] }}" style="font-size: 14px;"></i>
    </a>
    @endif
  @endforeach

  {{-- Nút Chỉnh Sửa --}}
  @if(isset($editRoute))
  <a href="{{ $editRoute }}"
     class="btn btn-outline-warning d-flex align-items-center justify-content-center"
     data-bs-toggle="tooltip"
     style="width: 32px; height: 32px;"
     aria-label="Chỉnh Sửa"
     data-bs-original-title="Chỉnh Sửa">
    <i class="fa fa-edit" style="font-size: 14px;"></i>
  </a>
  @endif

  {{-- Nút Bổ Sung (sau Edit) --}}
  @foreach($extraButtons as $button)
    @if($button['position'] === 'after-edit')
    <a href="{{ $button['route'] }}"
       class="btn btn-outline-{{ $button['color'] ?? 'primary' }} d-flex align-items-center justify-content-center"
       data-bs-toggle="tooltip"
       style="width: 32px; height: 32px;"
       aria-label="{{ $button['title'] }}"
       data-bs-original-title="{{ $button['title'] }}"
       @if(isset($button['target'])) target="{{ $button['target'] }}" @endif
       @if(isset($button['onclick'])) onclick="{{ $button['onclick'] }}" @endif>
      <i class="fa fa-{{ $button['icon'] }}" style="font-size: 14px;"></i>
    </a>
    @endif
  @endforeach

  {{-- Nút Xóa --}}
  @if(isset($deleteRoute))
    @if($canDelete)
    <form method="POST" action="{{ $deleteRoute }}" class="d-inline">
      @csrf
      @method('DELETE')
      <button type="submit"
              class="btn btn-outline-danger d-flex align-items-center justify-content-center"
              data-bs-toggle="tooltip"
              style="width: 32px; height: 32px;"
              onclick="return confirm('{{ $deleteMessage }}')"
              aria-label="Xóa"
              data-bs-original-title="Xóa">
        <i class="fa fa-trash" style="font-size: 14px;"></i>
      </button>
    </form>
    @else
    <button class="btn btn-outline-danger d-flex align-items-center justify-content-center"
            data-bs-toggle="tooltip"
            style="width: 32px; height: 32px;"
            aria-label="{{ $deleteMessage }}"
            data-bs-original-title="{{ $deleteMessage }}"
            disabled>
      <i class="fa fa-lock" style="font-size: 14px;"></i>
    </button>
    @endif
  @endif
</div>

{{-- CSS Styles --}}
@push('styles')
<style>
.btn {
  border-radius: 6px !important;
  transition: all 0.2s ease;
}
.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.btn i {
  line-height: 1;
}
</style>
@endpush

{{-- Tooltip Script --}}
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize tooltips
  var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
});
</script>
@endpush
