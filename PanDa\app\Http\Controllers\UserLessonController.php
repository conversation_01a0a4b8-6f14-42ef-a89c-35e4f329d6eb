<?php

namespace App\Http\Controllers;

use App\Models\UserCourse;
use App\Models\Lesson;
use App\Models\Chapter;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UserLessonController extends Controller
{
    /**
     * Hiển Thị Bài <PERSON>ới Pattern URL Mới
     */
    public function show($courseId, $chapterId, $lessonId)
    {
        // Kiểm tra user đã đăng ký khóa học
        $userCourse = auth()->user()->userCourses()
            ->with(['course.chapters.lessons'])
            ->where('course_id', $courseId)
            ->firstOrFail();

        // Kiểm tra lesson tồn tại và thuộc đúng course/chapter
        $lesson = Lesson::with(['chapter.course', 'comments.user', 'comments.replies.user'])->findOrFail($lessonId);
        
        if ($lesson->chapter_id != $chapterId || $lesson->chapter->course_id != $courseId) {
            abort(404, '<PERSON><PERSON><PERSON>ọc Hoặc <PERSON>ương Nà<PERSON>');
        }

        // Cập nhật current lesson và last accessed
        $userCourse->update([
            'current_lesson_id' => $lessonId,
            'last_accessed_at' => now()
        ]);

        // Bắt đầu học nếu chưa bắt đầu
        if (!$userCourse->started_at) {
            $userCourse->startLearning();
        }

        // Lấy danh sách bài học trong chapter để navigation
        $chapterLessons = $lesson->chapter->lessons()->active()->orderBy('lessons.position')->get();

        // Tìm bài học trước và sau
        $currentIndex = $chapterLessons->search(function ($item) use ($lesson) {
            return $item->id === $lesson->id;
        });

        $previousLesson = $currentIndex > 0 ? $chapterLessons[$currentIndex - 1] : null;
        $nextLesson = $currentIndex < $chapterLessons->count() - 1 ? $chapterLessons[$currentIndex + 1] : null;

        // Nếu không có bài tiếp theo trong chapter hiện tại, tìm bài đầu tiên của chapter tiếp theo
        if (!$nextLesson) {
            $nextChapter = $userCourse->course->chapters()
                ->where('chapters.position', '>', $lesson->chapter->position)
                ->orderBy('chapters.position')
                ->first();

            if ($nextChapter) {
                $nextLesson = $nextChapter->lessons()->active()->orderBy('lessons.position')->first();
            }
        }

        // Nếu không có bài trước trong chapter hiện tại, tìm bài cuối của chapter trước
        if (!$previousLesson) {
            $previousChapter = $userCourse->course->chapters()
                ->where('chapters.position', '<', $lesson->chapter->position)
                ->orderBy('chapters.position', 'desc')
                ->first();

            if ($previousChapter) {
                $previousLesson = $previousChapter->lessons()->active()->orderBy('lessons.position', 'desc')->first();
            }
        }

        $course = $userCourse->course;
        $chapter = $lesson->chapter;
        return view('user.lessons.show', compact('userCourse', 'course', 'lesson', 'chapter', 'previousLesson', 'nextLesson', 'chapterLessons'));
    }

    /**
     * Đánh Dấu Bài Học Hoàn Thành
     */
    public function complete($courseId, $chapterId, $lessonId)
    {
        // Kiểm tra user đã đăng ký khóa học
        $userCourse = auth()->user()->userCourses()
            ->where('course_id', $courseId)
            ->firstOrFail();

        // Kiểm tra lesson tồn tại và thuộc đúng course/chapter
        $lesson = Lesson::with(['chapter'])->findOrFail($lessonId);
        
        if ($lesson->chapter_id != $chapterId || $lesson->chapter->course_id != $courseId) {
            abort(404, 'Bài Học Không Thuộc Khóa Học Hoặc Chương Này');
        }

        // Đánh dấu hoàn thành
        $userCourse->updateProgress($lesson->id);

        // Tìm bài học tiếp theo
        $nextLesson = $this->getNextLesson($userCourse, $lesson);

        if ($nextLesson) {
            return redirect()->route('user.courses.lessons.show', [
                'course' => $courseId,
                'chapter' => $nextLesson->chapter_id,
                'lesson' => $nextLesson->id
            ])->with('success', 'Hoàn Thành Bài Học! Chuyển Đến Bài Tiếp Theo.');
        }

        return redirect()->route('my-courses.show', $courseId)
            ->with('success', 'Chúc Mừng! Bạn Đã Hoàn Thành Tất Cả Bài Học!');
    }

    /**
     * Lấy Bài Học Tiếp Theo
     */
    private function getNextLesson($userCourse, $currentLesson)
    {
        // Tìm bài học tiếp theo trong cùng chapter
        $nextLessonInChapter = $currentLesson->chapter->lessons()
            ->where('lessons.position', '>', $currentLesson->position)
            ->orderBy('lessons.position')
            ->first();

        if ($nextLessonInChapter) {
            return $nextLessonInChapter;
        }

        // Tìm bài học đầu tiên của chapter tiếp theo
        $nextChapter = $userCourse->course->chapters()
            ->where('chapters.position', '>', $currentLesson->chapter->position)
            ->orderBy('chapters.position')
            ->first();

        if ($nextChapter) {
            return $nextChapter->lessons()->orderBy('lessons.position')->first();
        }

        return null;
    }

    /**
     * Download Exercise File
     */
    public function downloadFile($courseId, $chapterId, $lessonId, $fileIndex)
    {
        $lesson = Lesson::with(['chapter.course'])->findOrFail($lessonId);

        // Kiểm tra quyền truy cập
        $userCourse = auth()->user()->userCourses()
            ->where('course_id', $lesson->chapter->course_id)
            ->first();

        if (!$userCourse) {
            abort(403, 'Bạn chưa đăng ký khóa học này');
        }

        // Kiểm tra file tồn tại
        if (!$lesson->exercise_files || !isset($lesson->exercise_files[$fileIndex])) {
            abort(404, 'File không tồn tại');
        }

        $file = $lesson->exercise_files[$fileIndex];
        $filePath = storage_path('app/public/' . $file['path']);

        if (!file_exists($filePath)) {
            abort(404, 'File không tồn tại trên server');
        }

        return response()->download($filePath, $file['name']);
    }

    /**
     * Download PDF File
     */
    public function downloadPdf($courseId, $chapterId, $lessonId)
    {
        $lesson = Lesson::with(['chapter.course'])->findOrFail($lessonId);

        // Kiểm tra quyền truy cập
        $userCourse = auth()->user()->userCourses()
            ->where('course_id', $lesson->chapter->course_id)
            ->first();

        if (!$userCourse) {
            abort(403, 'Bạn chưa đăng ký khóa học này');
        }

        if (!$lesson->file_path) {
            abort(404, 'File PDF không tồn tại');
        }

        $filePath = storage_path('app/public/' . $lesson->file_path);

        if (!file_exists($filePath)) {
            abort(404, 'File không tồn tại trên server');
        }

        return response()->download($filePath, $lesson->title . '.pdf');
    }
}
