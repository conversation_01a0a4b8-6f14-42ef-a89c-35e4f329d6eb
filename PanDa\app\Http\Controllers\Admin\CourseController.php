<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CourseController extends Controller
{
    /**
     * Hiển Thị <PERSON>h Sách <PERSON>h<PERSON>a Học
     */
    public function index(Request $request)
    {
        $query = Course::with('category');

        // Tìm <PERSON>
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Lọc Theo Category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Lọc Theo Status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $courses = $query->latest()->paginate(10);
        $categories = Category::active()->get();

        return view('admin.courses.index', compact('courses', 'categories'));
    }

    /**
     * Hiển Thị Form Tạo Khóa Học
     */
    public function create()
    {
        $categories = Category::active()->get();
        return view('admin.courses.create', compact('categories'));
    }

    /**
     * Lưu Khóa Học Mới
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'price' => 'required|numeric|min:0',
            'level' => 'required|in:beginner,intermediate,advanced',
            'duration_hours' => 'required|integer|min:1',
            'requirements' => 'nullable|string',
            'what_you_learn' => 'nullable|string',
            'image_url' => 'nullable|url',
        ], [
            'title.required' => 'Tiêu Đề Là Bắt Buộc',
            'description.required' => 'Mô Tả Là Bắt Buộc',
            'category_id.required' => 'Danh Mục Là Bắt Buộc',
            'price.required' => 'Giá Là Bắt Buộc',
            'price.numeric' => 'Giá Phải Là Số',
            'level.required' => 'Cấp Độ Là Bắt Buộc',
            'duration_hours.required' => 'Thời Lượng Là Bắt Buộc',
            'image_url.url' => 'URL Hình Ảnh Không Hợp Lệ',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();
        $data['slug'] = Str::slug($request->title);

        Course::create($data);

        return redirect()->route('admin.courses.index')
            ->with('success', 'Tạo Khóa Học Thành Công!');
    }

    /**
     * Hiển Thị Chi Tiết Khóa Học
     */
    public function show($id)
    {
        $course = Course::with(['category', 'chapters.lessons'])->findOrFail($id);
        return view('admin.courses.show', compact('course'));
    }

    /**
     * Hiển Thị Form Chỉnh Sửa
     */
    public function edit($id)
    {
        $course = Course::findOrFail($id);
        $categories = Category::active()->get();
        return view('admin.courses.edit', compact('course', 'categories'));
    }

    /**
     * Cập Nhật Khóa Học
     */
    public function update(Request $request, $id)
    {
        $course = Course::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'price' => 'required|numeric|min:0',
            'level' => 'required|in:beginner,intermediate,advanced',
            'duration_hours' => 'required|integer|min:1',
            'requirements' => 'nullable|string',
            'what_you_learn' => 'nullable|string',
            'image_url' => 'nullable|url',
        ], [
            'title.required' => 'Tiêu Đề Là Bắt Buộc',
            'description.required' => 'Mô Tả Là Bắt Buộc',
            'category_id.required' => 'Danh Mục Là Bắt Buộc',
            'price.required' => 'Giá Là Bắt Buộc',
            'level.required' => 'Cấp Độ Là Bắt Buộc',
            'duration_hours.required' => 'Thời Lượng Là Bắt Buộc',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();
        $data['slug'] = Str::slug($request->title);
        $data['is_completed'] = $request->has('is_completed');

        // Kiểm tra thay đổi is_completed
        $oldIsCompleted = $course->is_completed;
        $newIsCompleted = $data['is_completed'];

        $course->update($data);

        // Nếu admin thay đổi trạng thái hoàn thành, cập nhật lại user courses
        if ($oldIsCompleted !== $newIsCompleted) {
            $this->updateUserCourseCompletions($course);
        }

        return redirect()->route('admin.courses.index')
            ->with('success', 'Cập Nhật Khóa Học Thành Công!');
    }

    /**
     * Xóa Khóa Học
     */
    public function destroy($id)
    {
        $course = Course::findOrFail($id);

        // Không cần xóa hình ảnh vì dùng URL

        $course->delete();

        return redirect()->route('admin.courses.index')
            ->with('success', 'Xóa Khóa Học Thành Công!');
    }

    /**
     * Cập Nhật Trạng Thái Hoàn Thành Của User Courses
     */
    private function updateUserCourseCompletions($course)
    {
        $userCourses = $course->userCourses()->get();

        foreach ($userCourses as $userCourse) {
            $totalLessons = $course->total_lessons;
            $completedLessons = count($userCourse->completed_lessons ?? []);
            $progressPercentage = $totalLessons > 0 ?
                round(($completedLessons / $totalLessons) * 100) : 0;

            // Logic hoàn thành: 100% bài học VÀ admin đã đánh dấu
            $shouldComplete = $progressPercentage >= 100 && $course->is_completed;

            if ($shouldComplete && !$userCourse->completed_at) {
                // Đánh dấu hoàn thành và tạo certificate
                $userCourse->update(['completed_at' => now()]);
                \App\Models\Certificate::createForUserCourse($userCourse);
            } elseif (!$shouldComplete && $userCourse->completed_at) {
                // Reset hoàn thành nếu không đủ điều kiện
                $userCourse->update(['completed_at' => null]);

                // Xóa certificate nếu có
                \App\Models\Certificate::where('user_course_id', $userCourse->id)->delete();
            }
        }
    }
}
