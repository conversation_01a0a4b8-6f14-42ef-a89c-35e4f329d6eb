@extends('layouts.admin')

@section('title', 'Quản <PERSON> - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0"><PERSON><PERSON><PERSON><PERSON></h4>
        <p class="text-muted mb-0"><PERSON><PERSON></p>
      </div>
      <div>
        <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
          <i class="fa fa-plus me-2"></i>Thêm <PERSON>
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Bộ Lọc -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" action="{{ route('admin.categories.index') }}">
          <div class="row">
            <div class="col-md-8 mb-3">
              <label class="form-label">Tìm Kiếm</label>
              <input type="text" class="form-control" name="search" value="{{ request('search') }}" placeholder="Nhập Tên Danh Mục...">
            </div>
            
            <div class="col-md-4 mb-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fa fa-search me-2"></i>Tìm Kiếm
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Danh Sách Danh Mục -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Danh Sách Danh Mục ({{ $categories->total() }})
        </h5>
      </div>
      <div class="card-body p-0">
        @if($categories->count() > 0)
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Hình Ảnh</th>
                <th>Tên Danh Mục</th>
                <th>Mô Tả</th>
                <th>Số Khóa Học</th>
                <th>Trạng Thái</th>
                <th>Ngày Tạo</th>
                <th>Thao Tác</th>
              </tr>
            </thead>
            <tbody>
              @foreach($categories as $category)
              <tr>
                <td>
                  @if($category->image_url)
                  <img src="{{ $category->image_url }}" alt="{{ $category->name }}" class="rounded" width="60" height="40" style="object-fit: cover;">
                  @else
                  <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 60px; height: 40px;">
                    <i class="fa fa-list text-muted"></i>
                  </div>
                  @endif
                </td>
                
                <td>
                  <div>
                    <h6 class="mb-1">
                      <a href="{{ route('admin.categories.show', $category->id) }}" class="text-decoration-none">
                        {{ $category->name }}
                      </a>
                    </h6>
                    <small class="text-muted">{{ $category->slug }}</small>
                  </div>
                </td>
                
                <td>
                  <span class="text-muted">{{ Str::limit($category->description, 50) }}</span>
                </td>
                
                <td>
                  <span class="badge bg-primary">{{ $category->courses_count }} Khóa Học</span>
                </td>
                
                <td>
                  @if($category->is_active)
                  <span class="badge bg-success">Hoạt Động</span>
                  @else
                  <span class="badge bg-secondary">Ẩn</span>
                  @endif
                </td>
                
                <td>
                  <small class="text-muted">{{ $category->created_at->format('d/m/Y') }}</small>
                </td>
                
                <td>
                  @include('admin.partials.action-buttons', [
                    'showRoute' => route('admin.categories.show', $category->id),
                    'editRoute' => route('admin.categories.edit', $category->id),
                    'deleteRoute' => route('admin.categories.destroy', $category->id),
                    'canDelete' => $category->courses_count == 0,
                    'deleteMessage' => $category->courses_count > 0 ? 'Không Thể Xóa - Có Khóa Học' : 'Bạn Có Chắc Muốn Xóa?'
                  ])
                </td>
              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
        @else
        <div class="text-center py-5">
          <i class="fa fa-list fa-4x text-muted mb-3"></i>
          <h5 class="text-muted mb-3">Chưa Có Danh Mục Nào</h5>
          <p class="text-muted mb-4">Bắt Đầu Tạo Danh Mục Đầu Tiên</p>
          <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
            <i class="fa fa-plus me-2"></i>Tạo Danh Mục
          </a>
        </div>
        @endif
      </div>
    </div>
  </div>
</div>

<!-- Phân Trang -->
@if($categories->hasPages())
<div class="row mt-4">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      {{ $categories->appends(request()->query())->links() }}
    </div>
  </div>
</div>
@endif

<!-- Thống Kê -->
<div class="row mt-4">
  <div class="col-md-4">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $categories->total() }}</h4>
        <small>Tổng Danh Mục</small>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $categories->where('is_active', true)->count() }}</h4>
        <small>Đang Hoạt Động</small>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $categories->sum('courses_count') }}</h4>
        <small>Tổng Khóa Học</small>
      </div>
    </div>
  </div>
</div>
@endsection
