@extends('layouts.admin')

@section('title', '<PERSON><PERSON>ả<PERSON> - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0"><PERSON><PERSON><PERSON><PERSON></h4>
        @if($course)
        <p class="text-muted mb-0">Khó<PERSON>: {{ $course->title }}</p>
        @else
        <p class="text-muted mb-0">Tất Cả Chương Trong Hệ Thống</p>
        @endif
      </div>
      <div>
        <a href="{{ route('admin.chapters.create', ['course_id' => $course?->id]) }}" class="btn btn-primary">
          <i class="fa fa-plus me-2"></i>Thêm Chương
        </a>
      </div>
    </div>
  </div>
</div>

<!-- <PERSON><PERSON> -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" action="{{ route('admin.chapters.index') }}">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label class="form-label">Khóa Học</label>
              <select class="form-select" name="course_id" onchange="this.form.submit()">
                <option value="">Tất Cả Khóa Học</option>
                @foreach($courses as $courseOption)
                <option value="{{ $courseOption->id }}" {{ request('course_id') == $courseOption->id ? 'selected' : '' }}>
                  {{ $courseOption->title }}
                </option>
                @endforeach
              </select>
            </div>
            
            @if($course)
            <div class="col-md-6 mb-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <a href="{{ route('admin.courses.show', $course->id) }}" class="btn btn-outline-info">
                  <i class="fa fa-eye me-2"></i>Xem Chi Tiết Khóa Học
                </a>
              </div>
            </div>
            @endif
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Danh Sách Chương -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Danh Sách Chương ({{ $chapters->total() }})
        </h5>
      </div>
      <div class="card-body p-0">
        @if($chapters->count() > 0)
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Vị Trí</th>
                <th>Tên Chương</th>
                <th>Khóa Học</th>
                <th>Số Bài Học</th>
                <th>Trạng Thái</th>
                <th>Ngày Tạo</th>
                <th>Thao Tác</th>
              </tr>
            </thead>
            <tbody>
              @foreach($chapters as $chapter)
              <tr>
                <td>
                  <span class="badge bg-primary">{{ $chapter->position }}</span>
                </td>
                
                <td>
                  <div>
                    <h6 class="mb-1">
                      <a href="{{ route('admin.chapters.show', $chapter->id) }}" class="text-decoration-none">
                        {{ $chapter->title }}
                      </a>
                    </h6>
                    @if($chapter->description)
                    <small class="text-muted">{{ Str::limit($chapter->description, 50) }}</small>
                    @endif
                  </div>
                </td>
                
                <td>
                  <span class="badge bg-info">{{ $chapter->course->title ?? 'N/A' }}</span>
                </td>
                
                <td>
                  <span class="badge bg-success">{{ $chapter->lessons_count }} Bài</span>
                </td>
                
                <td>
                  @if($chapter->is_active)
                  <span class="badge bg-success">Hoạt Động</span>
                  @else
                  <span class="badge bg-secondary">Ẩn</span>
                  @endif
                </td>
                
                <td>
                  <small class="text-muted">{{ $chapter->created_at->format('d/m/Y') }}</small>
                </td>
                
                <td>
                  @include('admin.partials.action-buttons', [
                    'showRoute' => route('admin.chapters.show', $chapter->id),
                    'editRoute' => route('admin.chapters.edit', $chapter->id),
                    'deleteRoute' => route('admin.chapters.destroy', $chapter->id),
                    'canDelete' => $chapter->lessons_count == 0,
                    'deleteMessage' => $chapter->lessons_count > 0 ? 'Không Thể Xóa - Có Bài Học' : 'Bạn Có Chắc Muốn Xóa?',
                    'extraButtons' => [
                      [
                        'route' => route('admin.lessons.index', ['chapter_id' => $chapter->id]),
                        'icon' => 'play-circle',
                        'color' => 'primary',
                        'title' => 'Quản Lý Bài Học',
                        'position' => 'before-edit'
                      ]
                    ]
                  ])
                </td>
              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
        @else
        <div class="text-center py-5">
          <i class="fa fa-folder fa-4x text-muted mb-3"></i>
          <h5 class="text-muted mb-3">Chưa Có Chương Nào</h5>
          @if($course)
          <p class="text-muted mb-4">Bắt Đầu Tạo Chương Đầu Tiên Cho Khóa Học "{{ $course->title }}"</p>
          <a href="{{ route('admin.chapters.create', ['course_id' => $course->id]) }}" class="btn btn-primary">
            <i class="fa fa-plus me-2"></i>Tạo Chương Đầu Tiên
          </a>
          @else
          <p class="text-muted mb-4">Chọn Khóa Học Để Xem Chương Hoặc Tạo Chương Mới</p>
          <a href="{{ route('admin.chapters.create') }}" class="btn btn-primary">
            <i class="fa fa-plus me-2"></i>Tạo Chương
          </a>
          @endif
        </div>
        @endif
      </div>
    </div>
  </div>
</div>

<!-- Phân Trang -->
@if($chapters->hasPages())
<div class="row mt-4">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      {{ $chapters->appends(request()->query())->links() }}
    </div>
  </div>
</div>
@endif

<!-- Thống Kê -->
@if($course)
<div class="row mt-4">
  <div class="col-md-4">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $chapters->total() }}</h4>
        <small>Tổng Chương</small>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $chapters->where('is_active', true)->count() }}</h4>
        <small>Đang Hoạt Động</small>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $chapters->sum('lessons_count') }}</h4>
        <small>Tổng Bài Học</small>
      </div>
    </div>
  </div>
</div>
@endif
@endsection
