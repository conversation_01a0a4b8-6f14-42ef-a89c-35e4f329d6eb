<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Chapter;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ChapterController extends Controller
{
    /**
     * Hi<PERSON><PERSON>
     */
    public function index(Request $request)
    {
        $courseId = $request->get('course_id');
        $course = null;

        if ($courseId) {
            $course = Course::findOrFail($courseId);
            $chapters = $course->chapters()->withCount('lessons')->orderBy('position')->paginate(10);
        } else {
            $chapters = Chapter::with('course')->withCount('lessons')->latest()->paginate(10);
        }

        $courses = Course::all();

        return view('admin.chapters.index', compact('chapters', 'courses', 'course'));
    }

    /**
     * <PERSON><PERSON><PERSON>hị Form Tạo Chương
     */
    public function create(Request $request)
    {
        $courseId = $request->get('course_id');
        $course = $courseId ? Course::findOrFail($courseId) : null;
        $courses = Course::all();

        return view('admin.chapters.create', compact('courses', 'course'));
    }

    /**
     * Lưu Chương Mới
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'position' => 'required|integer|min:1',
        ], [
            'course_id.required' => 'Khóa Học Là Bắt Buộc',
            'title.required' => 'Tiêu Đề Chương Là Bắt Buộc',
            'position.required' => 'Vị Trí Là Bắt Buộc',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        Chapter::create($request->all());

        return redirect()->route('admin.chapters.index', ['course_id' => $request->course_id])
            ->with('success', 'Tạo Chương Thành Công!');
    }

    /**
     * Hiển Thị Chi Tiết Chương
     */
    public function show($id)
    {
        $chapter = Chapter::with(['course', 'lessons'])->findOrFail($id);
        return view('admin.chapters.show', compact('chapter'));
    }

    /**
     * Hiển Thị Form Chỉnh Sửa
     */
    public function edit($id)
    {
        $chapter = Chapter::with('course')->findOrFail($id);
        $courses = Course::all();
        return view('admin.chapters.edit', compact('chapter', 'courses'));
    }

    /**
     * Cập Nhật Chương
     */
    public function update(Request $request, $id)
    {
        $chapter = Chapter::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'position' => 'required|integer|min:1',
        ], [
            'course_id.required' => 'Khóa Học Là Bắt Buộc',
            'title.required' => 'Tiêu Đề Chương Là Bắt Buộc',
            'position.required' => 'Vị Trí Là Bắt Buộc',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $chapter->update($request->all());

        return redirect()->route('admin.chapters.index', ['course_id' => $chapter->course_id])
            ->with('success', 'Cập Nhật Chương Thành Công!');
    }

    /**
     * Xóa Chương
     */
    public function destroy($id)
    {
        $chapter = Chapter::findOrFail($id);
        $courseId = $chapter->course_id;

        // Kiểm tra có bài học không
        if ($chapter->lessons()->count() > 0) {
            return redirect()->route('admin.chapters.index', ['course_id' => $courseId])
                ->with('error', 'Không Thể Xóa Chương Có Bài Học!');
        }

        $chapter->delete();

        return redirect()->route('admin.chapters.index', ['course_id' => $courseId])
            ->with('success', 'Xóa Chương Thành Công!');
    }
}
