<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="csrf-token" content="{{ csrf_token() }}">

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="{{ asset('assets/images/logos/favicon.png') }}" />

  <!-- Core Css -->
  <link rel="stylesheet" href="{{ asset('assets/css/styles.css') }}" />

  <!-- Custom Css -->
  <link rel="stylesheet" href="{{ asset('assets/css/custom.css') }}" />

  <title>Qu<PERSON><PERSON>u - <PERSON><PERSON> T<PERSON>ống <PERSON></title>
</head>

<body>
  <!-- Preloader -->
  <div class="preloader">
    <img src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/images/logos/favicon.png') }}" alt="loader" class="lds-ripple img-fluid" />
  </div>
  <div id="main-wrapper">
    <div class="position-relative overflow-hidden auth-bg min-vh-100 w-100 d-flex align-items-center justify-content-center">
      <div class="d-flex align-items-center justify-content-center w-100">
        <div class="row justify-content-center w-100 my-5 my-xl-0">
          <div class="col-md-6 d-flex flex-column justify-content-center">
            <div class="card mb-0 bg-body auth-login m-auto w-100">
              <div class="row gx-0">
                <div class="col-12">
                  <div class="row justify-content-center py-4">
                    <div class="col-lg-11">
                      <div class="card-body">
                        <a href="{{ route('home') }}" class="text-nowrap logo-img d-block mb-4 w-100 text-center">
                          <img src="{{ asset('assets/images/logos/logo.svg') }}" class="dark-logo" alt="Logo-Dark" />
                        </a>
                        
                        <div class="text-center mb-4">
                          <h2 class="lh-base mb-3">Quên Mật Khẩu?</h2>
                          <p class="fs-4 text-muted">Nhập Email Của Bạn Để Nhận Link Đặt Lại Mật Khẩu</p>
                        </div>
                        
                        @if (session('status'))
                        <div class="alert alert-success" role="alert">
                          {{ session('status') }}
                        </div>
                        @endif

                        @if ($errors->any())
                        <div class="alert alert-danger">
                          <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                            @endforeach
                          </ul>
                        </div>
                        @endif
                        
                        <form method="POST" action="{{ route('password.email') }}">
                          @csrf
                          <div class="mb-4">
                            <label for="email" class="form-label">Địa Chỉ Email</label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" placeholder="Nhập Email Của Bạn" required autofocus>
                            @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                          </div>
                          
                          <button type="submit" class="btn btn-primary w-100 py-8 mb-4 rounded-1">
                            <i class="fa fa-code me-2"></i>Gửi Link Đặt Lại Mật Khẩu
                          </button>
                          
                          <div class="d-flex align-items-center justify-content-center">
                            <a class="text-primary fw-bolder" href="{{ route('login') }}">
                              <i class="fa fa-arrow-left me-2"></i>Quay Lại Đăng Nhập
                            </a>
                          </div>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="dark-transparent sidebartoggler"></div>
  
  <!-- Import Js Files -->
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/libs/simplebar/dist/simplebar.min.js') }}"></script>
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/js/theme/app.init.js') }}"></script>
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/js/theme/theme.js') }}"></script>
  <script src="{{ asset('bootstrapdemos.adminmart.com/matdash/dist/assets/js/theme/app.min.js') }}"></script>

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  
  <!-- Solar Icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
</body>

</html>
