@extends('layouts.admin')

@section('title', 'Quản L<PERSON> - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Quản L<PERSON></h4>
        @if($chapter)
        <p class="text-muted mb-0">Chương: {{ $chapter->title }} ({{ $chapter->course->title }})</p>
        @else
        <p class="text-muted mb-0">Tất <PERSON><PERSON> B<PERSON>ố<PERSON></p>
        @endif
      </div>
      <div>
        <a href="{{ route('admin.lessons.create', ['chapter_id' => $chapter?->id]) }}" class="btn btn-primary">
          <i class="fa fa-plus me-2"></i>Thê<PERSON>
        </a>
      </div>
    </div>
  </div>
</div>

<!-- <PERSON>ộ Lọc -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" action="{{ route('admin.lessons.index') }}">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label class="form-label">Chương</label>
              <select class="form-select" name="chapter_id" onchange="this.form.submit()">
                <option value="">Tất Cả Chương</option>
                @foreach($chapters as $chapterOption)
                <option value="{{ $chapterOption->id }}" {{ request('chapter_id') == $chapterOption->id ? 'selected' : '' }}>
                  {{ $chapterOption->course->title }} - {{ $chapterOption->title }}
                </option>
                @endforeach
              </select>
            </div>
            
            @if($chapter)
            <div class="col-md-6 mb-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <a href="{{ route('admin.chapters.show', $chapter->id) }}" class="btn btn-outline-info">
                  <i class="fa fa-eye me-2"></i>Xem Chi Tiết Chương
                </a>
              </div>
            </div>
            @endif
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Danh Sách Bài Học -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Danh Sách Bài Học ({{ $lessons->total() }})
        </h5>
      </div>
      <div class="card-body p-0">
        @if($lessons->count() > 0)
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Vị Trí</th>
                <th>Tên Bài Học</th>
                <th>Chương</th>
                <th>Loại</th>
                <th>Thời Lượng</th>
                <th>Trạng Thái</th>
                <th>Ngày Tạo</th>
                <th>Thao Tác</th>
              </tr>
            </thead>
            <tbody>
              @foreach($lessons as $lesson)
              <tr>
                <td>
                  <span class="badge bg-primary">{{ $lesson->position }}</span>
                </td>
                
                <td>
                  <div>
                    <h6 class="mb-1">
                      <a href="{{ route('admin.lessons.show', $lesson->id) }}" class="text-decoration-none">
                        {{ $lesson->title }}
                      </a>
                    </h6>
                    @if($lesson->is_preview)
                    <span class="badge bg-success">Preview</span>
                    @endif
                    @if($lesson->exercise_files && count($lesson->exercise_files) > 0)
                    <span class="badge bg-warning"><i class="fa fa-tasks me-1"></i>{{ count($lesson->exercise_files) }} Bài Tập</span>
                    @endif
                  </div>
                </td>
                
                <td>
                  <div>
                    <small class="text-muted">{{ $lesson->chapter->course->title ?? 'N/A' }}</small>
                    <div class="fw-bold">{{ $lesson->chapter->title ?? 'N/A' }}</div>
                  </div>
                </td>
                
                <td>
                  @if($lesson->type == 'video')
                  <span class="badge bg-primary"><i class="fa fa-play-circle me-1"></i>Video</span>
                  @elseif($lesson->type == 'text')
                  <span class="badge bg-success"><i class="fa fa-file-text me-1"></i>Văn Bản</span>
                  @elseif($lesson->type == 'pdf')
                  <span class="badge bg-danger"><i class="fa fa-file-pdf me-1"></i>PDF</span>
                  @else
                  <span class="badge bg-warning"><i class="fa fa-question-circle me-1"></i>Quiz</span>
                  @endif
                </td>
                
                <td>
                  <span class="text-muted">{{ $lesson->duration_minutes }} phút</span>
                </td>
                
                <td>
                  @if($lesson->is_active)
                  <span class="badge bg-success">Hoạt Động</span>
                  @else
                  <span class="badge bg-secondary">Ẩn</span>
                  @endif
                </td>
                
                <td>
                  <small class="text-muted">{{ $lesson->created_at->format('d/m/Y') }}</small>
                </td>
                
                <td>
                  @include('admin.partials.action-buttons', [
                    'showRoute' => route('admin.lessons.show', $lesson->id),
                    'editRoute' => route('admin.lessons.edit', $lesson->id),
                    'deleteRoute' => route('admin.lessons.destroy', $lesson->id),
                    'extraButtons' => [
                      [
                        'route' => route('lessons.show', $lesson->id),
                        'icon' => 'external-link-alt',
                        'color' => 'primary',
                        'title' => 'Xem Trang Công Khai',
                        'position' => 'before-edit',
                        'target' => '_blank'
                      ]
                    ]
                  ])
                </td>
              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
        @else
        <div class="text-center py-5">
          <i class="fa fa-play-circle fa-4x text-muted mb-3"></i>
          <h5 class="text-muted mb-3">Chưa Có Bài Học Nào</h5>
          @if($chapter)
          <p class="text-muted mb-4">Bắt Đầu Tạo Bài Học Đầu Tiên Cho Chương "{{ $chapter->title }}"</p>
          <a href="{{ route('admin.lessons.create', ['chapter_id' => $chapter->id]) }}" class="btn btn-primary">
            <i class="fa fa-plus me-2"></i>Tạo Bài Học Đầu Tiên
          </a>
          @else
          <p class="text-muted mb-4">Chọn Chương Để Xem Bài Học Hoặc Tạo Bài Học Mới</p>
          <a href="{{ route('admin.lessons.create') }}" class="btn btn-primary">
            <i class="fa fa-plus me-2"></i>Tạo Bài Học
          </a>
          @endif
        </div>
        @endif
      </div>
    </div>
  </div>
</div>

<!-- Phân Trang -->
@if($lessons->hasPages())
<div class="row mt-4">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      {{ $lessons->appends(request()->query())->links() }}
    </div>
  </div>
</div>
@endif

<!-- Thống Kê -->
@if($chapter)
<div class="row mt-4">
  <div class="col-md-3">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $lessons->total() }}</h4>
        <small>Tổng Bài Học</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $lessons->where('is_active', true)->count() }}</h4>
        <small>Đang Hoạt Động</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-warning text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $lessons->where('is_preview', true)->count() }}</h4>
        <small>Preview</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <h4 class="mb-0">{{ $lessons->sum('duration_minutes') }}</h4>
        <small>Tổng Phút</small>
      </div>
    </div>
  </div>
</div>
@endif
@endsection
