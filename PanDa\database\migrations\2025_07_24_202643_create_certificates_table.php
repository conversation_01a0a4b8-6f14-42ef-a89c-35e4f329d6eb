<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('certificates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('course_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_course_id')->constrained()->onDelete('cascade');
            $table->string('certificate_number')->unique();
            $table->string('certificate_code', 20)->unique(); // Mã ngắn để verify
            $table->timestamp('issued_at');
            $table->string('file_path')->nullable(); // Đường dẫn file PDF
            $table->json('certificate_data')->nullable(); // <PERSON><PERSON> liệu để generate certificate
            $table->boolean('is_verified')->default(true);
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();

            // Index
            $table->index(['user_id', 'course_id']);
            $table->index('certificate_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('certificates');
    }
};
