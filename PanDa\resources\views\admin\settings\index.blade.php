@extends('layouts.admin')

@section('title', 'Cài Đặt Hệ Thống')

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Cài Đặt Hệ Thống</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active">Cài Đặt</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <!-- Settings Menu Cards -->
  <div class="row">
    <!-- Cài Đặt Tổng Quan -->
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100 hover-card border-0 shadow-sm">
        <div class="card-body text-center p-4">
          <div class="settings-icon mb-4">
            <div class="icon-circle bg-primary bg-gradient">
              <i class="fa fa-cog fa-2x text-white"></i>
            </div>
          </div>
          <h5 class="card-title fw-bold">Cài Đặt Tổng Quan</h5>
          <p class="card-text text-muted small">
            Cấu hình thông tin cơ bản của website như tên, logo, thông tin liên hệ
          </p>
          <a href="{{ route('admin.settings.general') }}" class="btn btn-primary btn-sm px-4">
            <i class="fa fa-edit me-2"></i>Cấu Hình
          </a>
        </div>
      </div>
    </div>

    <!-- Cài Đặt Email -->
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100 hover-card border-0 shadow-sm">
        <div class="card-body text-center p-4">
          <div class="settings-icon mb-4">
            <div class="icon-circle bg-success bg-gradient">
              <i class="fa fa-envelope fa-2x text-white"></i>
            </div>
          </div>
          <h5 class="card-title fw-bold">Cài Đặt Email</h5>
          <p class="card-text text-muted small">
            Cấu hình SMTP, email templates và thông tin gửi email
          </p>
          <a href="{{ route('admin.settings.email') }}" class="btn btn-success btn-sm px-4">
            <i class="fa fa-mail-bulk me-2"></i>Cấu Hình
          </a>
        </div>
      </div>
    </div>

    <!-- Cài Đặt SEO -->
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100 hover-card border-0 shadow-sm">
        <div class="card-body text-center p-4">
          <div class="settings-icon mb-4">
            <div class="icon-circle bg-warning bg-gradient">
              <i class="fa fa-search fa-2x text-white"></i>
            </div>
          </div>
          <h5 class="card-title fw-bold">Cài Đặt SEO</h5>
          <p class="card-text text-muted small">
            Tối ưu SEO, Google Analytics, Meta tags và sitemap
          </p>
          <a href="{{ route('admin.settings.seo') }}" class="btn btn-warning btn-sm px-4">
            <i class="fa fa-chart-line me-2"></i>Cấu Hình
          </a>
        </div>
      </div>
    </div>

    <!-- Cài Đặt Bảo Mật -->
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100 hover-card border-0 shadow-sm">
        <div class="card-body text-center p-4">
          <div class="settings-icon mb-4">
            <div class="icon-circle bg-danger bg-gradient">
              <i class="fa fa-shield-alt fa-2x text-white"></i>
            </div>
          </div>
          <h5 class="card-title fw-bold">Cài Đặt Bảo Mật</h5>
          <p class="card-text text-muted small">
            Cấu hình bảo mật, 2FA, captcha và các chính sách bảo mật
          </p>
          <a href="{{ route('admin.settings.security') }}" class="btn btn-danger btn-sm px-4">
            <i class="fa fa-lock me-2"></i>Cấu Hình
          </a>
        </div>
      </div>
    </div>

    <!-- Cài Đặt Mạng Xã Hội -->
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100 hover-card border-0 shadow-sm">
        <div class="card-body text-center p-4">
          <div class="settings-icon mb-4">
            <div class="icon-circle bg-info bg-gradient">
              <i class="fa fa-share-alt fa-2x text-white"></i>
            </div>
          </div>
          <h5 class="card-title fw-bold">Mạng Xã Hội</h5>
          <p class="card-text text-muted small">
            Cấu hình liên kết mạng xã hội và đăng nhập social
          </p>
          <a href="{{ route('admin.settings.social') }}" class="btn btn-info btn-sm px-4">
            <i class="fa fa-users me-2"></i>Cấu Hình
          </a>
        </div>
      </div>
    </div>

    <!-- Cài Đặt Hệ Thống -->
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="card h-100 hover-card border-0 shadow-sm">
        <div class="card-body text-center p-4">
          <div class="settings-icon mb-4">
            <div class="icon-circle bg-secondary bg-gradient">
              <i class="fa fa-server fa-2x text-white"></i>
            </div>
          </div>
          <h5 class="card-title fw-bold">Cài Đặt Hệ Thống</h5>
          <p class="card-text text-muted small">
            Cấu hình cache, backup, logs và thông tin hệ thống
          </p>
          <a href="{{ route('admin.settings.system') }}" class="btn btn-secondary btn-sm px-4">
            <i class="fa fa-tools me-2"></i>Cấu Hình
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fa fa-bolt me-2"></i>Thao Tác Nhanh
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3 mb-3">
              <div class="d-grid">
                <form action="{{ route('admin.settings.clear-cache') }}" method="POST">
                  @csrf
                  <button type="submit" class="btn btn-outline-primary" onclick="return confirm('Bạn có chắc muốn xóa cache?')">
                    <i class="fa fa-trash me-2"></i>Xóa Cache
                  </button>
                </form>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="d-grid">
                <form action="{{ route('admin.settings.optimize') }}" method="POST">
                  @csrf
                  <button type="submit" class="btn btn-outline-success">
                    <i class="fa fa-rocket me-2"></i>Tối Ưu Hệ Thống
                  </button>
                </form>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="d-grid">
                <form action="{{ route('admin.settings.backup') }}" method="POST">
                  @csrf
                  <button type="submit" class="btn btn-outline-warning">
                    <i class="fa fa-download me-2"></i>Backup Database
                  </button>
                </form>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="d-grid">
                <a href="{{ route('admin.settings.system') }}" class="btn btn-outline-info">
                  <i class="fa fa-info-circle me-2"></i>Thông Tin Hệ Thống
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Recent Settings Changes -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fa fa-history me-2"></i>Thay Đổi Gần Đây
          </h5>
        </div>
        <div class="card-body">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-marker bg-primary"></div>
              <div class="timeline-content">
                <h6 class="timeline-title">Cập Nhật Cài Đặt Tổng Quan</h6>
                <p class="timeline-text">Thay đổi tên website và mô tả</p>
                <small class="text-muted">2 giờ trước</small>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-marker bg-success"></div>
              <div class="timeline-content">
                <h6 class="timeline-title">Cấu Hình Email SMTP</h6>
                <p class="timeline-text">Cập nhật thông tin máy chủ email</p>
                <small class="text-muted">1 ngày trước</small>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-marker bg-warning"></div>
              <div class="timeline-content">
                <h6 class="timeline-title">Tối Ưu Hệ Thống</h6>
                <p class="timeline-text">Xóa cache và tối ưu performance</p>
                <small class="text-muted">3 ngày trước</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.hover-card {
  transition: all 0.3s ease-in-out;
  border-radius: 15px !important;
}

.hover-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
}

.settings-icon {
  position: relative;
}

.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.icon-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
  border-radius: 50%;
}

.card-title {
  color: #2c3e50;
  margin-bottom: 15px;
}

.card-text {
  line-height: 1.6;
  margin-bottom: 20px;
}

.btn {
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #007bff, #6c757d);
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-marker {
  position: absolute;
  left: -22px;
  top: 5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 3px solid #fff;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.timeline-content {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 15px;
  border-radius: 10px;
  border-left: 4px solid #007bff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.timeline-title {
  margin-bottom: 5px;
  font-weight: 600;
  color: #2c3e50;
}

.timeline-text {
  margin-bottom: 5px;
  color: #6c757d;
}


</style>
@endsection
