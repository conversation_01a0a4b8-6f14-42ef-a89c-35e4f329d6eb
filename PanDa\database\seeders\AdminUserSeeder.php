<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Tạo Admin Mặc Định
        User::create([
            'name' => 'Quản Trị Viên',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '0123456789',
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Tạo User Thường Mẫu
        User::create([
            'name' => 'Người Dùng Mẫu',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '0987654321',
            'role' => 'user',
            'email_verified_at' => now(),
        ]);
    }
}
