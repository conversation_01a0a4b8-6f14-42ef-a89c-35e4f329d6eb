@extends('layouts.admin')

@section('title', 'Thê<PERSON> - Admin')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Thê<PERSON> Chương Mới</h4>
        @if($course)
        <p class="text-muted mb-0">Khó<PERSON> H<PERSON>: {{ $course->title }}</p>
        @else
        <p class="text-muted mb-0"><PERSON><PERSON><PERSON>ơng Cho Khóa Học</p>
        @endif
      </div>
      <div>
        <a href="{{ route('admin.chapters.index', ['course_id' => $course?->id]) }}" class="btn btn-outline-secondary">
          <i class="fa fa-arrow-left me-2"></i>Quay Lại
        </a>
      </div>
    </div>
  </div>
</div>

<form method="POST" action="{{ route('admin.chapters.store') }}">
  @csrf
  <div class="row">
    <div class="col-lg-8">
      <!-- Thông Tin Chương -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Thông Tin Chương
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-12 mb-3">
              <label for="course_id" class="form-label">Khóa Học <span class="text-danger">*</span></label>
              <select class="form-select @error('course_id') is-invalid @enderror" id="course_id" name="course_id" required>
                <option value="">Chọn Khóa Học</option>
                @foreach($courses as $courseOption)
                <option value="{{ $courseOption->id }}" {{ old('course_id', $course?->id) == $courseOption->id ? 'selected' : '' }}>
                  {{ $courseOption->title }}
                </option>
                @endforeach
              </select>
              @error('course_id')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-12 mb-3">
              <label for="title" class="form-label">Tiêu Đề Chương <span class="text-danger">*</span></label>
              <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title') }}" placeholder="Nhập Tiêu Đề Chương" required>
              @error('title')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
            
            <div class="col-12 mb-3">
              <label for="description" class="form-label">Mô Tả Chương</label>
              <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="4" placeholder="Nhập Mô Tả Chương">{{ old('description') }}</textarea>
              @error('description')
              <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-4">
      <!-- Cài Đặt -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fa fa-code me-2"></i>Cài Đặt
          </h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label for="position" class="form-label">Vị Trí <span class="text-danger">*</span></label>
            <input type="number" class="form-control @error('position') is-invalid @enderror" id="position" name="position" value="{{ old('position', 1) }}" min="1" required>
            @error('position')
            <div class="invalid-feedback">{{ $message }}</div>
            @enderror
            <div class="form-text">Thứ Tự Hiển Thị Của Chương</div>
          </div>
          
          <div class="mb-3">
            <label for="is_active" class="form-label">Trạng Thái</label>
            <select class="form-select" id="is_active" name="is_active">
              <option value="1" {{ old('is_active', '1') == '1' ? 'selected' : '' }}>Hoạt Động</option>
              <option value="0" {{ old('is_active') == '0' ? 'selected' : '' }}>Ẩn</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Hành Động -->
      <div class="card">
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary">
              <i class="fa fa-save me-2"></i>Lưu Chương
            </button>
            <a href="{{ route('admin.chapters.index', ['course_id' => $course?->id]) }}" class="btn btn-outline-secondary">
              <i class="fa fa-times me-2"></i>Hủy
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
@endsection
