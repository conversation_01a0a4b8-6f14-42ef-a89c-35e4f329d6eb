<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\LessonController;
use App\Http\Controllers\CartController;

// API Routes Công Khai
Route::prefix('auth')->group(function () {
    Route::post('/register', [AuthController::class, 'apiRegister']);
    Route::post('/login', [AuthController::class, 'apiLogin']);
});

// API Routes Công Khai Cho Khóa Học
Route::get('/courses', [CourseController::class, 'apiIndex']);
Route::get('/courses/{slug}', [CourseController::class, 'apiShow']);
Route::get('/lessons/{id}', [LessonController::class, 'apiShow']);

// API Routes Yêu Cầu <PERSON>c
Route::middleware('auth:sanctum')->group(function () {
    // Thông Tin User
    Route::get('/user', [AuthController::class, 'user']);
    Route::put('/user', [AuthController::class, 'apiUpdateProfile']);

    // Đăng Xuất
    Route::post('/auth/logout', [AuthController::class, 'apiLogout']);

    // Giỏ Hàng
    Route::get('/cart', [CartController::class, 'apiIndex']);
    Route::post('/cart', [CartController::class, 'apiStore']);
    Route::delete('/cart/{id}', [CartController::class, 'apiDestroy']);
});
