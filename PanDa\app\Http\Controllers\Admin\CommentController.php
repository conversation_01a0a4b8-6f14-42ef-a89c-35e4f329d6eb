<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Comment;
use App\Models\Lesson;
use App\Models\User;
use Illuminate\Http\Request;

class CommentController extends Controller
{
    /**
     * <PERSON>h <PERSON>
     */
    public function index(Request $request)
    {
        $query = Comment::with(['user', 'lesson.chapter.course']);

        // Tìm kiếm
        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('content', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function($q2) use ($request) {
                      $q2->where('name', 'like', '%' . $request->search . '%')
                         ->orWhere('email', 'like', '%' . $request->search . '%');
                  })
                  ->orWhereHas('lesson', function($q2) use ($request) {
                      $q2->where('title', 'like', '%' . $request->search . '%');
                  });
            });
        }

        // Lọc theo trạng thái
        if ($request->status) {
            if ($request->status == 'approved') {
                $query->approved();
            } elseif ($request->status == 'pending') {
                $query->pending();
            }
        }

        // Lọc theo loại
        if ($request->type) {
            if ($request->type == 'parent') {
                $query->whereNull('parent_id');
            } elseif ($request->type == 'reply') {
                $query->whereNotNull('parent_id');
            }
        }

        // Lọc theo user
        if ($request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        // Lọc theo lesson
        if ($request->lesson_id) {
            $query->where('lesson_id', $request->lesson_id);
        }

        $comments = $query->latest()->paginate(20);
        $users = User::orderBy('name')->get();
        $lessons = Lesson::with('chapter.course')->orderBy('title')->get();

        return view('admin.comments.index', compact('comments', 'users', 'lessons'));
    }

    /**
     * Chi Tiết Bình Luận
     */
    public function show($id)
    {
        $comment = Comment::with(['user', 'lesson.chapter.course', 'replies.user'])->findOrFail($id);

        return view('admin.comments.show', compact('comment'));
    }

    /**
     * Duyệt Bình Luận
     */
    public function approve($id)
    {
        $comment = Comment::findOrFail($id);
        $comment->update(['is_approved' => true]);

        return back()->with('success', 'Đã Duyệt Bình Luận Thành Công!');
    }

    /**
     * Từ Chối Bình Luận
     */
    public function reject($id)
    {
        $comment = Comment::findOrFail($id);
        $comment->update(['is_approved' => false]);

        return back()->with('success', 'Đã Từ Chối Bình Luận!');
    }

    /**
     * Xóa Bình Luận
     */
    public function destroy($id)
    {
        $comment = Comment::findOrFail($id);

        // Xóa các reply trước
        $comment->replies()->delete();

        // Xóa comment chính
        $comment->delete();

        return back()->with('success', 'Đã Xóa Bình Luận Thành Công!');
    }

    /**
     * Duyệt Hàng Loạt
     */
    public function bulkApprove(Request $request)
    {
        $commentIds = $request->comment_ids;

        if ($commentIds) {
            Comment::whereIn('id', $commentIds)->update(['is_approved' => true]);
            return back()->with('success', 'Đã Duyệt ' . count($commentIds) . ' Bình Luận!');
        }

        return back()->with('error', 'Vui Lòng Chọn Bình Luận!');
    }

    /**
     * Xóa Hàng Loạt
     */
    public function bulkDelete(Request $request)
    {
        $commentIds = $request->comment_ids;

        if ($commentIds) {
            // Xóa replies trước
            Comment::whereIn('parent_id', $commentIds)->delete();

            // Xóa comments chính
            Comment::whereIn('id', $commentIds)->delete();

            return back()->with('success', 'Đã Xóa ' . count($commentIds) . ' Bình Luận!');
        }

        return back()->with('error', 'Vui Lòng Chọn Bình Luận!');
    }

    /**
     * Thống Kê Bình Luận
     */
    public function stats()
    {
        $stats = [
            'total_comments' => Comment::count(),
            'approved_comments' => Comment::approved()->count(),
            'pending_comments' => Comment::pending()->count(),
            'parent_comments' => Comment::parent()->count(),
            'reply_comments' => Comment::whereNotNull('parent_id')->count(),
            'today_comments' => Comment::whereDate('created_at', today())->count(),
            'this_week_comments' => Comment::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month_comments' => Comment::whereMonth('created_at', now()->month)->count(),
        ];

        return response()->json($stats);
    }
}
