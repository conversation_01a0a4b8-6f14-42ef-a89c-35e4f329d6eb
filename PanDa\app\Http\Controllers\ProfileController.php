<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    /**
     * Hiển Thị Trang Profile
     */
    public function index()
    {
        $user = auth()->user();

        // Thống kê học tập
        $learningStats = [
            'total_courses' => $user->userCourses()->count(),
            'completed_courses' => $user->userCourses()->completed()->count(),
            'in_progress_courses' => $user->userCourses()->inProgress()->count(),
            'certificates' => $user->certificates()->count(),
            'total_progress' => round($user->userCourses()->avg('progress_percentage') ?? 0),
            'learning_streak' => $this->calculateLearningStreak($user),
        ];

        // Hoạt động gần đây
        $recentActivities = $user->userCourses()
            ->with(['course'])
            ->whereNotNull('last_accessed_at')
            ->orderBy('last_accessed_at', 'desc')
            ->limit(5)
            ->get();

        // Chứng chỉ gần đây
        $recentCertificates = $user->certificates()
            ->with(['course'])
            ->orderBy('issued_at', 'desc')
            ->limit(3)
            ->get();

        return view('user.profile.index', compact(
            'user',
            'learningStats',
            'recentActivities',
            'recentCertificates'
        ));
    }

    /**
     * Hiển Thị Form Chỉnh Sửa Profile
     */
    public function edit()
    {
        $user = auth()->user();
        return view('user.profile.edit', compact('user'));
    }

    /**
     * Cập Nhật Thông Tin Cá Nhân
     */
    public function update(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:500',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], [
            'name.required' => 'Tên Là Bắt Buộc',
            'email.required' => 'Email Là Bắt Buộc',
            'email.email' => 'Email Không Hợp Lệ',
            'email.unique' => 'Email Đã Được Sử Dụng',
            'avatar.image' => 'Avatar Phải Là Hình Ảnh',
            'avatar.max' => 'Avatar Không Được Quá 2MB',
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'bio' => $request->bio,
        ];

        // Xử lý upload avatar
        if ($request->hasFile('avatar')) {
            // Xóa avatar cũ
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Upload avatar mới
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $updateData['avatar'] = $avatarPath;
        }

        $user->update($updateData);

        return redirect()->route('user.profile.index')
            ->with('success', 'Cập Nhật Thông Tin Thành Công!');
    }

    /**
     * Thay Đổi Mật Khẩu
     */
    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', Password::min(8)],
        ], [
            'current_password.required' => 'Mật Khẩu Hiện Tại Là Bắt Buộc',
            'password.required' => 'Mật Khẩu Mới Là Bắt Buộc',
            'password.confirmed' => 'Xác Nhận Mật Khẩu Không Khớp',
            'password.min' => 'Mật Khẩu Phải Có Ít Nhất 8 Ký Tự',
        ]);

        $user = auth()->user();

        // Kiểm tra mật khẩu hiện tại
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'Mật Khẩu Hiện Tại Không Đúng']);
        }

        // Cập nhật mật khẩu mới
        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return back()->with('success', 'Thay Đổi Mật Khẩu Thành Công!');
    }

    /**
     * Xóa Avatar
     */
    public function deleteAvatar()
    {
        $user = auth()->user();

        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
            $user->update(['avatar' => null]);
        }

        return back()->with('success', 'Xóa Avatar Thành Công!');
    }

    /**
     * Xuất Dữ Liệu Cá Nhân
     */
    public function exportData()
    {
        $user = auth()->user();

        $data = [
            'user_info' => $user->only(['name', 'email', 'phone', 'bio', 'created_at']),
            'courses' => $user->userCourses()->with(['course'])->get(),
            'certificates' => $user->certificates()->with(['course'])->get(),
            'orders' => $user->orders()->with(['items.course'])->get(),
            'notifications' => $user->notifications()->get(),
        ];

        $fileName = 'user_data_' . $user->id . '_' . date('Y-m-d') . '.json';

        return response()->json($data)
            ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');
    }

    /**
     * Tính Chuỗi Ngày Học Liên Tiếp
     */
    private function calculateLearningStreak($user)
    {
        $recentActivity = $user->userCourses()
            ->whereNotNull('last_accessed_at')
            ->where('last_accessed_at', '>=', now()->subDays(30))
            ->orderBy('last_accessed_at', 'desc')
            ->get();

        $streak = 0;
        $currentDate = now()->startOfDay();

        foreach ($recentActivity as $activity) {
            $activityDate = $activity->last_accessed_at->startOfDay();

            if ($activityDate->eq($currentDate) || $activityDate->eq($currentDate->subDay())) {
                $streak++;
                $currentDate = $activityDate->subDay();
            } else {
                break;
            }
        }

        return $streak;
    }
}
