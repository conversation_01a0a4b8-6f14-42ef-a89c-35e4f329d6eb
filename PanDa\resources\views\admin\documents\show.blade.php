@extends('layouts.admin')

@section('title', 'Chi Tiết Tài Liệu - ' . $lesson->title)

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Chi Tiết Tài Liệu</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.documents.index') }}">Tài Liệu</a></li>
            <li class="breadcrumb-item active">{{ $lesson->title }}</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Thông Tin Bài Học -->
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="fa fa-file-alt me-2"></i>{{ $lesson->title }}
            </h5>
            <div>
              <span class="badge bg-primary">{{ $lesson->chapter->course->title }}</span>
            </div>
          </div>
        </div>
        
        <div class="card-body">
          <div class="row">
            <!-- Thông Tin Cơ Bản -->
            <div class="col-12 mb-4">
              <table class="table table-borderless">
                <tr>
                  <td width="20%"><strong>Bài Học:</strong></td>
                  <td>{{ $lesson->title }}</td>
                </tr>
                <tr>
                  <td><strong>Chương:</strong></td>
                  <td>{{ $lesson->chapter->title }}</td>
                </tr>
                <tr>
                  <td><strong>Khóa Học:</strong></td>
                  <td>{{ $lesson->chapter->course->title }}</td>
                </tr>
                <tr>
                  <td><strong>Loại Bài Học:</strong></td>
                  <td>
                    @if($lesson->type == 'video')
                    <span class="badge bg-info">Video</span>
                    @elseif($lesson->type == 'pdf')
                    <span class="badge bg-danger">PDF</span>
                    @else
                    <span class="badge bg-secondary">Khác</span>
                    @endif
                  </td>
                </tr>
                <tr>
                  <td><strong>Thứ Tự:</strong></td>
                  <td>{{ $lesson->position }}</td>
                </tr>
                <tr>
                  <td><strong>Ngày Tạo:</strong></td>
                  <td>{{ $lesson->created_at->format('d/m/Y H:i:s') }}</td>
                </tr>
                <tr>
                  <td><strong>Cập Nhật Cuối:</strong></td>
                  <td>{{ $lesson->updated_at->format('d/m/Y H:i:s') }}</td>
                </tr>
              </table>
            </div>

            <!-- Nội Dung Bài Học -->
            @if($lesson->content)
            <div class="col-12 mb-4">
              <h6><i class="fa fa-align-left me-2"></i>Nội Dung Bài Học</h6>
              <div class="border rounded p-3 bg-light">
                {!! nl2br(e($lesson->content)) !!}
              </div>
            </div>
            @endif
          </div>
        </div>
      </div>

      <!-- File PDF -->
      @if($lesson->type == 'pdf' && $lesson->file_path)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="fa fa-file-pdf me-2 text-danger"></i>File PDF
          </h6>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h6 class="mb-1">{{ $lesson->title }}.pdf</h6>
              @php
                $pdfPath = storage_path('app/public/' . $lesson->file_path);
                $fileSize = file_exists($pdfPath) ? filesize($pdfPath) : 0;
                $sizeFormatted = $fileSize > 0 ? number_format($fileSize / 1024 / 1024, 2) . ' MB' : 'N/A';
              @endphp
              <small class="text-muted">Dung lượng: {{ $sizeFormatted }}</small>
            </div>
            <div>
              <a href="{{ route('admin.documents.download', ['id' => $lesson->id, 'type' => 'pdf']) }}" 
                 class="btn btn-primary btn-sm me-2">
                <i class="fa fa-download me-1"></i>Tải Xuống
              </a>
              <form action="{{ route('admin.documents.delete-pdf', $lesson->id) }}" method="POST" class="d-inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger btn-sm" 
                        onclick="return confirm('Bạn có chắc muốn xóa file PDF này?')">
                  <i class="fa fa-trash me-1"></i>Xóa
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
      @endif

      <!-- File Bài Tập -->
      @if($lesson->exercise_files && count($lesson->exercise_files) > 0)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="fa fa-tasks me-2 text-success"></i>File Bài Tập ({{ count($lesson->exercise_files) }})
          </h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Tên File</th>
                  <th>Dung Lượng</th>
                  <th>Ngày Upload</th>
                  <th>Thao Tác</th>
                </tr>
              </thead>
              <tbody>
                @foreach($lesson->exercise_files as $index => $file)
                <tr>
                  <td>
                    <i class="fa fa-file me-2"></i>{{ $file['name'] }}
                  </td>
                  <td>
                    @php
                      $filePath = storage_path('app/public/' . $file['path']);
                      $fileSize = file_exists($filePath) ? filesize($filePath) : 0;
                      $sizeFormatted = $fileSize > 0 ? number_format($fileSize / 1024 / 1024, 2) . ' MB' : 'N/A';
                    @endphp
                    {{ $sizeFormatted }}
                  </td>
                  <td>
                    {{ isset($file['uploaded_at']) ? \Carbon\Carbon::parse($file['uploaded_at'])->format('d/m/Y H:i') : 'N/A' }}
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <a href="{{ route('admin.documents.download', ['id' => $lesson->id, 'type' => 'exercise', 'fileIndex' => $index]) }}" 
                         class="btn btn-outline-primary">
                        <i class="fa fa-download"></i>
                      </a>
                      <form action="{{ route('admin.documents.delete-exercise', ['id' => $lesson->id, 'fileIndex' => $index]) }}" 
                            method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger" 
                                onclick="return confirm('Bạn có chắc muốn xóa file này?')">
                          <i class="fa fa-trash"></i>
                        </button>
                      </form>
                    </div>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div>
      @endif

      <!-- Không Có Tài Liệu -->
      @if((!$lesson->file_path || $lesson->type != 'pdf') && (!$lesson->exercise_files || count($lesson->exercise_files) == 0))
      <div class="card mt-4">
        <div class="card-body text-center py-5">
          <i class="fa fa-file-alt fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">Bài Học Này Chưa Có Tài Liệu</h5>
          <p class="text-muted">Hãy thêm file PDF hoặc bài tập cho bài học này</p>
          <a href="{{ route('admin.lessons.edit', $lesson->id) }}" class="btn btn-primary">
            <i class="fa fa-edit me-2"></i>Chỉnh Sửa Bài Học
          </a>
        </div>
      </div>
      @endif
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
      <!-- Thống Kê -->
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-chart-bar me-2"></i>Thống Kê Tài Liệu
          </h6>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-6 mb-3">
              <div class="border rounded p-3">
                @php
                  $totalFiles = 0;
                  if($lesson->type == 'pdf' && $lesson->file_path) $totalFiles++;
                  if($lesson->exercise_files) $totalFiles += count($lesson->exercise_files);
                @endphp
                <h4 class="text-primary mb-1">{{ $totalFiles }}</h4>
                <small class="text-muted">Tổng File</small>
              </div>
            </div>
            <div class="col-6 mb-3">
              <div class="border rounded p-3">
                @php
                  $totalSize = 0;
                  if($lesson->type == 'pdf' && $lesson->file_path) {
                    $pdfPath = storage_path('app/public/' . $lesson->file_path);
                    if(file_exists($pdfPath)) $totalSize += filesize($pdfPath);
                  }
                  if($lesson->exercise_files) {
                    foreach($lesson->exercise_files as $file) {
                      $filePath = storage_path('app/public/' . $file['path']);
                      if(file_exists($filePath)) $totalSize += filesize($filePath);
                    }
                  }
                  $sizeFormatted = $totalSize > 0 ? number_format($totalSize / 1024 / 1024, 2) . ' MB' : '0 MB';
                @endphp
                <h4 class="text-success mb-1">{{ $sizeFormatted }}</h4>
                <small class="text-muted">Dung Lượng</small>
              </div>
            </div>
            <div class="col-6">
              <div class="border rounded p-3">
                <h4 class="text-warning mb-1">{{ $lesson->type == 'pdf' && $lesson->file_path ? 1 : 0 }}</h4>
                <small class="text-muted">File PDF</small>
              </div>
            </div>
            <div class="col-6">
              <div class="border rounded p-3">
                <h4 class="text-info mb-1">{{ $lesson->exercise_files ? count($lesson->exercise_files) : 0 }}</h4>
                <small class="text-muted">Bài Tập</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Thao Tác Nhanh -->
      <div class="card mt-3">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-bolt me-2"></i>Thao Tác Nhanh
          </h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('admin.lessons.edit', $lesson->id) }}" class="btn btn-primary">
              <i class="fa fa-edit me-2"></i>Chỉnh Sửa Bài Học
            </a>
            <a href="{{ route('admin.lessons.show', $lesson->id) }}" class="btn btn-info">
              <i class="fa fa-eye me-2"></i>Xem Chi Tiết Bài Học
            </a>
            <a href="{{ route('admin.courses.show', $lesson->chapter->course->id) }}" class="btn btn-success">
              <i class="fa fa-graduation-cap me-2"></i>Xem Khóa Học
            </a>
          </div>
        </div>
      </div>

      <!-- Thông Tin Khóa Học -->
      <div class="card mt-3">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-info-circle me-2"></i>Thông Tin Khóa Học
          </h6>
        </div>
        <div class="card-body">
          <table class="table table-borderless table-sm">
            <tr>
              <td><strong>Khóa Học:</strong></td>
              <td>{{ $lesson->chapter->course->title }}</td>
            </tr>
            <tr>
              <td><strong>Danh Mục:</strong></td>
              <td>{{ $lesson->chapter->course->category->name }}</td>
            </tr>
            <tr>
              <td><strong>Cấp Độ:</strong></td>
              <td>{{ ucfirst($lesson->chapter->course->level) }}</td>
            </tr>
            <tr>
              <td><strong>Trạng Thái:</strong></td>
              <td>
                @if($lesson->chapter->course->status == 'published')
                <span class="badge bg-success">Xuất Bản</span>
                @elseif($lesson->chapter->course->status == 'draft')
                <span class="badge bg-secondary">Nháp</span>
                @else
                <span class="badge bg-warning">Lưu Trữ</span>
                @endif
              </td>
            </tr>
            <tr>
              <td><strong>Tổng Bài Học:</strong></td>
              <td>{{ $lesson->chapter->course->chapters->sum(function($chapter) { return $chapter->lessons->count(); }) }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Back Button -->
  <div class="row mt-4">
    <div class="col-12">
      <a href="{{ route('admin.documents.index') }}" class="btn btn-secondary">
        <i class="fa fa-arrow-left me-2"></i>Quay Lại Danh Sách Tài Liệu
      </a>
    </div>
  </div>
</div>
@endsection
