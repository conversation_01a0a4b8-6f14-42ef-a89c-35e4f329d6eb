<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserCourse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    /**
     * Danh Sách Người Dùng
     */
    public function index(Request $request)
    {
        $query = User::with(['userCourses.course']);

        // Tìm kiếm
        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Lọc theo role
        if ($request->role) {
            $query->where('role', $request->role);
        }

        // Lọc theo trạng thái
        if ($request->status) {
            if ($request->status == 'active') {
                $query->whereNull('email_verified_at');
            } elseif ($request->status == 'verified') {
                $query->whereNotNull('email_verified_at');
            }
        }

        $users = $query->latest()->paginate(20);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Chi Tiết Người Dùng
     */
    public function show($id)
    {
        $user = User::with([
            'userCourses.course.category',
            'certificates.course',
            'reviews.course'
        ])->findOrFail($id);

        return view('admin.users.show', compact('user'));
    }

    /**
     * Chỉnh Sửa Người Dùng
     */
    public function edit($id)
    {
        $user = User::findOrFail($id);
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Cập Nhật Người Dùng
     */
    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'role' => 'required|in:user,admin',
            'password' => 'nullable|min:8|confirmed',
        ], [
            'name.required' => 'Tên Là Bắt Buộc',
            'email.required' => 'Email Là Bắt Buộc',
            'email.unique' => 'Email Đã Tồn Tại',
            'role.required' => 'Role Là Bắt Buộc',
            'password.min' => 'Mật Khẩu Tối Thiểu 8 Ký Tự',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->only(['name', 'email', 'role']);

        if ($request->password) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($data);

        return redirect()->route('admin.users.index')
            ->with('success', 'Cập Nhật Người Dùng Thành Công!');
    }

    /**
     * Xóa Người Dùng
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);

        // Không cho phép xóa admin
        if ($user->role === 'admin') {
            return back()->with('error', 'Không Thể Xóa Tài Khoản Admin!');
        }

        // Xóa các dữ liệu liên quan
        $user->userCourses()->delete();
        $user->certificates()->delete();
        $user->reviews()->delete();

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'Xóa Người Dùng Thành Công!');
    }

    /**
     * Khóa/Mở Khóa Tài Khoản
     */
    public function toggleStatus($id)
    {
        $user = User::findOrFail($id);

        if ($user->role === 'admin') {
            return back()->with('error', 'Không Thể Khóa Tài Khoản Admin!');
        }

        $user->update([
            'email_verified_at' => $user->email_verified_at ? null : now()
        ]);

        $status = $user->email_verified_at ? 'Kích Hoạt' : 'Khóa';

        return back()->with('success', "Đã {$status} Tài Khoản Thành Công!");
    }
}
