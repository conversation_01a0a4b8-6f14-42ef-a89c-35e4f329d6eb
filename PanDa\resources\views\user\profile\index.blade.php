@extends('layouts.user')

@section('title', '<PERSON><PERSON> Sơ Cá Nhân')

@section('content')
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0"><PERSON><PERSON>ơ Cá Nhân</h4>
        <p class="text-muted mb-0">Quản Lý <PERSON>hông Tin Và <PERSON></p>
      </div>
      <div>
        <a href="{{ route('profile.edit') }}" class="btn btn-primary">
          <i class="fa fa-edit me-2"></i>Chỉnh Sửa
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Thông Tin Cá Nhân -->
<div class="row mb-4">
  <div class="col-lg-4">
    <div class="card">
      <div class="card-body text-center">
        <div class="mb-3">
          @if($user->avatar)
          <img src="{{ asset('storage/' . $user->avatar) }}" alt="Avatar" class="rounded-circle" width="120" height="120" style="object-fit: cover;">
          @else
          <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 120px; height: 120px;">
            <i class="fa fa-user fa-3x text-white"></i>
          </div>
          @endif
        </div>
        
        <h5 class="mb-1">{{ $user->name }}</h5>
        <p class="text-muted mb-2">{{ $user->email }}</p>
        
        @if($user->phone)
        <p class="text-muted mb-2">
          <i class="fa fa-phone me-1"></i>{{ $user->phone }}
        </p>
        @endif
        
        @if($user->bio)
        <p class="text-muted small">{{ $user->bio }}</p>
        @endif
        
        <div class="mt-3">
          <small class="text-muted">
            <i class="fa fa-calendar me-1"></i>
            Tham Gia: {{ $user->created_at->format('d/m/Y') }}
          </small>
        </div>
      </div>
    </div>
  </div>
  
  <div class="col-lg-8">
    <!-- Thống Kê Học Tập -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card bg-primary text-white">
          <div class="card-body text-center">
            <i class="fa fa-graduation-cap fa-2x mb-2"></i>
            <h4 class="mb-0">{{ $learningStats['total_courses'] }}</h4>
            <small>Khóa Học</small>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-success text-white">
          <div class="card-body text-center">
            <i class="fa fa-check-circle fa-2x mb-2"></i>
            <h4 class="mb-0">{{ $learningStats['completed_courses'] }}</h4>
            <small>Hoàn Thành</small>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-warning text-white">
          <div class="card-body text-center">
            <i class="fa fa-clock fa-2x mb-2"></i>
            <h4 class="mb-0">{{ $learningStats['in_progress_courses'] }}</h4>
            <small>Đang Học</small>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-info text-white">
          <div class="card-body text-center">
            <i class="fa fa-certificate fa-2x mb-2"></i>
            <h4 class="mb-0">{{ $learningStats['certificates'] }}</h4>
            <small>Chứng Chỉ</small>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Tiến Độ Tổng Quan -->
    <div class="card mb-4">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-chart-line me-2"></i>Tiến Độ Học Tập
        </h6>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <div class="d-flex justify-content-between mb-1">
                <small>Tiến Độ Trung Bình</small>
                <small>{{ $learningStats['total_progress'] }}%</small>
              </div>
              <div class="progress" style="height: 10px;">
                <div class="progress-bar bg-success" style="width: {{ $learningStats['total_progress'] }}%"></div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="text-center">
              <h4 class="text-primary mb-0">{{ $learningStats['learning_streak'] }}</h4>
              <small class="text-muted">Ngày Học Liên Tiếp</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Hoạt Động Gần Đây -->
@if($recentActivities->count() > 0)
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fa fa-history me-2"></i>Hoạt Động Gần Đây
        </h6>
      </div>
      <div class="card-body">
        <div class="timeline">
          @foreach($recentActivities as $activity)
          <div class="timeline-item">
            <div class="timeline-marker bg-primary"></div>
            <div class="timeline-content">
              <h6 class="mb-1">{{ $activity->course->title }}</h6>
              <p class="text-muted mb-1">Tiến Độ: {{ $activity->progress_percentage }}%</p>
              <small class="text-muted">{{ $activity->last_accessed_at->diffForHumans() }}</small>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
</div>
@endif

<!-- Chứng Chỉ Gần Đây -->
@if($recentCertificates->count() > 0)
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i class="fa fa-certificate me-2"></i>Chứng Chỉ Gần Đây
          </h6>
          <a href="{{ route('user.certificates.index') }}" class="btn btn-outline-primary btn-sm">
            Xem Tất Cả
          </a>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          @foreach($recentCertificates as $certificate)
          <div class="col-md-4 mb-3">
            <div class="card border">
              <div class="card-body text-center">
                <i class="fa fa-certificate fa-3x text-warning mb-2"></i>
                <h6 class="mb-1">{{ Str::limit($certificate->course->title, 30) }}</h6>
                <small class="text-muted d-block mb-2">{{ $certificate->formatted_issue_date }}</small>
                <a href="{{ route('user.certificates.show', $certificate->id) }}" class="btn btn-outline-primary btn-sm">
                  <i class="fa fa-eye me-1"></i>Xem
                </a>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
</div>
@endif

<!-- Hành Động Nhanh -->
<div class="row">
  <div class="col-md-3">
    <div class="card text-center">
      <div class="card-body">
        <i class="fa fa-graduation-cap fa-2x text-primary mb-2"></i>
        <h6>Khóa Học Của Tôi</h6>
        <a href="{{ route('my-courses.index') }}" class="btn btn-outline-primary btn-sm">Xem</a>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-center">
      <div class="card-body">
        <i class="fa fa-file-text fa-2x text-info mb-2"></i>
        <h6>Tài Liệu</h6>
        <a href="{{ route('documents.index') }}" class="btn btn-outline-info btn-sm">Xem</a>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-center">
      <div class="card-body">
        <i class="fa fa-certificate fa-2x text-warning mb-2"></i>
        <h6>Chứng Chỉ</h6>
        <a href="{{ route('user.certificates.index') }}" class="btn btn-outline-warning btn-sm">Xem</a>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-center">
      <div class="card-body">
        <i class="fa fa-download fa-2x text-success mb-2"></i>
        <h6>Xuất Dữ Liệu</h6>
        <a href="{{ route('profile.export') }}" class="btn btn-outline-success btn-sm">Tải</a>
      </div>
    </div>
  </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-marker {
  position: absolute;
  left: -35px;
  top: 5px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: -31px;
  top: 15px;
  width: 2px;
  height: calc(100% + 5px);
  background-color: #e9ecef;
}

.timeline-content {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 3px solid #007bff;
}
</style>
@endpush
