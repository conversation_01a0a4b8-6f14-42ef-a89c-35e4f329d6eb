<?php

namespace App\Http\Controllers;

use App\Models\Wishlist;
use App\Models\Course;
use Illuminate\Http\Request;

class WishlistController extends Controller
{
    /**
     * Hi<PERSON>n Th<PERSON>ch Yêu <PERSON>h<PERSON>ch
     */
    public function index(Request $request)
    {
        $query = auth()->user()->wishlists()->with(['course.category']);

        // Lọc theo danh mục
        if ($request->filled('category')) {
            $query->whereHas('course', function ($courseQuery) use ($request) {
                $courseQuery->where('category_id', $request->category);
            });
        }

        // Lọc theo cấp độ
        if ($request->filled('level')) {
            $query->whereHas('course', function ($courseQuery) use ($request) {
                $courseQuery->where('level', $request->level);
            });
        }

        // Lọc theo giá
        if ($request->filled('price_filter')) {
            $query->whereHas('course', function ($courseQuery) use ($request) {
                if ($request->price_filter === 'free') {
                    $courseQuery->where('price', 0);
                } elseif ($request->price_filter === 'paid') {
                    $courseQuery->where('price', '>', 0);
                }
            });
        }

        // Sắp xếp
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'name':
                $query->join('courses', 'wishlists.course_id', '=', 'courses.id')
                     ->orderBy('courses.title');
                break;
            case 'price_low':
                $query->join('courses', 'wishlists.course_id', '=', 'courses.id')
                     ->orderBy('courses.price', 'asc');
                break;
            case 'price_high':
                $query->join('courses', 'wishlists.course_id', '=', 'courses.id')
                     ->orderBy('courses.price', 'desc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }

        $wishlists = $query->paginate(12);

        // Lấy categories để filter
        $categories = \App\Models\Category::where('is_active', true)->get();

        return view('user.wishlist.index', compact('wishlists', 'categories'));
    }

    /**
     * Thêm/Xóa Khóa Học Khỏi Wishlist (AJAX)
     */
    public function toggle(Request $request, $courseId)
    {
        $course = Course::findOrFail($courseId);

        // Kiểm tra user đã mua khóa học chưa
        if (auth()->user()->hasPurchasedCourse($courseId)) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn Đã Sở Hữu Khóa Học Này'
            ]);
        }

        $result = Wishlist::toggle(auth()->id(), $courseId);

        return response()->json([
            'success' => true,
            'action' => $result['action'],
            'message' => $result['message'],
            'in_wishlist' => $result['in_wishlist'],
            'wishlist_count' => auth()->user()->wishlists()->count()
        ]);
    }

    /**
     * Xóa Khóa Học Khỏi Wishlist
     */
    public function remove($courseId)
    {
        $wishlist = auth()->user()->wishlists()
            ->where('course_id', $courseId)
            ->first();

        if ($wishlist) {
            $wishlist->delete();
            return back()->with('success', 'Đã Xóa Khỏi Danh Sách Yêu Thích!');
        }

        return back()->with('error', 'Không Tìm Thấy Khóa Học Trong Danh Sách!');
    }

    /**
     * Thêm Tất Cả Vào Giỏ Hàng
     */
    public function addAllToCart()
    {
        $wishlists = auth()->user()->wishlists()->with('course')->get();
        $addedCount = 0;

        foreach ($wishlists as $wishlist) {
            // Kiểm tra chưa có trong giỏ hàng và chưa mua
            if (!auth()->user()->cartItems()->where('course_id', $wishlist->course_id)->exists() &&
                !auth()->user()->hasPurchasedCourse($wishlist->course_id)) {

                auth()->user()->cartItems()->create([
                    'course_id' => $wishlist->course_id,
                    'price' => $wishlist->course->price,
                ]);

                $addedCount++;
            }
        }

        if ($addedCount > 0) {
            return redirect()->route('cart.index')
                ->with('success', "Đã Thêm {$addedCount} Khóa Học Vào Giỏ Hàng!");
        }

        return back()->with('info', 'Không Có Khóa Học Nào Được Thêm Vào Giỏ Hàng');
    }

    /**
     * Xóa Tất Cả Wishlist
     */
    public function clear()
    {
        $count = auth()->user()->wishlists()->count();
        auth()->user()->wishlists()->delete();

        return back()->with('success', "Đã Xóa {$count} Khóa Học Khỏi Danh Sách Yêu Thích!");
    }

    /**
     * Lấy Số Lượng Wishlist (API)
     */
    public function getCount()
    {
        return response()->json([
            'count' => auth()->user()->wishlists()->count()
        ]);
    }

    /**
     * Kiểm Tra Course Có Trong Wishlist (API)
     */
    public function checkCourse($courseId)
    {
        $inWishlist = auth()->user()->hasInWishlist($courseId);

        return response()->json([
            'in_wishlist' => $inWishlist
        ]);
    }
}
