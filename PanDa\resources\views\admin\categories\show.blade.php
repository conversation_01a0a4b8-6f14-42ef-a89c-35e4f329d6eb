@extends('layouts.admin')

@section('title', '<PERSON> Tiết <PERSON> - ' . $category->name)

@section('content')
<div class="container-fluid">
  <!-- Breadcrumb -->
  <div class="row">
    <div class="col-12">
      <div class="page-title-box d-sm-flex align-items-center justify-content-between">
        <h4 class="mb-sm-0">Chi Tiết Danh M<PERSON>c</h4>
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.categories.index') }}">Danh <PERSON></a></li>
            <li class="breadcrumb-item active">{{ $category->name }}</li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Thông <PERSON> -->
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
              <i class="fa fa-folder me-2"></i>{{ $category->name }}
            </h5>
            <div>
              @if($category->is_active)
              <span class="badge bg-success">Kích Hoạt</span>
              @else
              <span class="badge bg-secondary">Tạm Dừng</span>
              @endif
            </div>
          </div>
        </div>
        
        <div class="card-body">
          <div class="row">
            <!-- Hình Ảnh -->
            <div class="col-md-4 text-center mb-3">
              @if($category->image_url)
              <img src="{{ $category->image_url }}" alt="{{ $category->name }}"
                   class="img-fluid rounded shadow" style="max-height: 200px;">
              <p class="text-muted small mt-1">URL: {{ $category->image_url }}</p>
              @else
              <div class="bg-light rounded p-4">
                <i class="fa fa-image fa-4x text-muted"></i>
                <p class="text-muted mt-2">Chưa Có Hình Ảnh</p>
                <p class="text-danger small">Debug: image_url = "{{ $category->image_url }}"</p>
              </div>
              @endif
            </div>

            <!-- Thông Tin -->
            <div class="col-md-8">
              <table class="table table-borderless">
                <tr>
                  <td width="30%"><strong>Tên Danh Mục:</strong></td>
                  <td>{{ $category->name }}</td>
                </tr>
                <tr>
                  <td><strong>Slug:</strong></td>
                  <td><code>{{ $category->slug }}</code></td>
                </tr>
                <tr>
                  <td><strong>Mô Tả:</strong></td>
                  <td>{{ $category->description ?: 'Chưa có mô tả' }}</td>
                </tr>
                <tr>
                  <td><strong>Màu Sắc:</strong></td>
                  <td>
                    @if($category->color)
                    <div class="d-flex align-items-center">
                      <div class="rounded me-2" style="width: 20px; height: 20px; background-color: {{ $category->color }};"></div>
                      <code>{{ $category->color }}</code>
                    </div>
                    @else
                    <span class="text-muted">Chưa đặt</span>
                    @endif
                  </td>
                </tr>
                <tr>
                  <td><strong>Thứ Tự:</strong></td>
                  <td>{{ $category->sort_order ?? 0 }}</td>
                </tr>
                <tr>
                  <td><strong>Số Khóa Học:</strong></td>
                  <td>
                    <span class="badge bg-primary">{{ $category->courses->count() }} Khóa Học</span>
                  </td>
                </tr>
                <tr>
                  <td><strong>Ngày Tạo:</strong></td>
                  <td>{{ $category->created_at->format('d/m/Y H:i:s') }}</td>
                </tr>
                <tr>
                  <td><strong>Cập Nhật Cuối:</strong></td>
                  <td>{{ $category->updated_at->format('d/m/Y H:i:s') }}</td>
                </tr>
              </table>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <div class="d-flex justify-content-between">
            <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
              <i class="fa fa-arrow-left me-2"></i>Quay Lại Danh Sách
            </a>
            <div>
              <a href="{{ route('admin.categories.edit', $category->id) }}" class="btn btn-warning">
                <i class="fa fa-edit me-2"></i>Chỉnh Sửa
              </a>
              <form action="{{ route('admin.categories.destroy', $category->id) }}" method="POST" class="d-inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger" 
                        onclick="return confirm('Bạn có chắc muốn xóa danh mục này? Tất cả khóa học trong danh mục sẽ bị ảnh hưởng.')">
                  <i class="fa fa-trash me-2"></i>Xóa
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>

      <!-- Danh Sách Khóa Học -->
      @if($category->courses->count() > 0)
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-graduation-cap me-2"></i>Khóa Học Trong Danh Mục ({{ $category->courses->count() }})
          </h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Hình Ảnh</th>
                  <th>Tên Khóa Học</th>
                  <th>Giá</th>
                  <th>Cấp Độ</th>
                  <th>Trạng Thái</th>
                  <th>Ngày Tạo</th>
                  <th>Thao Tác</th>
                </tr>
              </thead>
              <tbody>
                @foreach($category->courses as $course)
                <tr>
                  <td>
                    @if($course->image_url)
                    <img src="{{ $course->image_url }}" alt="{{ $course->title }}"
                         class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                    @else
                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                         style="width: 50px; height: 50px;">
                      <i class="fa fa-image text-muted"></i>
                    </div>
                    @endif
                  </td>
                  <td>
                    <strong>{{ $course->title }}</strong><br>
                    <small class="text-muted">{{ Str::limit($course->description, 50) }}</small>
                  </td>
                  <td>
                    @if($course->price > 0)
                    <span class="fw-bold text-success">{{ number_format($course->price, 0, ',', '.') }}đ</span>
                    @else
                    <span class="badge bg-info">Miễn Phí</span>
                    @endif
                  </td>
                  <td>
                    @if($course->level == 'beginner')
                    <span class="badge bg-success">Người Mới</span>
                    @elseif($course->level == 'intermediate')
                    <span class="badge bg-warning">Trung Cấp</span>
                    @else
                    <span class="badge bg-danger">Nâng Cao</span>
                    @endif
                  </td>
                  <td>
                    @if($course->status == 'published')
                    <span class="badge bg-success">Xuất Bản</span>
                    @elseif($course->status == 'draft')
                    <span class="badge bg-secondary">Nháp</span>
                    @else
                    <span class="badge bg-warning">Lưu Trữ</span>
                    @endif
                  </td>
                  <td>{{ $course->created_at->format('d/m/Y') }}</td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <a href="{{ route('admin.courses.show', $course->id) }}" class="btn btn-outline-primary">
                        <i class="fa fa-eye"></i>
                      </a>
                      <a href="{{ route('admin.courses.edit', $course->id) }}" class="btn btn-outline-warning">
                        <i class="fa fa-edit"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                @endforeach
              </tbody>
            </table>
          </div>
        </div>
      </div>
      @endif
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
      <!-- Thống Kê -->
      <div class="card">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-chart-bar me-2"></i>Thống Kê Danh Mục
          </h6>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-6 mb-3">
              <div class="border rounded p-3">
                <h4 class="text-primary mb-1">{{ $category->courses->count() }}</h4>
                <small class="text-muted">Tổng Khóa Học</small>
              </div>
            </div>
            <div class="col-6 mb-3">
              <div class="border rounded p-3">
                <h4 class="text-success mb-1">{{ $category->courses->where('status', 'published')->count() }}</h4>
                <small class="text-muted">Đã Xuất Bản</small>
              </div>
            </div>
            <div class="col-6">
              <div class="border rounded p-3">
                <h4 class="text-warning mb-1">{{ $category->courses->where('status', 'draft')->count() }}</h4>
                <small class="text-muted">Nháp</small>
              </div>
            </div>
            <div class="col-6">
              <div class="border rounded p-3">
                <h4 class="text-info mb-1">{{ $category->courses->where('price', 0)->count() }}</h4>
                <small class="text-muted">Miễn Phí</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Thao Tác Nhanh -->
      <div class="card mt-3">
        <div class="card-header">
          <h6 class="card-title mb-0">
            <i class="fa fa-bolt me-2"></i>Thao Tác Nhanh
          </h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="{{ route('admin.courses.create') }}?category_id={{ $category->id }}" class="btn btn-primary">
              <i class="fa fa-plus me-2"></i>Thêm Khóa Học Mới
            </a>
            <a href="{{ route('admin.categories.edit', $category->id) }}" class="btn btn-warning">
              <i class="fa fa-edit me-2"></i>Chỉnh Sửa Danh Mục
            </a>
            @if($category->is_active)
            <form action="{{ route('admin.categories.update', $category->id) }}" method="POST">
              @csrf
              @method('PUT')
              <input type="hidden" name="is_active" value="0">
              <button type="submit" class="btn btn-secondary w-100">
                <i class="fa fa-pause me-2"></i>Tạm Dừng Danh Mục
              </button>
            </form>
            @else
            <form action="{{ route('admin.categories.update', $category->id) }}" method="POST">
              @csrf
              @method('PUT')
              <input type="hidden" name="is_active" value="1">
              <button type="submit" class="btn btn-success w-100">
                <i class="fa fa-play me-2"></i>Kích Hoạt Danh Mục
              </button>
            </form>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
