<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use Illuminate\Http\Request;

class DocumentController extends Controller
{
    /**
     * <PERSON>h S<PERSON>ch Tài <PERSON>u
     */
    public function index(Request $request)
    {
        $query = Lesson::with(['chapter.course'])
            ->where(function($q) {
                $q->where('type', 'pdf')
                  ->whereNotNull('file_path')
                  ->orWhere(function($q2) {
                      $q2->whereNotNull('exercise_files')
                         ->where('exercise_files', '!=', '[]');
                  });
            });

        // Tìm kiếm
        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('content', 'like', '%' . $request->search . '%')
                  ->orWhereHas('chapter.course', function($q2) use ($request) {
                      $q2->where('title', 'like', '%' . $request->search . '%');
                  });
            });
        }

        // Lọc theo loại
        if ($request->type) {
            if ($request->type == 'pdf') {
                $query->where('type', 'pdf')->whereNotNull('file_path');
            } elseif ($request->type == 'exercise') {
                $query->whereNotNull('exercise_files')
                      ->where('exercise_files', '!=', '[]');
            }
        }

        // Lọc theo khóa học
        if ($request->course_id) {
            $query->whereHas('chapter', function($q) use ($request) {
                $q->where('course_id', $request->course_id);
            });
        }

        $documents = $query->latest()->paginate(20);
        $courses = \App\Models\Course::orderBy('title')->get();

        return view('admin.documents.index', compact('documents', 'courses'));
    }

    /**
     * Chi Tiết Tài Liệu
     */
    public function show($id)
    {
        $lesson = Lesson::with(['chapter.course'])->findOrFail($id);

        return view('admin.documents.show', compact('lesson'));
    }

    /**
     * Xóa Tài Liệu PDF
     */
    public function deletePdf($id)
    {
        $lesson = Lesson::findOrFail($id);

        if ($lesson->file_path && file_exists(storage_path('app/public/' . $lesson->file_path))) {
            unlink(storage_path('app/public/' . $lesson->file_path));
        }

        $lesson->update(['file_path' => null]);

        return back()->with('success', 'Đã Xóa File PDF Thành Công!');
    }

    /**
     * Xóa File Bài Tập
     */
    public function deleteExerciseFile($id, $fileIndex)
    {
        $lesson = Lesson::findOrFail($id);
        $exerciseFiles = $lesson->exercise_files ?? [];

        if (isset($exerciseFiles[$fileIndex])) {
            $file = $exerciseFiles[$fileIndex];

            // Xóa file vật lý
            if (file_exists(storage_path('app/public/' . $file['path']))) {
                unlink(storage_path('app/public/' . $file['path']));
            }

            // Xóa khỏi array
            unset($exerciseFiles[$fileIndex]);
            $exerciseFiles = array_values($exerciseFiles); // Reindex array

            $lesson->update(['exercise_files' => $exerciseFiles]);
        }

        return back()->with('success', 'Đã Xóa File Bài Tập Thành Công!');
    }

    /**
     * Download File
     */
    public function download($id, $type, $fileIndex = null)
    {
        $lesson = Lesson::findOrFail($id);

        if ($type == 'pdf' && $lesson->file_path) {
            $filePath = storage_path('app/public/' . $lesson->file_path);
            if (file_exists($filePath)) {
                return response()->download($filePath, $lesson->title . '.pdf');
            }
        }

        if ($type == 'exercise' && $fileIndex !== null) {
            $exerciseFiles = $lesson->exercise_files ?? [];
            if (isset($exerciseFiles[$fileIndex])) {
                $file = $exerciseFiles[$fileIndex];
                $filePath = storage_path('app/public/' . $file['path']);
                if (file_exists($filePath)) {
                    return response()->download($filePath, $file['name']);
                }
            }
        }

        abort(404, 'File không tồn tại');
    }

    /**
     * Thống Kê Tài Liệu
     */
    public function stats()
    {
        $stats = [
            'total_documents' => Lesson::where(function($q) {
                $q->where('type', 'pdf')->whereNotNull('file_path')
                  ->orWhere(function($q2) {
                      $q2->whereNotNull('exercise_files')
                         ->where('exercise_files', '!=', '[]');
                  });
            })->count(),

            'pdf_documents' => Lesson::where('type', 'pdf')
                ->whereNotNull('file_path')->count(),

            'exercise_files' => Lesson::whereNotNull('exercise_files')
                ->where('exercise_files', '!=', '[]')->count(),

            'total_size' => $this->calculateTotalSize(),
        ];

        return response()->json($stats);
    }

    /**
     * Tính Tổng Dung Lượng
     */
    private function calculateTotalSize()
    {
        $totalSize = 0;

        // PDF files
        $pdfLessons = Lesson::where('type', 'pdf')
            ->whereNotNull('file_path')->get();

        foreach ($pdfLessons as $lesson) {
            $filePath = storage_path('app/public/' . $lesson->file_path);
            if (file_exists($filePath)) {
                $totalSize += filesize($filePath);
            }
        }

        // Exercise files
        $exerciseLessons = Lesson::whereNotNull('exercise_files')
            ->where('exercise_files', '!=', '[]')->get();

        foreach ($exerciseLessons as $lesson) {
            foreach ($lesson->exercise_files as $file) {
                $filePath = storage_path('app/public/' . $file['path']);
                if (file_exists($filePath)) {
                    $totalSize += filesize($filePath);
                }
            }
        }

        return $this->formatBytes($totalSize);
    }

    /**
     * Format Bytes
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
